import CandidateCard from '@shared/components/molecules/CandidateCard';
import Flex from '@shared/uikit/Flex';
import { getSimilarCandidates } from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import { useCallback, type FC } from 'react';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Button from '@shared/uikit/Button';
import SendMessageButton from '@shared/components/molecules/SendMessageButton';
import useCandidateFilters from '@shared/hooks/useCandidateFilters';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import { IsManualWrapper } from '@shared/components/molecules/IsManualWrapper';
import SimilarCandidatesSkeleton from './CandidateSimilar.skeleton';
import type { BaseCandidateSectionProp } from '../types';

export const CandidateSimilarTab: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();
  const { similarEntityId } = useCandidateFilters();
  const appDispatch = useGlobalDispatch();

  const { data: similarUsers, isLoading } = useReactInfiniteQuery<any>(
    [QueryKeys.getSimilarCandidates, candidate?.id],
    {
      func: getSimilarCandidates,
      size: 10,
      extraProps: {
        id: similarEntityId || candidate?.id,
      },
    }
  );

  const handleOpenManagerModal = useCallback(
    (candidateId: string, tab: CandidateManagerTabkeys) => {
      if (candidateId) {
        appDispatch({
          type: 'TOGGLE_CANDIDATE_MANAGER',
          payload: {
            isOpen: true,
            tab,
            id: candidateId,
            enableNavigate: false,
          },
        });
      }
    },
    [appDispatch]
  );

  return isLoading ? (
    <SimilarCandidatesSkeleton />
  ) : similarUsers.length > 0 ? (
    <Flex className="mt-32 gap-20">
      {similarUsers.map((user) => (
        <CandidateCard
          key={user.id}
          enableLinks
          secondText={user?.usernameAtSign}
          thirdText={user?.occupation?.label}
          fourthText={cleanRepeatedWords(user?.location?.title || '')}
          FirstTextWrapper={
            !candidate.profile.username ? IsManualWrapper : undefined
          }
          onBadgeClick={(tab) => handleOpenManagerModal(user.id, tab)}
          showBadges
          showTags
          showActions
          firstText={user.fullName}
          avatar={user.croppedImageUrl}
        >
          <Flex className="!flex-row gap-12">
            <SendMessageButton
              className="flex-1"
              disabled={!user?.username}
              object={{
                id: user.originalId,
                croppedImageUrl: user.croppedImageUrl,
                fullName: user.fullName,
                username: user.username,
                isPage: false,
              }}
              fullWidth
            />
            <Button
              className="flex-1"
              label={t('manage')}
              leftIcon="user-cog"
              fullWidth
              onClick={() => handleOpenManagerModal(user.id, 'notes')}
            />
          </Flex>
        </CandidateCard>
      ))}
    </Flex>
  ) : (
    <Flex className="mt-32 gap-20 !flex-1">
      <EmptySearchResult
        title={t('no_similar_found')}
        sectionMessage={t('no_similar_candidate_found')}
        className="!py-32 px-0 !m-0"
      />
    </Flex>
  );
};
