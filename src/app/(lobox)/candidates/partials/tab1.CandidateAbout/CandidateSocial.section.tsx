import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { formatPhone } from '@shared/utils/toolkit/formatPhone';
import { useMemo, type FC } from 'react';
import isEmpty from '@shared/utils/toolkit/isEmpty';
import type { InfoCardListItem } from '../components/InfoCardsList/InfoCardList';
import InfoCardList from '../components/InfoCardsList/InfoCardList';
import type { BaseCandidateSectionProp } from '../types';

const CandidateSocialSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();
  const items = useMemo<InfoCardListItem[]>(
    () => [
      {
        key: 'linkedin_profile_url',
        icon: 'linkedin-line',
        title: t('linkedin_profile_url'),
        value: candidate.linkedinUrl,
      },
      {
        key: 'facebook_profile_url',
        icon: 'facebook-line',
        title: t('facebook_profile_url'),
        value: candidate.facebookUrl,
      },
      {
        key: 'twitter_profile_url',
        icon: 'x-twitter-line',
        title: t('twitter_profile_url'),
        value: candidate.twitterUrl,
      },
      {
        key: 'other_profile_url',
        icon: 'link',
        title: t('other_profile_url'),
        value: candidate.otherUrls?.filter((i) => !isEmpty(i)).join(', ') || '',
      },
      {
        key: 'skype_id',
        icon: 'skype-line',
        title: t('skype_id'),
        value: candidate.skypeId,
      },
      {
        key: 'cell_number',
        icon: 'phone',
        title: t('cell_number'),
        value: formatPhone(candidate.cellNumber),
      },
      {
        key: 'work_number',
        icon: 'phone',
        title: t('work_number'),
        value: formatPhone(candidate.workNumber),
      },
      {
        key: 'home_number',
        icon: 'phone',
        title: t('home_number'),
        value: formatPhone(candidate.homeNumber),
      },
    ],
    [candidate, t]
  );

  const visibleItems = useMemo(
    () => items.filter((item) => !isEmpty(item.value)),
    [items]
  );

  if (!visibleItems.length) return null;

  return (
    <SectionLayout title={t('social_information')} visibleActionButton>
      <InfoCardList items={visibleItems} />
    </SectionLayout>
  );
};

export default CandidateSocialSection;
