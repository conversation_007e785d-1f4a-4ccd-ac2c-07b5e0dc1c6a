import ProgressItem from '@shared/uikit/ProgressItem';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { FC } from 'react';
import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import type { BaseCandidateSectionProp } from '../types';

const CandidateLanguagesSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();

  if (!candidate._languages?.length) return null;

  return (
    <AboutSectionLayout data={candidate._languages} title={t('languages')}>
      {({ data }) =>
        data.map((item, index) => (
          <ProgressItem
            key={item?.id}
            image={item.image}
            title={item?.name}
            progressValue={item?.progress}
            progressSteps={7}
            tooltipText={t(item?.level)}
            badgeText={item.recruiterData ? t('recruiter_data') : undefined}
            styles={index !== 0 ? { root: 'responsive-margin-top' } : undefined}
          />
        ))
      }
    </AboutSectionLayout>
  );
};

export default CandidateLanguagesSection;
