import AboutSectionLayout from '@shared/components/Organism/AboutSectionLayout/AboutSectionLayout.component';
import ProgressItem from '@shared/uikit/ProgressItem';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { FC } from 'react';
import type { BaseCandidateSectionProp } from '../types';

const CandidateSkillsSection: FC<BaseCandidateSectionProp> = ({
  candidate,
}) => {
  const { t } = useTranslation();

  if (!candidate._skills.length) return null;

  return (
    <AboutSectionLayout data={candidate._skills} title={t('skills')}>
      {({ data }) =>
        data.map((item, index) => (
          <ProgressItem
            key={`${item.id}_${item.name}`}
            title={item.name}
            progressValue={item.progress}
            tooltipText={t(item?.level)}
            progressSteps={4}
            badgeText={item.recruiterData ? t('recruiter_data') : undefined}
            styles={index !== 0 ? { root: 'responsive-margin-top' } : undefined}
          />
        ))
      }
    </AboutSectionLayout>
  );
};

export default CandidateSkillsSection;
