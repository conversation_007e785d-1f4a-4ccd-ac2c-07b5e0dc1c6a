import type { FC } from 'react';
import { useState } from 'react';
import Flex from 'shared/uikit/Flex';
import Button from 'shared/uikit/Button';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import useTranslation from 'shared/utils/hooks/useTranslation';
import ValidationFormButton from '@shared/components/atoms/ValidationFormButton';
import type { MultiStepFormFooterProps } from '@shared/types/formTypes';
import Confirmation from '@shared/uikit/Confirmation/Confirmation';
import Typography from '@shared/uikit/Typography';
import Form from '@shared/uikit/Form';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { getJobsAllCandidateIds } from '@shared/utils/api/jobs';
import type { JobAPIProps } from '@shared/types/jobsProps';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import { QueryKeys } from '@shared/utils/constants';

const BatchActionsPipelineFooter: FC<
  MultiStepFormFooterProps & {
    setFieldValue?: (field: string, value: any) => void;
    onClose?: VoidFunction;
    onFinish?: VoidFunction;
  }
> = (props) => {
  const { setStep, step, values, setFieldValue, onClose, onFinish } = props;

  const { t } = useTranslation();
  const [openAlert, setOpenAlert] = useState(false);
  const queryClient = useQueryClient();

  const { mutate: getCandidates } = useMutation({
    mutationFn: getJobsAllCandidateIds,
    onSuccess: (data: { content: { candidateIds: string[] }[] }) => {
      const uniqueCandidateIds = Array.from(
        new Set(data.content.flatMap((job) => job.candidateIds))
      ).map((id) => ({ id: String(id) }));
      const jobIds = values.jobs.map((job: JobAPIProps) => job.id);
      openMultiStepForm({
        formName: 'linkJobForm',
        data: {
          id: jobIds,
          target: 'job',
          initialJobs: uniqueCandidateIds,
        },
        options: {
          handleRefetch: () => {
            queryClient.invalidateQueries({
              queryKey: [QueryKeys.getPipelinesList],
              exact: false,
            });
          },
        },
      });
      onClose?.();
      onFinish?.();
    },
  });

  const handleClickNext = () => {
    if (step === 0) {
      if (
        values.action.value === 'status' ||
        values.action.value === 'priority'
      ) {
        setOpenAlert(true);
        return;
      }
      if (values.action.value === 'link') {
        getCandidates({
          jobIds: values.jobs.map((job: JobAPIProps) => job.id),
        });
        return;
      }
    }
    setStep((prev) => prev + 1);
  };

  const onSuccessHandler = ({ actionValue }: { actionValue: string }) => {
    setFieldValue?.('value', actionValue);
    setStep((prev) => prev + 1);
    setOpenAlert(false);
  };

  return (
    <>
      <Flex className="!flex-row gap-8">
        <Button
          fullWidth
          label={t('discard')}
          schema="gray-semi-transparent"
          onClick={onClose}
        />
        {(values.action.value === 'projects' && step === 2) ||
        (values.action.value !== 'projects' && step === 1) ? (
          <SubmitButton fullWidth label={t('confirm')} schema="primary-blue" />
        ) : (
          <ValidationFormButton
            label={t('next')}
            onValidate={handleClickNext}
          />
        )}
      </Flex>

      {openAlert && (
        <Confirmation
          title={t(alertTitle[values.action.value as keyof typeof alertTitle])}
          onBackDropClick={() => setOpenAlert(false)}
          variant="wideRightSideModal"
          styles={{ wrapper: '!z-[1052]', backdrop: '!z-[1051]' }}
        >
          <Typography>
            {t(alertMessage[values.action.value as keyof typeof alertMessage])}
          </Typography>
          <Form
            initialValues={{ actionValue: '' }}
            onSuccess={onSuccessHandler}
            submitWithEnter
            local
          >
            {() => (
              <Flex className="!flex-col gap-20">
                <DynamicFormBuilder
                  groups={[
                    {
                      name: 'actionValue',
                      cp: 'dropdownSelect',
                      label: t(
                        alertValue[
                          values.action.value as keyof typeof alertValue
                        ]
                      ),
                      options: alertOptions[
                        values.action.value as keyof typeof alertOptions
                      ].map((option) => ({
                        label: t(option.label),
                        value: option.value,
                      })),
                      required: true,
                      className: 'z-[9002]',
                    },
                  ]}
                />
                <Flex className="!flex-row gap-20">
                  <Button
                    label={t('cancel')}
                    schema="ghost"
                    onClick={() => setOpenAlert(false)}
                    fullWidth
                  />
                  <SubmitButton label={t('next')} fullWidth />
                </Flex>
              </Flex>
            )}
          </Form>
        </Confirmation>
      )}
    </>
  );
};

export default BatchActionsPipelineFooter;

const alertTitle = {
  status: 'change_status',
  priority: 'change_priority',
};

const alertMessage = {
  status: 'batch_status_alert_message',
  priority: 'batch_priority_alert_message',
};

const alertValue = {
  status: 'status',
  priority: 'priority',
};

const alertOptions = {
  status: [
    { label: 'open', value: 'OPEN' },
    { label: 'closed', value: 'CLOSED' },
    { label: 'archive', value: 'ARCHIVE' },
  ],
  priority: [
    { label: 'low', value: 'LOW' },
    { label: 'medium', value: 'MEDIUM' },
    { label: 'high', value: 'HIGH' },
    { label: 'critical', value: 'CRITICAL' },
  ],
};
