import React from 'react';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import CardBadge from '@shared/components/molecules/CardBadge/CardBadge';
import type { JobAPIProps, JobStatusType } from '@shared/types/jobsProps';
import type { ProjectAPIProps } from '@shared/types/projectsProps';
import type { colorsKeys } from '@shared/uikit/utils/makeStyle';
import { priorityIconDetails } from '@shared/constants/priorityDetails';
import type { PriorityType } from '@shared/types/pipelineTypes';

export const statusColor: Record<JobStatusType, colorsKeys> = {
  OPEN: 'success',
  CLOSED: 'error',
  ARCHIVE: 'secondaryDisabledText',
  UNPUBLISHED: 'error',
};

interface StatusVariantProps {
  job: JobAPIProps;
  value: {
    value: JobStatusType;
    label: string;
  };
  t: (key: string) => string;
}

interface PriorityVariantProps {
  job: JobAPIProps;
  value: {
    value: PriorityType;
    label: string;
  };
  t: (key: string) => string;
}

interface ProjectsVariantProps {
  job: JobAPIProps;
  projects: ProjectAPIProps[];
  t: (key: string) => string;
}

export const StatusVariant: React.FC<StatusVariantProps> = ({
  job,
  value,
  t,
}) => (
  <>
    <CardBadge
      value={t(job.status.toLowerCase())}
      iconsDetails={{
        iconSize: 12,
        iconColor: statusColor[job.status],
        iconName: 'circle-s',
      }}
      classNames={{ badge: 'h-32 !px-16' }}
    />
    <Flex className="p-6">
      <Icon name="long-arrow-right-s" type="far" />
    </Flex>
    <CardBadge
      value={t(value.value.toLowerCase())}
      iconsDetails={{
        iconSize: 12,
        iconColor: statusColor[value.value],
        iconName: 'circle-s',
      }}
      classNames={{ badge: 'h-32 !px-16' }}
    />
  </>
);

export const PriorityVariant: React.FC<PriorityVariantProps> = ({
  job,
  value,
  t,
}) => (
  <>
    <CardBadge
      value={t(job.priority.toLowerCase())}
      iconsDetails={{
        iconSize: 12,
        iconColor: priorityIconDetails[job.priority].color,
        iconName: priorityIconDetails[job.priority].name,
      }}
      classNames={{ badge: 'h-32 !px-16' }}
    />
    <Flex className="p-6">
      <Icon name="long-arrow-right-s" type="far" />
    </Flex>
    <CardBadge
      value={t(value.value.toLowerCase())}
      iconsDetails={{
        iconSize: 12,
        iconColor: priorityIconDetails[value.value].color,
        iconName: priorityIconDetails[value.value].name,
      }}
      classNames={{ badge: 'h-32 !px-16' }}
    />
  </>
);

export const ProjectsVariant: React.FC<ProjectsVariantProps> = ({
  job,
  projects,
  t,
}) => (
  <>
    <CardBadge
      value={job.projects.map((p) => p.title).join(', ')}
      iconsDetails={{
        iconSize: 12,
        iconName: 'projects-light',
      }}
      classNames={{ badge: 'h-32 !px-16' }}
      tooltipProps={{
        children: job.projects.map((p) => p.title).join(', '),
      }}
    />
    <Flex className="p-6">
      <Icon name="long-arrow-right-s" type="far" />
    </Flex>
    <CardBadge
      value={projects.map((p) => p.title).join(', ')}
      iconsDetails={{
        iconSize: 12,
        iconName: 'projects-light',
      }}
      classNames={{ badge: 'h-32 !px-16' }}
      tooltipProps={{
        children: projects.map((p) => p.title).join(', '),
      }}
    />
  </>
);
