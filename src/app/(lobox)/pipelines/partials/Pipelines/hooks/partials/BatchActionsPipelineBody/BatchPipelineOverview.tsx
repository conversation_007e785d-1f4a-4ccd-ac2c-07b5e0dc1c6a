import React from 'react';
import { useFormikContext } from 'formik';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { JobAPIProps, JobStatusType } from '@shared/types/jobsProps';
import Flex from '@shared/uikit/Flex';
import LinkJobModalJobItem from '@shared/components/Organism/MultiStepForm/LinkJobForm/LinkJobModal/LinkJobModalBody/LinkJobModalJobsList/LinkJobModalJobItem';
import Info from '@shared/components/molecules/Info/Info';
import FormGroupHeader from '@shared/uikit/Form/FormGroupHeader';
import Typography from '@shared/uikit/Typography';
import type { ProjectAPIProps } from '@shared/types/projectsProps';
import type { PriorityType } from '@shared/types/pipelineTypes';
import {
  StatusVariant,
  PriorityVariant,
  ProjectsVariant,
} from './ActionVariants';

interface FormValues {
  jobs: JobAPIProps[];
  action: {
    value: string;
    label: string;
  };
  value: {
    value: JobStatusType | PriorityType;
    label: string;
  };
  projects: ProjectAPIProps[];
}

const subtitleText: Record<string, string> = {
  status: 'changing_status',
  priority: 'changing_priority',
  projects: 'changing_projects',
};

const BatchPipelineOverview: React.FC = () => {
  const { t } = useTranslation();
  const { values } = useFormikContext<FormValues>();

  const renderActionVariant = (job: JobAPIProps) => {
    switch (values.action.value) {
      case 'status':
        return (
          <StatusVariant
            job={job}
            value={{
              ...values.value,
              value: values.value.value as JobStatusType,
            }}
            t={t}
          />
        );
      case 'priority':
        return (
          <PriorityVariant
            job={job}
            value={{
              ...values.value,
              value: values.value.value as PriorityType,
            }}
            t={t}
          />
        );
      case 'projects':
        return <ProjectsVariant job={job} projects={values.projects} t={t} />;
      default:
        return null;
    }
  };

  return (
    <Flex className="flex-col gap-20">
      <Info
        text={t('batch_action_alert')}
        icon="exclamation-triangle"
        color="pendingOrange"
        className="!bg-pendingOrange_10"
        textColor="smoke_coal"
        classNames={{ text: 'text-center' }}
      />
      <Flex>
        <FormGroupHeader title={t('actions')} formSection className="!mb-4" />
        <Flex className="flex-col gap-8">
          {values.jobs.map((job: JobAPIProps) => (
            <Flex key={job.id}>
              <LinkJobModalJobItem
                job={job}
                action={<div />}
                subtitle={
                  <Typography
                    color="secondaryDisabledText"
                    className="!mt-auto"
                  >
                    {t(subtitleText[values.action.value])}
                  </Typography>
                }
              />
              <Flex className="!flex-row items-center pl-[52px]">
                {renderActionVariant(job)}
              </Flex>
            </Flex>
          ))}
        </Flex>
      </Flex>
    </Flex>
  );
};

export default BatchPipelineOverview;
