import { useCallback, useMemo } from 'react';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import useTranslation from 'shared/utils/hooks/useTranslation';
import BatchActionsPipelineFooter from './partials/BatchActionsPipelineFooter';
import { BatchActionsPipelineBody } from './partials/BatchActionsPipelineBody';

export function useBatchActionsPipelines({
  onClose,
  onFinish,
}: {
  onClose: VoidFunction;
  onFinish: VoidFunction;
}): MultiStepFormStepProps[] {
  const { t } = useTranslation();
  const getHeaderProps = useMemo<MultiStepFormStepProps['getHeaderProps']>(
    () =>
      ({ step, setStep, values }) => ({
        title: t(
          step === 0
            ? headerTitleData[step].title
            : (headerTitleData[1].title as any)?.[values.action.value]
        ),
        helper: step === 0 ? t('bulk_job_action_helper') : undefined,
        hideBack: step === 0,
        backButtonProps: {
          onClick: () => {
            setStep((prev) => prev - 1);
          },
        },
        noCloseButton: step !== 0,
      }),
    [t]
  );

  const getStepHeaderProps = useCallback<
    MultiStepFormStepProps['getStepHeaderProps']
  >(() => ({}), []);

  const renderBody = useCallback<MultiStepFormStepProps['renderBody']>(
    (props) => <BatchActionsPipelineBody {...props} />,
    []
  );

  const renderFooter = useCallback<MultiStepFormStepProps['renderFooter']>(
    (props) => (
      <BatchActionsPipelineFooter
        {...props}
        onClose={onClose}
        onFinish={onFinish}
      />
    ),
    [onClose, onFinish]
  );

  const data: Array<MultiStepFormStepProps> = useMemo(
    () =>
      new Array(headerTitleData.length).fill(1).map((_, i) => ({
        stepKey: i.toString(),
        getHeaderProps,
        getStepHeaderProps,
        renderBody,
        renderFooter,
      })),
    [getHeaderProps, getStepHeaderProps, renderBody, renderFooter]
  );

  return data;
}

const headerTitleData = [
  {
    title: 'bulk_job_action',
  },
  {
    title: {
      status: 'change_status',
      priority: 'change_priority',
      projects: 'change_project_links',
    },
  },
  {
    title: 'change_project_links',
  },
];
