'use client';

import { searchFilterQueryParams } from 'shared/constants/search';
import useSearchQueries from 'shared/hooks/useSearchQueries';
import type { FiltersDataProps } from 'shared/types/generalProps';
import {
  Endpoints,
  pageEndpoints,
  searchEndPoints,
} from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import geoApi from 'shared/utils/api/geo';
import lookupResponseNormalizer, {
  lookupResponseNormalizerWithLabel,
} from 'shared/utils/normalizers/lookupResponseNormalizer';
import skillsResponseNormalizer from 'shared/utils/normalizers/skillsResponseNormalizer';
import {
  experienceLevels,
  JOB_EMPLOYMENT_TYPE_MODEL,
  JOB_PRIORITY_MODEL,
  JOB_RESPONSE_TIME_MODEL,
  JOB_STATUS,
  memberSince,
  PIPELINE_STATUS_DATA,
  sortBy,
  WORK_SPACE_MODEL,
} from 'shared/utils/constants/enums/jobsDb';
import { useMemo } from 'react';
import capitalize from 'lodash/capitalize';
import type { PaginateResponse } from 'shared/types/response';
import { hashtagNormalizer } from '@shared/utils/normalizers/hashtagNormalizer';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import classes from './RecruiterJobDetails.module.scss';

const FORM_GROUP_CLASS = '!py-0 !mb-12';
const PLUS_BUTTON_CLASS = '!mt-8 !ml-8';

const useRecruiterJobSearch = (filtersData?: FiltersDataProps) => {
  const { t } = useTranslation();
  const { authUser } = useGetAppObject();
  const countryCode = authUser?.location?.countryCode;
  const { getQueryValue } = useSearchQueries();

  const titlesOptions = useMemo(() => {
    if (!filtersData?.titles) return [];
    return filtersData.titles.map((title) => ({
      value: title,
      label: title,
    }));
  }, [filtersData?.titles]);
  const categoriesOptions = useMemo(() => {
    if (!filtersData?.categories) return [];
    return filtersData.categories.map((category) => ({
      value: category.id,
      label: category.title,
    }));
  }, [filtersData?.categories]);
  const citiesData = useMemo(() => {
    if (!filtersData?.cities) return [];
    return filtersData.cities.map((city) => ({
      value: city.code,
      label: city.name,
    }));
  }, [filtersData?.cities]);
  const skillsData = useMemo(() => {
    if (!filtersData?.skills) return [];
    return filtersData.skills.map((skill) => ({
      value: skill.skillName,
      label: skill.skillName,
    }));
  }, [filtersData?.skills]);
  const languages = useMemo(() => {
    if (!filtersData?.languages) return [];
    return filtersData.languages.map((language) => ({
      value: language.languageId,
      label: language.languageName,
    }));
  }, [filtersData?.languages]);
  const projects = useMemo(() => {
    if (!filtersData?.projects) return [];
    return filtersData.projects.map((project) => ({
      value: project.id,
      label: project.title,
    }));
  }, [filtersData?.projects]);
  const collaborators = useMemo(() => {
    if (!filtersData?.collaborators) return [];
    return filtersData.collaborators.map((collaborator) => ({
      value: collaborator.id,
      label: `${collaborator.name} ${collaborator.surname}`,
    }));
  }, [filtersData?.collaborators]);
  const pointOfContacts = useMemo(() => {
    if (!filtersData?.pointOfContacts) return [];
    return filtersData.pointOfContacts.map((poc) => ({
      value: poc.id,
      label: `${poc.name} ${poc.surname}`,
    }));
  }, [filtersData?.pointOfContacts]);
  const creators = useMemo(() => {
    if (!filtersData?.creators) return [];
    return filtersData.creators.map((creator) => ({
      value: creator.id,
      label: `${creator.name} ${creator.surname}`,
    }));
  }, [filtersData?.creators]);
  const authorizations = useMemo(() => {
    if (!filtersData?.workAuthorization) return [];
    return filtersData.workAuthorization.map((wa) => ({
      value: wa.id,
      label: wa.title,
    }));
  }, [filtersData?.workAuthorization]);
  const clientIds = useMemo(
    () =>
      filtersData?.clientIds?.map((project) => ({
        value: project.id,
        label: project.title,
      })) || [],
    [filtersData?.clientIds]
  );
  const vendorIds = useMemo(
    () =>
      filtersData?.vendorIds?.map((project) => ({
        value: project.id,
        label: project.title,
      })) || [],
    [filtersData?.vendorIds]
  );

  const tags = useMemo(() => {
    if (!filtersData?.tags) return [];
    return filtersData.tags.map((tag) => ({
      value: tag,
      label: tag,
    }));
  }, [filtersData?.tags]);
  const hashtags = useMemo(() => {
    if (!filtersData?.hashtags) return [];
    return filtersData.hashtags.map((hashtag) => ({
      value: hashtag,
      label: hashtag,
    }));
  }, [filtersData?.hashtags]);

  const travelRequirements = useMemo(
    () =>
      filtersData?.travelRequirements?.map((item) => ({
        value: item,
        label: capitalize(item),
      })) || [],
    [filtersData?.travelRequirements]
  );

  const STATUS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('status'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'radioGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.status,
    options: JOB_STATUS,
    label: t('status'),
    getValue: () => getQueryValue(searchFilterQueryParams.status),
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const PIPELINE_STATUS = {
    ...STATUS,
    options: PIPELINE_STATUS_DATA,
    alwaysShowInHeader: true,
  };
  const TITLES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('j_title'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.titles,
    options: titlesOptions,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.getOccupations,
      normalizer: lookupResponseNormalizerWithLabel,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('j_title'),
    placeholder: t('search_j_title'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.titles, 'array'),
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const CATEGORY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('category'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.categoryIds,
    options: categoriesOptions,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.getIndustry,
      normalizer: lookupResponseNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('category'),
    placeholder: t('search_category'),
    getValue: () => getQueryValue(searchFilterQueryParams.categoryIds, 'array'),
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const JOB_MODEL = {
    formGroup: {
      color: 'smoke_coal',
      title: t('job_model'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.workPlaceTypes,
    options: WORK_SPACE_MODEL,
    label: t('job_model'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.workPlaceTypes, 'array'),
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const EMPLOYMENT_TYPE = {
    formGroup: {
      color: 'smoke_coal',
      title: t('j_type'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.employmentTypes,
    options: JOB_EMPLOYMENT_TYPE_MODEL,
    label: t('j_type'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.employmentTypes, 'array'),
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const LOCATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('location'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.cities,
    options: citiesData,
    value: citiesData,
    label: t('locations'),
    placeholder: t('search_location'),
    getValue: () => getQueryValue(searchFilterQueryParams.cities, 'array'),
    alwaysShowInHeader: true,
    asyncAutoCompleteProps: {
      maxLength: 100,
      apiFunc: geoApi.suggestCity,
      params: { countryCode },
      normalizer: (items: any) =>
        items?.map((item: any) => ({
          label: item?.label,
          value: item?.cityCode,
        })),
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    divider: {
      className: classes.groupDivider,
    },
  };

  // side filter
  const SORT_BY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('sort_by'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'radioGroup',
    name: searchFilterQueryParams.sortBy,
    options: sortBy,
    label: t('sort_by'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.sortBy) || 'MOST_RELEVANT',
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const DATE_POSTED = {
    formGroup: {
      color: 'smoke_coal',
      title: t('date_posted'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'radioGroup',
    name: searchFilterQueryParams.datePosted,
    divider: {
      className: classes.groupDivider,
    },
    options: memberSince,
    label: t('date_posted'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.datePosted) || 'ANY_TIME',
    hiddenInHeader: true,
    alwaysShowInHeader: false,
  };
  const PRIORITY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('priority'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.priorities,
    options: JOB_PRIORITY_MODEL,
    getValue: () => getQueryValue(searchFilterQueryParams.priorities, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const PIPELINE_PRIORITY = {
    ...PRIORITY,
    label: t('priority'),
    alwaysShowInHeader: true,
  };
  const SKILLS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('skills'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    name: searchFilterQueryParams.skills,
    options: skillsData,
    label: t('skills'),
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.getSkills,
      normalizer: skillsResponseNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    placeholder: t('search_skill'),
    getValue: () => getQueryValue(searchFilterQueryParams.skills, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const LANGUAGES = {
    formGroup: {
      color: 'smoke_coal',
      title: t('languages'),
      className: FORM_GROUP_CLASS,
    },
    name: searchFilterQueryParams.languageIds,
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    label: t('languages'),
    placeholder: t('search_language'),
    options: languages,
    asyncAutoCompleteProps: {
      plusButtonClassName: PLUS_BUTTON_CLASS,
      maxLength: 100,
      url: Endpoints.App.Common.getLanguages,
      normalizer: lookupResponseNormalizer,
    },
    getValue: () => getQueryValue(searchFilterQueryParams.languageIds, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const EXPERIENCE_LEVEL = {
    formGroup: {
      color: 'smoke_coal',
      title: t('exp_level'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.experienceLevels,
    options: experienceLevels,
    label: t('exp_level'),
    placeholder: t('search_exp_level'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.experienceLevels, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const PROJECTS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('projects'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.projectIds,
    options: projects,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('projects'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.projectIds, 'array'),
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const SALARY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('salary_range'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'salaryPicker',
    withConfirmation: false,
    name: searchFilterQueryParams.salaryRange,
    data: filtersData?.salaryRange ?? {},
    getValue: () => getQueryValue(searchFilterQueryParams.salaryRange),
    label: t('salary_range'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const COLLABORATORS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('assignees'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.collaboratorUserIds,
    options: collaborators,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: pageEndpoints.pageAccessibilities,
      normalizer: responseCollaboratorsNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('assignees'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.collaboratorUserIds, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const POINT_OF_CONTACT = {
    formGroup: {
      color: 'smoke_coal',
      title: t('point_of_contact'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.pointOfContactUserIds,
    options: pointOfContacts,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: pageEndpoints.pageAccessibilities,
      normalizer: responseCollaboratorsNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('point_of_contact'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.pointOfContactUserIds, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const CREATED_BY = {
    formGroup: {
      color: 'smoke_coal',
      title: t('created_by'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.creatorUserIds,
    options: creators,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: pageEndpoints.pageAccessibilities,
      normalizer: responseCollaboratorsNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('created_by'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.creatorUserIds, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const RESPONSE_TIME = {
    formGroup: {
      color: 'smoke_coal',
      title: t('response_time'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.responseTimes,
    options: JOB_RESPONSE_TIME_MODEL,
    label: t('response_time'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.responseTimes, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const AUTHORIZATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('work_authorization'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.workAuthorizationIds,
    options: authorizations,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Common.searchAuthorization,
      normalizer: lookupResponseNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    label: t('work_authorization'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () =>
      getQueryValue(searchFilterQueryParams.workAuthorizationIds, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const TAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('tags'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.tags,
    options: tags,
    label: t('tags'),
    getValue: () => getQueryValue(searchFilterQueryParams.tags, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };
  const HASHTAGS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('hashtags'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.hashtags,
    options: hashtags,
    label: t('hashtags'),
    placeholder: t('search_hashtag'),
    getValue: () => getQueryValue(searchFilterQueryParams.hashtags, 'array'),
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: searchEndPoints.suggestHashtag,
      normalizer: hashtagNormalizer,
      plusButtonClassName: PLUS_BUTTON_CLASS,
    },
    hiddenInHeader: true,
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };

  const ASSOCIATION = {
    formGroup: {
      color: 'smoke_coal',
      title: t('priority'),
      className: FORM_GROUP_CLASS,
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.priorities,
    options: JOB_PRIORITY_MODEL,
    getValue: () => getQueryValue(searchFilterQueryParams.priorities, 'array'),
    alwaysShowInHeader: true,
    label: t('association'),
    divider: {
      className: classes.groupDivider,
    },
    disabled: true,
  };

  const VENDOR = {
    formGroup: {
      color: 'smoke_coal',
      title: t('Vendor'),
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.vendorIds,
    options: vendorIds,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    label: t('Vendor'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.vendorIds, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };
  const CLIENT = {
    formGroup: {
      color: 'smoke_coal',
      title: t('Client'),
    },
    cp: 'checkBoxGroup',
    withConfirmation: false,
    name: searchFilterQueryParams.clientIds,
    options: clientIds,
    asyncAutoCompleteProps: {
      maxLength: 100,
      url: Endpoints.App.Project.search,
      normalizer: responseProjectsNormalizer,
    },
    label: t('Client'),
    placeholder: t('search_placeholder'),
    isCustomEntryAllowed: true,
    getValue: () => getQueryValue(searchFilterQueryParams.clientIds, 'array'),
    hiddenInHeader: true,
    alwaysShowInHeader: true,
    divider: {
      className: classes.groupDivider,
    },
  };

  const TRAVEL_REQUIREMENTS = {
    formGroup: {
      color: 'smoke_coal',
      title: t('willing_to_travel'),
      className: classes.header,
    },
    cp: 'checkBoxGroup',
    classNames: { inputContainer: classes.checkBoxInputContainer },
    withConfirmation: false,
    name: searchFilterQueryParams.travelRequirements,
    options: travelRequirements,
    label: t('willing_to_travel'),
    getValue: () =>
      getQueryValue(searchFilterQueryParams.travelRequirements, 'array'),
    alwaysShowInHeader: false,
    divider: {
      className: classes.groupDivider,
    },
  };

  const groups = [
    SORT_BY,
    DATE_POSTED,
    PROJECTS,
    STATUS,
    PRIORITY,
    TITLES,
    CATEGORY,
    JOB_MODEL,
    EMPLOYMENT_TYPE,
    LOCATION,
    VENDOR,
    CLIENT,
    SKILLS,
    LANGUAGES,
    EXPERIENCE_LEVEL,
    SALARY,
    COLLABORATORS,
    POINT_OF_CONTACT,
    CREATED_BY,
    RESPONSE_TIME,
    AUTHORIZATION,
    TAGS,
    HASHTAGS,
    TRAVEL_REQUIREMENTS,
  ];

  const pipelineGroups = [
    PIPELINE_PRIORITY,
    PIPELINE_STATUS,
    ASSOCIATION,
    TITLES,
    CATEGORY,
    LOCATION,
  ];

  return { groups, pipelineGroups };
};

export default useRecruiterJobSearch;

type LookupResponseNormalizerType = Array<{
  value: string;
  label: string;
}>;
export const responseProjectsNormalizer = (
  response: PaginateResponse<{ id: string; title: string }>
): LookupResponseNormalizerType =>
  response?.content
    ?.map(({ title, id }) => ({
      value: id,
      label: title,
    }))
    .slice(0, 10);
export const responseCollaboratorsNormalizer = (
  response: PaginateResponse<{
    userId: string;
    profileInfo: { fullName: string };
  }>
): LookupResponseNormalizerType =>
  response?.content
    ?.map(({ profileInfo, userId }) => ({
      value: userId,
      label: profileInfo.fullName,
    }))
    .slice(0, 6);
