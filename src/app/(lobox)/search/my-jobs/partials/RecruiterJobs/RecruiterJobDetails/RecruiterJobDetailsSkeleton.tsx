import CandidateCardSkeleton from '@shared/components/molecules/CandidateCard/CandidateCardSkeleton';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';

const RecruiterCandidatesSkeleton = () => (
  <CandidateCardSkeleton showBadges showTags>
    <Flex className="!flex-row gap-12">
      {Array.from({ length: 2 }).map((_, index) => (
        <Skeleton key={index} className="w-50 flex-1 h-32 rounded" />
      ))}
    </Flex>
  </CandidateCardSkeleton>
);

export default function RecruiterJobDetailsSkeleton() {
  return (
    <Flex className="mt-32 gap-20">
      {Array.from({ length: 3 }).map((_, index) => (
        <RecruiterCandidatesSkeleton key={index} />
      ))}
    </Flex>
  );
}
