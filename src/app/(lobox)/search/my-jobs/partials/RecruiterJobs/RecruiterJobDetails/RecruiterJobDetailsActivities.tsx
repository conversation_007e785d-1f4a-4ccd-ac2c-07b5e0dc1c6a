import ActivityItem from 'shared/components/molecules/ActivityItem';
import type { ActivityProps } from 'shared/types/activityProps';
import Flex from 'shared/uikit/Flex';
import { useQuery } from '@tanstack/react-query';
import type { FC } from 'react';
import { Endpoints, QueryKeys } from 'shared/utils/constants';
import request from 'shared/utils/toolkit/request';
import type { PaginateResponse } from 'shared/types/response';
import classes from './RecruiterJobDetailsStyles.module.scss';

interface RecruiterJobDetailsActivitiesProps {
  jobId: string;
}

const RecruiterJobDetailsActivities: FC<RecruiterJobDetailsActivitiesProps> = ({
  jobId,
}) => {
  const { data, isLoading } = useQuery({
    queryKey: [QueryKeys.jobActivities, jobId],
    queryFn: (params) =>
      request.get<PaginateResponse<ActivityProps>>(
        Endpoints.App.Job.getActivities(params.queryKey[1])
      ),
  });

  return (
    <Flex className={classes.applicantsRoot}>
      {data?.data.content.map((activity) => (
        <ActivityItem key={`activity_${activity.id}`} item={activity} />
      ))}
    </Flex>
  );
};

export default RecruiterJobDetailsActivities;
