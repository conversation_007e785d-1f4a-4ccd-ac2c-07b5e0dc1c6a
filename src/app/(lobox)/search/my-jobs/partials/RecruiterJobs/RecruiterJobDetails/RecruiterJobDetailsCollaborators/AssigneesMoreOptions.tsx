import IconButton from '@shared/uikit/Button/IconButton';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import useTranslation from '@shared/utils/hooks/useTranslation';
import getCookieKey from '@shared/utils/toolkit/getCookieKey';
import { decodeObject } from '@shared/utils/middleware-utils';
import Cookies from '@shared/utils/toolkit/cookies';
import { useMemo, type FC } from 'react';
import Tooltip from '@shared/uikit/Tooltip';
import { ShareEntities, ShareEntityTab } from '@shared/types/share/entities';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import type { JobDetailsCollaboratorProps } from '@shared/types/jobsProps';
import { useSchedulesCalendar } from '@shared/hooks/useSchedulesCalendar';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { Time } from '@shared/utils/Time';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateJobAssignees } from '@shared/utils/api/jobs';
import { useSearchParams } from 'next/navigation';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { QueryKeys } from '@shared/utils/constants';

interface AssigneesMoreOptionsProps {
  id?: string;
  assignee: JobDetailsCollaboratorProps;
  assigneesList?: JobDetailsCollaboratorProps[];
}

const AssigneesMoreOptions: FC<AssigneesMoreOptionsProps> = ({
  id,
  assignee,
  assigneesList,
}) => {
  const { t } = useTranslation();
  const USER_OBJ_TOKEN = getCookieKey('userObjToken');
  const userObjToken = decodeObject(Cookies.get(USER_OBJ_TOKEN)) as any;
  const isOwner = userObjToken?.userId === String(id);
  const { openCreateEventWithDate } = useSchedulesCalendar();
  const searchParams = useSearchParams();
  const entityId = searchParams.get('currentEntityId') as string;
  const queryClient = useQueryClient();
  const { handleChangeParams } = useCustomParams();

  const isPointOfContact = useMemo(() => !assigneesList, [assigneesList]);
  const data = useMemo(
    () => ({
      ...assignee,
      profile: {
        croppedImageUrl: assignee.croppedImageUrl,
        usernameAtSign: `@${assignee.username}`,
        occupation: assignee.occupation,
        location: assignee.location,
        fullName: assignee.fullName,
      },
    }),
    [assignee]
  );
  const appDispatch = useGlobalDispatch();

  const { mutate: removeAssignee } = useMutation({
    mutationFn: (ids: string[]) => updateJobAssignees(entityId, ids),
    onSuccess: () => {
      handleChangeParams({
        add: { refresh: 'true' },
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.jobCollaborators, entityId],
        exact: false,
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.jobDetails, entityId],
        exact: false,
      });
    },
  });

  const shareCandidate = () => {
    appDispatch({
      type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA',
      payload: {
        isOpen: true,
        tabs: [
          ShareEntityTab.COPY_LINK,
          ShareEntityTab.SHARE_VIA_MESSAGE,
          ShareEntityTab.SHARE_VIA_EMAIL,
        ],
        entityData: {
          showCopyId: true,
          attachment: {
            type: ShareEntities.CANDIDATE,
            data: { ...data, isBusiness: true },
          },
        },
      },
    });
  };
  const handleSetUpMeeting = () => {
    openCreateEventWithDate(Time.getToday(), {
      schedulesEventType: ScheduleEventTypes.MEETING,
      attendees: [data],
    });
  };
  const handleRemoveAssignee = () => {
    const updatedAssignees = assigneesList
      ?.filter((existingAssignee) => existingAssignee.id !== assignee.id)
      .map((_assignee) => _assignee.id);
    removeAssignee(updatedAssignees ?? []);
  };

  return (
    <PopperMenu
      placement="bottom-end"
      closeOnScroll
      buttonComponent={<IconButton type="fas" name="ellipsis-h" size="md" />}
    >
      <Tooltip
        trigger={
          <PopperItem
            label={t('remove_assignee')}
            iconName="remove-follower"
            iconType="far"
            iconSize={20}
            disabled={isPointOfContact}
            onClick={handleRemoveAssignee}
          />
        }
        disabled={!isPointOfContact}
      >
        {t('poc_cant_remove')}
      </Tooltip>
      {!isOwner && (
        <PopperItem
          label={t('schedule_meeting')}
          iconName="calendar-days"
          iconType="far"
          iconSize={20}
          onClick={handleSetUpMeeting}
        />
      )}
      <PopperItem
        label={t('share_profile')}
        iconName="share"
        iconType="far"
        iconSize={20}
        onClick={shareCandidate}
      />
    </PopperMenu>
  );
};

export default AssigneesMoreOptions;
