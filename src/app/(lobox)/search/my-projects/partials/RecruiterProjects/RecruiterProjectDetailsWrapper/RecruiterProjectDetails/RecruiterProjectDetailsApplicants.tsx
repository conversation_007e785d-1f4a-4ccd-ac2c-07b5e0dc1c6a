import ApplicantCard from 'shared/components/molecules/ApplicantCard';
import EmptySearchResult from 'shared/components/Organism/EmptySearchResult';
import SearchList from 'shared/components/Organism/SearchList';
import { useQueryClient } from '@tanstack/react-query';
import { useCallback, type FC } from 'react';
import useChangePipeline from 'shared/hooks/useChangePipeline';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { QueryKeys } from 'shared/utils/constants';
import { getProjectEntitiesAPI } from '@shared/utils/api/project';
import type { JobParticipationModel } from '@shared/types/jobsProps';
import Button from '@shared/uikit/Button';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import type { ProjectProps } from '@shared/types/project';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import classes from './RecruiterProjectDetailsStyles.module.scss';

interface RecruiterProjectDetailsApplicantsProps {
  project: ProjectProps;
}

const RecruiterProjectDetailsApplicants: FC<
  RecruiterProjectDetailsApplicantsProps
> = ({ project }) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const projectId = project.id;
  const appDispatch = useGlobalDispatch();

  const {
    data,
    totalElements,
    totalPages,
    isLoading,
    refetch,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useReactInfiniteQuery<JobParticipationModel>(
    [QueryKeys.projectApplications, projectId],
    {
      func: (props) =>
        getProjectEntitiesAPI('applicants', projectId, {
          page: props.pageParam,
          onlyDone: false,
        }),
      size: 10,
    }
  );

  const { onChangePipeline } = useChangePipeline({
    onSuccess: () => {
      refetch();
      queryClient.refetchQueries({
        queryKey: [QueryKeys.projectActivities, projectId, 0],
      });
    },
    variant: 'applicant',
  });

  const handleOpenManagerModal = useCallback(
    (candidateId: string, tab: CandidateManagerTabkeys) => {
      appDispatch({
        type: 'TOGGLE_CANDIDATE_MANAGER',
        payload: {
          isOpen: true,
          tab,
          id: candidateId,
          enableNavigate: false,
        },
      });
    },
    [appDispatch]
  );

  // TODO: needs to be implemented later!
  const handleShare = () => {
    alert('Todo: needs to be implemented later!');
  };

  return (
    <SearchList
      entity="recruiterJobs"
      isLoading={isLoading}
      totalElements={Number(totalElements)}
      data={data ?? []}
      onPageChange={() => {}}
      totalPages={Number(totalPages)}
      className={{ root: classes.applicantsRoot }}
      noItemButtonAction
      renderItem={(application) => (
        <ApplicantCard
          data={application}
          onChangePipeline={(pipeline) =>
            onChangePipeline({
              userId: application.id,
              pipelineId: pipeline.id as string,
            })
          }
          key={`application_${application.id}`}
          showJob
          onPrimaryButtonClick={() => {
            handleOpenManagerModal(application.applicant.id, 'notes');
          }}
        />
      )}
      emptyList={
        <EmptySearchResult
          title={t('no_applicant')}
          sectionMessage={translateReplacer(t('no_antity_in_object'), [
            t('applicants').toLowerCase(),
            t('share_job').toLowerCase(),
          ])}
          className={classes.emptyList}
          classNames={{ description: '!mt-12' }}
        >
          <Button
            label={t('share_job')}
            className="mt-20"
            leftIcon="share"
            leftType="far"
            onClick={handleShare}
          />
        </EmptySearchResult>
      }
      // parentPage={0}
      innerList
    />
  );
};

export default RecruiterProjectDetailsApplicants;
