import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import Flex from '@shared/uikit/Flex';
import { QueryKeys } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useState, type FC } from 'react';
import ScheduleCard from '@shared/components/molecules/ScheduleCard';
import Button from '@shared/uikit/Button';
import SearchList from '@shared/components/Organism/SearchList';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';
import { getProjectEntitiesAPI } from '@shared/utils/api/project';
import type { MeetingDataProps } from '@shared/types/meeting';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import classes from './RecruiterProjectDetailsStyles.module.scss';
import { PROJECT_ENTITIES_VARIANT } from '@shared/constants/projects';

type MeetingType = 'upcoming' | 'past';
interface RecruiterProjectDetailsMeetingsProps {
  projectId: string;
}

const RecruiterProjectDetailsMeetings: FC<
  RecruiterProjectDetailsMeetingsProps
> = ({ projectId }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<MeetingType>('upcoming');

  const { content, setPage, page, totalElements, totalPages, isLoading } =
    usePaginateQuery<MeetingDataProps>({
      action: {
        apiFunc: getProjectEntitiesAPI,
        key: [QueryKeys.projectMeetings, projectId, activeTab],
        params: {
          variant:
            activeTab === 'past'
              ? PROJECT_ENTITIES_VARIANT.pastMettings
              : PROJECT_ENTITIES_VARIANT.upcomingMettings,
          projectId,
          size: 10,
        },
      },
    });

  return (
    <Flex className={classes.meetingsRoot}>
      <Flex className={classes.buttons}>
        <Button
          label={t('up_meetings')}
          schema={
            activeTab === 'upcoming' ? 'primary-blue' : 'semi-transparent'
          }
          onClick={() => setActiveTab('upcoming')}
          className={classes.button}
        />
        <Button
          label={t('past_meetings')}
          schema={activeTab === 'past' ? 'primary-blue' : 'semi-transparent'}
          onClick={() => setActiveTab('past')}
          className={classes.button}
        />
      </Flex>
      <SearchList
        entity="todos"
        isLoading={isLoading}
        totalElements={Number(totalElements)}
        data={content}
        onPageChange={setPage}
        totalPages={Number(totalPages)}
        className={{ root: classes.childrenBox }}
        noItemButtonAction
        renderItem={(meeting) => (
          <ScheduleCard
            actionProps={{ label: 'View meeting' }}
            item={{ meeting }}
            cardProps={{
              classNames: { root: classes.jobItem },
            }}
          />
        )}
        emptyList={
          <EmptySearchResult
            title={t('no_meetings')}
            sectionMessage={translateReplacer(t('no_antity_in_object'), [
              t('meetings').toLowerCase(),
              t('create_one').toLowerCase(),
            ])}
            className={classes.emptyList}
            classNames={{ description: '!mt-12' }}
          />
        }
        parentPage={page}
        innerList
      />
    </Flex>
  );
};

export default RecruiterProjectDetailsMeetings;
