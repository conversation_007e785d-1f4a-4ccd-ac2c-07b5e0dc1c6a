import { CompanyTab, ECompanyRole } from '@shared/types/company';

export function getMyCompanyRole(
  onlyClients: string,
  activeTab: CompanyTab
): ECompanyRole {
  const isPendingTab = activeTab === CompanyTab.PENDING;
  if (isPendingTab) {
    return onlyClients === 'true' ? ECompanyRole.VENDOR : ECompanyRole.CLIENT;
  }
  return onlyClients === 'true' ? ECompanyRole.CLIENT : ECompanyRole.VENDOR;
}
