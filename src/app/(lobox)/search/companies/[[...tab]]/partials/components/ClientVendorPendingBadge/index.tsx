import Button from '@shared/uikit/Button';
import React from 'react';
import useTranslation from '@shared/utils/hooks/useTranslation';

export function ClientVendorPendingBadge({
  isVendorPending,
}: {
  isVendorPending: boolean;
}) {
  const { t } = useTranslation();

  return (
    <Button
      schema="orange-semi-transparent"
      label={isVendorPending ? t('vendor_pending') : t('client_pending')}
      leftIcon="info-circle"
      leftType="far"
      labelSize={12}
      className="!px-8 hover:!bg-pendingOrange_10 mb-auto"
      variant="thin"
    />
  );
}
