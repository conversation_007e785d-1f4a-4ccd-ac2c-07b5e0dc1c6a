import React from 'react';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import useTranslation from '@shared/utils/hooks/useTranslation';
import InfoCard from '@shared/uikit/InfoCard';
import Typography from '@shared/uikit/Typography';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import Flex from '@shared/uikit/Flex';
import Button from '@shared/uikit/Button';
import { searchCompanies } from '@shared/utils/api/search';
import Form from '@shared/uikit/Form';
import { addClients, addVendors } from '@shared/utils/api/company';
import AvatarCard from '@shared/uikit/AvatarCard';
import CheckBox from '@shared/uikit/CheckBox';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import { QueryKeys, routeNames } from '@shared/utils/constants';
import formValidator from '@shared/utils/form/formValidator';
import useToast from '@shared/uikit/Toast/useToast';
import {
  useSearchDispatch,
  useSearchState,
} from '@shared/contexts/search/search.provider';
import type { ISearchPageApi } from '@shared/types/search';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import { CompanyTab } from '@shared/types/company';
import { SearchableAsyncList } from '@shared/uikit/SearchableAsyncList';
import AvatarCardSkeleton from '@shared/uikit/AvatarCard/AvatarCard.Skeleton';
import { useRouter } from 'next/navigation';

const MAX_VALUE = 10;

export default function AddCompanyModal() {
  const { t } = useTranslation();
  const toast = useToast();
  const searchDispatch = useSearchDispatch();
  const companyType = useSearchState('addCompanyModalData')?.companyType;
  const isClient = companyType === CompanyTab.CLIENTS;
  const router = useRouter();

  const handleClose = () => {
    searchDispatch({ type: 'SET_ADD_COMPANY_MODAL_DATA' });
  };

  const onSuccess = () => {
    toast({
      type: 'success',
      icon: 'check-circle',
      title: 'request_sent',
      message: isClient ? 'yr_client_req_sent_suc' : 'yr_vendor_req_sent_suc',
    });
    handleClose();
    router.push(routeNames.companies[isClient ? 'clients' : 'vendors']);
  };

  return (
    <Form
      initialValues={{ companyIds: [] }}
      apiFunc={isClient ? addClients : addVendors}
      transform={({ companyIds }) => ({
        [isClient ? 'clientIds' : 'vendorIds']: companyIds,
      })}
      onSuccess={onSuccess}
      validationSchema={formValidator.object().shape({
        companyIds: formValidator
          .array()
          .min(1, t('validation.min_vendor'))
          .max(
            MAX_VALUE,
            translateReplacer(
              t('must_be_less_than_number_item'),
              `${MAX_VALUE}`
            )
          )
          .required(t('validation.required_vendor')),
      })}
    >
      {({ values, setFieldValue, dirty, errors, ...rest }) => (
        <FixedRightSideModal
          isOpen
          wide
          showConfirm={dirty}
          onClickOutside={handleClose}
          onClose={handleClose}
        >
          <ModalHeaderSimple
            title={companyType === 'VENDOR' ? t('add_vendor') : t('add_client')}
          />
          <ModalBody className="gap-20">
            <SearchableAsyncList<ISearchPageApi>
              variant="multi"
              name={QueryKeys.searchCompanies}
              renderInfoMessage={
                <Flex flexDir="column" className="gap-20">
                  <InfoCard
                    hasLeftIcon
                    leftIconProps={{
                      name: 'info-circle',
                      color: 'secondaryDisabledText',
                    }}
                    classNames={{ wrapper: '!bg-gray_5' }}
                  >
                    <Typography size={14} color="secondaryDisabledText">
                      {t('search_company_infobox_content')}
                    </Typography>
                  </InfoCard>
                  {isClient && (
                    <InfoCard
                      hasLeftIcon
                      leftIconProps={{
                        name: 'info-circle',
                        color: 'warning',
                      }}
                      classNames={{ wrapper: '!bg-warning_10 mt-[-8px]' }}
                    >
                      <Typography size={14} color="smoke_coal">
                        {t('as_a_vendor_pntyar_lim_tolif_refq')}
                      </Typography>
                    </InfoCard>
                  )}
                </Flex>
              }
              renderItem={({ item }) => {
                const isSelected = values.companyIds?.find(
                  (i) => i === item.id
                );

                return (
                  <AvatarCard
                    data={{
                      title: item.title,
                      subTitle: item.username,
                      image: item.croppedImageUrl,
                    }}
                    avatarProps={{ isCompany: true }}
                    onClick={() =>
                      setFieldValue(
                        'companyIds',
                        isSelected
                          ? values.companyIds.filter((i) => i !== item.id)
                          : [...values.companyIds, item.id]
                      )
                    }
                    action={
                      <CheckBox
                        classNames={{ root: '!ml-auto' }}
                        value={isSelected}
                      />
                    }
                    key={`company-item-${item?.id}`}
                  />
                );
              }}
              pageSize={10}
              params={{
                spreadParams: true,
              }}
              normalizer={(values: any) =>
                values?.content?.map((item: any) => item)
              }
              keywords="text"
              apiFunc={searchCompanies}
              placeholder={t('search_company_placeholder')}
              renderLoading={<AvatarCardSkeleton />}
              enableInfiniteScroll
            />
          </ModalBody>
          <ModalFooter className="gap-12">
            <Flex flexDir="row" className="justify-between">
              <Typography size={13} color="error">
                {values.companyIds.length > MAX_VALUE && errors.companyIds}
              </Typography>
              <Typography size={12} color="secondaryDisabledText">
                {values.companyIds.length} / {MAX_VALUE}
              </Typography>
            </Flex>
            <Flex flexDir="row" className="gap-8">
              <Button
                label={t('discard')}
                schema="gray"
                onClick={handleClose}
                fullWidth
              />
              <SubmitButton label={t('send_request')} fullWidth />
            </Flex>
          </ModalFooter>
        </FixedRightSideModal>
      )}
    </Form>
  );
}
