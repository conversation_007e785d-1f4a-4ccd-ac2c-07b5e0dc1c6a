import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';

import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import InfoCard from '@shared/components/Organism/Objects/Common/InfoCard';
import { useObjectClicks } from '@shared/hooks/useObjectClicks';
import RichTextView from '@shared/uikit/RichText/RichTextView';
import formatDate from '@shared/utils/toolkit/formatDate';
import Flex from '@shared/uikit/Flex';
import OverflowTip from '@shared/uikit/Typography/OverflowTip';
import cnj from '@shared/uikit/utils/cnj';
import classes from './Overview.component.module.scss';

const Overview: React.FC = ({ company }) => {
  const { t } = useTranslation();

  const category = company.industry?.label;
  const { description, establishmentDate, hashtags } = company;
  const companySize = company.companySize?.label;

  const { handleHashtagClick, hoveredHashtag, onHashtagHover } =
    useObjectClicks();

  const categoryField = {
    id: 'industry',
    title: t('category'),
    subTitle: t('no_category_ent'),
    value: category,
    icon: 'list-ul',
  };

  const hashtagField = hashtags?.length
    ? {
        id: 'hashtags',
        title: t('hashtags'),
        subTitle: t('no_hashtag_ent'),
        icon: 'hashtag',
        value: (
          <Flex className={classes.hashtagWrapper}>
            {hashtags?.map((item: any, index: number) => (
              <OverflowTip
                size={15}
                key={`${item}_${index}`}
                color="thirdText"
                mt={5}
                className={cnj(classes.hashtag)}
                height={18}
              >
                {`#${item}`}&nbsp;
              </OverflowTip>
            ))}
          </Flex>
        ),
      }
    : undefined;
  const descriptionField = description && {
    id: 'description',
    title: t('description'),
    subTitle: t('no_desc_ent'),
    value: description ? (
      <RichTextView
        html={description}
        row={4}
        showMore={false}
        typographyProps={{
          color: 'thirdText',
          isWordWrap: true,
        }}
        onHashtagClick={handleHashtagClick}
        className={classes.richText}
        onHashtagHover={onHashtagHover}
        hoveredHashtag={hoveredHashtag}
      />
    ) : undefined,
    icon: 'info-circle',
  };

  const establishmentDateField = {
    id: 'establishment_date',
    title: t('establishment_date'),
    subTitle: t('no_establishment_date_ent'),
    value: establishmentDate
      ? formatDate(establishmentDate, 'YYYY')
      : undefined,
    icon: 'calendar-alt',
  };
  const companySizeField = {
    id: 'companySize',
    title: t('page_size'),
    subTitle: t('no_page_size_ent'),
    value: t(companySize),
    icon: 'users',
  };

  const data = [
    descriptionField,
    categoryField,
    establishmentDateField,
    companySizeField,
    hashtagField,
  ].filter(Boolean);

  return (
    <SectionLayout
      title={t('overview')}
      classNames={{ childrenWrap: '!p-20 gap-12' }}
      visibleActionButton={false}
    >
      {data.map(({ id, title, subTitle, value, icon }) => (
        <InfoCard
          key={id}
          disabledHover
          title={title}
          subTitle={subTitle}
          value={value}
          icon={icon}
          border={false}
          wrapperClassName="!p-0"
        />
      ))}
    </SectionLayout>
  );
};

export default Overview;
