import React, { useEffect, useState } from 'react';
import GoogleMap from 'shared/uikit/GoogleMap';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type ILocation from 'shared/utils/ILocation';
import MenuItem from 'shared/uikit/MenuItem';
import Flex from 'shared/uikit/Flex';
import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import classes from './Location.component.module.scss';

const centerLat = 41.0536;
const centerLon = 28.982;

const Location: React.FC = ({ company }) => {
  const { t } = useTranslation();
  const [selected, setSelected] = useState<ILocation>(undefined);

  const locations = company?.locations || [];

  useEffect(() => {
    const location = locations?.[0]?.location;
    if (location) {
      setSelected({
        ...location,
        isInitLocation: true,
      });
    }
  }, [locations?.length]);

  const onClick = (val: ILocation) => () => {
    setSelected({
      ...val,
      isInitLocation: false,
    });
  };

  if (!locations || locations.length === 0) {
    return null;
  }

  return (
    <SectionLayout
      title={t('locations')}
      classNames={{ childrenWrap: '!p-20' }}
      visibleActionButton={false}
    >
      {locations?.length > 0 && (
        <GoogleMap
          markers={
            selected
              ? [
                  {
                    key: 1,
                    position: [selected.lat, selected.lon],
                  },
                ]
              : undefined
          }
          center={[centerLat, centerLon]}
          value={
            selected ? { lat: selected.lat, lon: selected.lon } : undefined
          }
          visiblePicker={false}
          zoom={selected?.zoom}
          defaultZoom={selected?.zoom}
          animated={!selected?.isInitLocation}
          zoomAble
          draggable
        />
      )}
      <Flex className={classes.addressWrapper}>
        {locations.map((address: ILocation) => {
          const { location = {}, subTitle, name } = address;
          return (
            <MenuItem
              key={location.title || name}
              title={name || location.countryCode}
              titleVariant="lg"
              subTitle={
                location?.title + [location.lat, location.lon].join(',')
              }
              iconVariant="square"
              iconName="location-v2"
              iconType="far"
              iconBoxSize={40}
              iconSize={21}
              iconColor={
                selected?.externalId === location.externalId
                  ? 'brand'
                  : 'smoke_coal'
              }
              withHover
              onClick={onClick(address?.location)}
              disabled={false}
            />
          );
        })}
      </Flex>
    </SectionLayout>
  );
};

export default Location;
