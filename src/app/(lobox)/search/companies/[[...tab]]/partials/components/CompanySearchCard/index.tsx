import pagesCategories from '@shared/constants/pagesCategories';
import CompanyCardBadges from '@app/search/companies/[[...tab]]/partials/components/CompanyCardBadges';
import SearchCard from '@shared/components/Organism/SearchCard';
import useTranslation from '@shared/utils/hooks/useTranslation';
import React from 'react';
import type { CompanyType } from '@shared/types/company';
import { CompanyTab } from '@shared/types/company';
import { ClientVendorPendingBadge } from '../ClientVendorPendingBadge';

interface Props {
  item: CompanyType;
  activeTab: CompanyTab;
}
const CompanySearchCard: React.FC<Props> = ({ item, activeTab, ...props }) => {
  const { t } = useTranslation();

  return (
    <SearchCard
      imgSrc={item.croppedImageUrl}
      firstText={item.title}
      secondText={`@${item.username}`}
      fourthText={item.locationTitle || t('no_location_ent')}
      thirdText={t(pagesCategories[item.category]?.label)}
      {...props}
      topRightActionComponent={
        activeTab === CompanyTab.REQUESTS ? (
          <ClientVendorPendingBadge
            isVendorPending={item.companyRole === 'VENDOR'}
          />
        ) : undefined
      }
      bottomComponent={
        activeTab === CompanyTab.CLIENTS || activeTab === CompanyTab.VENDORS
          ? () => (
              <CompanyCardBadges
                jobCount={item.jobCount}
                candidateCount={item.candidateCount}
                collaboratorCount={item.collaboratorCount}
              />
            )
          : undefined
      }
      isPage
    />
  );
};

export default CompanySearchCard;
