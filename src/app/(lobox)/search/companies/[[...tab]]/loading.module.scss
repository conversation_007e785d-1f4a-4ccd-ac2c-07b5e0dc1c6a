@import '/src/shared/theme/theme.scss';

@layer layout {
  .contentRoot {
    background: colors(background2);
    flex-wrap: unset;
    flex: 1;
    width: 100%;
    padding: 0;
  }

  .jobsListWithDetails {
    justify-content: center;
  }

  .details {
    display: none;
    height: 100%;
    overflow-y: auto !important;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .mobileHeader {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    z-index: 503;
    background: colors(background);
  }

  .linksRootShrink {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: auto;
    z-index: 6;
    background-color: colors(background);
  }

  .searchFilterContent {
    flex-direction: row;
    align-items: center;
    padding: variables(gutter) * 0.5 0;
  }

  .modalButtonSkeleton {
    min-width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-left: auto;
  }

  .editBtn {
    margin-left: auto;
  }

  .maxWidth {
  }

  .backBtn {
    display: none;
  }

  .rightWrapper {
    display: none;
    padding-left: variables(gutter) * 0.5;
    flex-direction: row;
    align-items: center;
  }

  .mobileHeaderBackButton {
    display: none;
  }

  .titleWrapper {
    display: flex;
    flex-direction: row;
    word-break: break-all;

    & > * {
      display: inline;
    }
  }

  .searchIon {
    margin-left: auto;
  }

  .skeleton {
    height: 32px;
    width: 107px;
    border-radius: 999px;
    margin-right: variables(gutter) * 0.5;

    &:first-child {
      border-radius: 4px;
    }
  }

  .textWrapper {
    flex-direction: row;
    margin-bottom: variables(gutter) * 0.25;
  }
  .wrapperClassName {
    width: 100%;
    padding: variables(gutter);
    border-radius: 0;
    margin: variables(gutter) 0;
  }
  .skeletonTitle {
    width: 120px;
    height: 20px;
  }
  .skeletonSubTitle {
    width: 60px;
    height: 20px;
  }

  @media (min-width: breakpoints(tablet)) {
    .wrapperClassName {
      margin: variables(largeGutter) 0 variables(xLargeGutter) * 0.5 0;
      padding: 12px variables(largeGutter);
      border-radius: 12px;
    }

    .jobsListWithDetails {
      flex-direction: row;
      height: 100%;
    }

    .list {
      flex: 1;
      overflow-y: auto;
      max-width: calc(
        variables(contentMaxWidth) * 0.5 - variables(largeGutter)
      );
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .details {
      display: flex;
      padding-left: variables(largeGutter);
      padding-top: variables(largeGutter);
      flex: 1.3;
      overflow: hidden;
    }

    .linksRootShrink {
      flex-direction: row;
      padding: 0 variables(largeGutter);
      position: sticky;
    }

    .mobileHeader {
      display: none;
    }

    $varR: variables(wideContentMaxWidth);
    .searchFilterContent {
      padding: 0;
      height: 64px;
      max-width: min($varR, 100%);
      margin: 0 auto;
      flex: 1;
    }

    .contentRoot {
      padding: 0 variables(largeGutter);
    }

    .backBtn {
      display: flex;
      width: 30px;
      margin-right: variables(gutter) * 0.5;
    }

    .rightWrapper {
      display: flex;
      padding-left: variables(gutter);
    }

    .maxWidth {
      place-self: center;
      padding-left: variables(largeGutter) !important;
      padding-right: variables(largeGutter) !important;
    }
  }
}
