import React from 'react';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import FollowButton from 'shared/components/molecules/FollowButton/FollowButton';
import FollowingButton from 'shared/components/molecules/FollowingButton/FollowingButton';
import SendMessageButton from 'shared/components/molecules/SendMessageButton';
import { useSchedulesCalendar } from 'shared/hooks/useSchedulesCalendar';
import type { UserType } from 'shared/types/user';
import { Time } from '@/shared/utils/Time';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import classes from './JobCreatedBy.actions.module.scss';

interface JobMeetActionBottomProps {
  creator: UserType;
  onSuccess: (arg: any) => void;
}

const JobCreatedByActionBottom = ({
  creator,
  onSuccess,
}: JobMeetActionBottomProps) => {
  const { openCreateEventWithDate } = useSchedulesCalendar();
  const follow = creator?.network?.follow;

  const userObject = {
    isPage: false,
    id: creator.id,
    username: creator.username,
    name: creator?.fullName,
  };
  const handleSetUpMeeting = () => {
    openCreateEventWithDate(Time.getToday(), {
      schedulesEventType: ScheduleEventTypes.MEETING,
      attendees: [creator],
    });
  };

  return (
    <Flex flexDir="row" className={classes.followersItemRoot}>
      <SendMessageButton
        object={{
          fullName: creator?.fullName,
          croppedImageUrl: creator.croppedImageUrl,
          id: creator.id,
          owner: creator.id,
          username: creator.username,
        }}
        className={classes.fullWidth}
      />

      <Flex className={classes.divider} />

      {follow ? (
        <FollowingButton
          onSuccess={onSuccess({ follow: false })}
          object={userObject}
          className={classes.fullWidth}
        />
      ) : (
        <FollowButton
          onSuccess={onSuccess({ follow: true })}
          object={userObject}
          className={classes.fullWidth}
        />
      )}

      <Flex className={classes.divider} />

      <IconButton
        name="calendar-alt"
        size="md15"
        colorSchema="semi-transparent"
        type="far"
        onClick={handleSetUpMeeting}
      />
    </Flex>
  );
};

export default JobCreatedByActionBottom;
