'use client';

import type { JSX } from 'react';
import React from 'react';
import Button from 'shared/uikit/Button';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import { useSchedulesCalendar } from 'shared/hooks/useSchedulesCalendar';
import type { CalendarEvent } from 'shared/types/schedules/schedules';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import ScheduleSection from './partials/components/ScheduleSection';

const ProfileSchedules = (): JSX.Element => {
  const { t } = useTranslation();

  const { handleEventClick } = useSchedulesCalendar();
  const onClickHandler = (event: CalendarEvent) => () => {
    handleEventClick(undefined, event as any);
  };

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeDashboardTab]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <ScheduleSection
        title={t('up_meetings')}
        schedulesEventType={ScheduleEventTypes.MEETING}
        emptyProps={{
          caption: t('no_meetings_sch'),
          message: t('you_dont_any_upcoming_meetings_sho_y'),
          action: {
            title: t('create_meeting'),
          },
        }}
        action={() => (
          <Button
            schema="semi-transparent"
            fullWidth
            label={t('join_meeting')}
          />
        )}
      />
      <ScheduleSection
        title={t('scheduled_reminders')}
        schedulesEventType={ScheduleEventTypes.REMINDER}
        emptyProps={{
          caption: t('no_reminders_set'),
          message: t('y_dnt_h_a_reminder'),
          action: {
            title: t('create_reminder'),
          },
        }}
        action={(event) => (
          <Button
            onClick={onClickHandler(event)}
            schema="semi-transparent"
            fullWidth
            label={t('see_details')}
          />
        )}
      />
      <ScheduleSection
        title={t('open_tasks')}
        schedulesEventType={ScheduleEventTypes.TASK}
        emptyProps={{
          caption: t('no_tasks'),
          message: t('y_dnt_h_any_task_assi'),
          action: {
            title: t('create_task'),
          },
        }}
        action={(event) => (
          <Button
            onClick={onClickHandler(event)}
            schema="semi-transparent"
            fullWidth
            label={t('see_details')}
          />
        )}
      />
    </PermissionsGate>
  );
};

export default ProfileSchedules;
