'use client';

import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SchedulesSections from '@app/schedules/partials/SchedulesSections';
import Button from 'shared/uikit/Button';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';

interface SchedulesSectionsProps {
  schedulesEventType: ScheduleEventTypes;
}

const eventType = `${ScheduleEventTypes.HOLIDAY},${ScheduleEventTypes.BIRTHDAY}`;

const SchedulesEvents: React.FC<SchedulesSectionsProps> = () => {
  const { t } = useTranslation();

  return (
    <SchedulesSections
      schedulesEventType={eventType}
      firstSectionProps={{
        title: t('up_events'),
        emptyComponent: {
          caption: t('no_upcoming_events_sch'),
          message: t('you_dont_any_upcoming_events_sho_y'),
        },
      }}
      secondSectionProps={{
        title: t('past_events'),
        tooltipText: t('w_r_go_p_events'),
      }}
      emptyState={{
        caption: t('no_events_sch'),
        message: t('you_dont_any_events_sho_y'),
      }}
    />
  );
};

export default SchedulesEvents;
