'use client';

import React from 'react';
import Flex from 'shared/uikit/Flex';
import useTranslation from 'shared/utils/hooks/useTranslation';
import SchedulesSections from '@app/schedules/partials/SchedulesSections';
import Button from 'shared/uikit/Button';
import { detailsLabels } from 'shared/constants/schedules';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';

interface SchedulesSectionsProps {
  schedulesEventType: ScheduleEventTypes;
}

const SchedulesMeetings: React.FC<SchedulesSectionsProps> = () => {
  const { t } = useTranslation();

  return (
    <SchedulesSections
      schedulesEventType={ScheduleEventTypes.MEETING}
      firstSectionProps={{
        title: t('up_meetings'),
        action: () => (
          <Button
            schema="semi-transparent"
            fullWidth
            label={t(detailsLabels[ScheduleEventTypes.MEETING])}
          />
        ),
        emptyComponent: {
          caption: t('no_upcoming_meetings_sch'),
          message: t('you_dont_any_upcoming_meetings_sho_y'),
          action: {
            title: t('create_meeting'),
          },
        },
      }}
      secondSectionProps={{
        title: t('past_meetings'),
        tooltipText: t('w_r_go_p_meetings'),
        action: () => (
          <Button
            schema="semi-transparent"
            fullWidth
            label={t(detailsLabels[ScheduleEventTypes.MEETING])}
          />
        ),
        emptyComponent: {
          caption: t('no_meetings_sch'),
          message: t('you_dont_any_meetings_sho_y'),
        },
      }}
      emptyState={{
        caption: t('no_meetings_sch'),
        message: t('you_dont_any_meetings_sho_y'),
        action: { title: t('create_meeting') },
      }}
    />
  );
};

export default SchedulesMeetings;
