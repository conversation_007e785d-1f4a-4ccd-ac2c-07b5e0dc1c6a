import React, { useCallback, useMemo, useState } from 'react';
import ScheduleEventCard from 'shared/components/molecules/ScheduleEventCard';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import ViewPortList from 'shared/uikit/ViewPortList';
import Skeleton from 'shared/uikit/Skeleton';
import cnj from 'shared/uikit/utils/cnj';
import Tabs from 'shared/components/Organism/Tabs';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { detailsLabels } from 'shared/constants/schedules';
import { SCHEDULE_CALENDAR_RIGHT_SIDE_WRAPPER } from 'shared/constants/enums';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import { type CalendarEvent } from 'shared/types/schedules/schedules';
import { useSchedulesCalendar } from 'shared/hooks/useSchedulesCalendar';
import { useUpcomingAndPastEvents } from 'shared/hooks/useUpcomingAndPastEvents';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import classes from './SchedulesCloseEventList.module.scss';

function SchedulesCloseEventList() {
  const { t } = useTranslation();
  const { handleEventClick } = useSchedulesCalendar();
  const [activeTab, setActiveTab] = useState<'past' | 'upcoming' | ''>(
    'upcoming'
  );

  const { events, isLoading } = useUpcomingAndPastEvents(activeTab);
  const handleClick = useCallback(
    (event: CalendarEvent) => () => {
      handleEventClick(undefined, event);
    },
    [handleEventClick]
  );

  const isEmpty = !isLoading && !events?.length;

  const EmptyState = useMemo(
    () =>
      activeTab === 'upcoming' ? (
        <EmptySectionInModules
          {...{
            title: t('no_upcoming_scheduled_items'),
            text: t('you_not_have_upcoming_schedule_items'),
          }}
          classNames={{
            container: classes.emptyStateContainer,
          }}
        />
      ) : (
        <EmptySectionInModules
          {...{
            title: t('no_past_scheduled_items'),
            text: t('you_not_have_past_schedule_items'),
          }}
          classNames={{
            container: classes.emptyStateContainer,
          }}
        />
      ),
    [activeTab, t]
  );

  const itemContentCallback = useCallback(
    (index: number) => {
      const event = events[index];
      return (
        <Flex className={classes.cardWrapper}>
          {isLoading ? (
            <Skeleton className={classes.skeleton} />
          ) : (
            <ScheduleEventCard
              event={event}
              disableOnClick
              noUserPopper
              className={classes.wrapper}
              action={
                <Button
                  label={t(
                    [
                      ScheduleEventTypes.HOLIDAY,
                      ScheduleEventTypes.BIRTHDAY,
                    ].includes(event.type)
                      ? 'celebrate'
                      : detailsLabels[event.type]
                  )}
                  onClick={handleClick(event)}
                  schema="semi-transparent2"
                />
              }
            />
          )}
        </Flex>
      );
    },
    [events, handleClick, isLoading, t]
  );

  const CardsList = useMemo(
    () =>
      isEmpty ? (
        EmptyState
      ) : (
        <ViewPortList
          totalCount={isLoading ? 4 : events.length}
          itemContent={itemContentCallback}
          style={{ width: '100%' }}
          useRelativeScroller
          customScrollParent={
            document.getElementById(SCHEDULE_CALENDAR_RIGHT_SIDE_WRAPPER) ||
            undefined
          }
        />
      ),
    [isEmpty, EmptyState, isLoading, events.length, itemContentCallback]
  );
  const tabs = useMemo(
    () =>
      [
        {
          path: 'upcoming',
          title: t('upcoming'),
          content: () => CardsList,
        },
        {
          path: 'past',
          title: t('past'),
          content: () => CardsList,
        },
      ] as const,
    [CardsList, t]
  );

  return (
    <Tabs
      onChangeTab={setActiveTab}
      styles={{
        linksRoot: classes.linksRoot,
        tabsRoot: classes.flexGrow,
        tabsWrap: classes.flexGrow,
        content: cnj(classes.flexGrow, classes.tabContent),
      }}
      activePath={activeTab || tabs[0].path}
      tabs={tabs}
    />
  );
}

export default SchedulesCloseEventList;
