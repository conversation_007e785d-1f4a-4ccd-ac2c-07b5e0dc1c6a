'use client';

import { useEffect } from 'react';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';

const CreateSchedule = () => {
  const { setScheduleEventsPanelData: setScheduleCreationModalData } =
    useSchedulesUrlState();
  useEffect(() => {
    setScheduleCreationModalData(
      {
        isInCrEdit: true,
        schedulesEventType: ScheduleEventTypes.MEETING,
      },
      {
        replace: true,
      }
    );
  }, []);
  return null;
};

export default CreateSchedule;
