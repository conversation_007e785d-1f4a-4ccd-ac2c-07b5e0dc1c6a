'use client';

import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Container from '@app/settings/partials/components/SettingsHandler/SettingsHandler';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import PreferencesSingleItem from 'shared/components/Organism/PreferencesModal/PreferencesSingleItem';
import { pageSettingsTextTemplates } from 'shared/components/layouts/SidebarLayout/sidebarLayout.data';
import classes from './page.module.scss';

const Templates = (): React.JSX.Element => {
  const { t } = useTranslation();

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeSettingsScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Container title={t('text_templates')}>
        {Object.values(pageSettingsTextTemplates).map((item, index) => (
          <PreferencesSingleItem
            key={`preferences-item-${index}`}
            {...item}
            classNames={{ container: classes.itemContainer }}
          />
        ))}
      </Container>
    </PermissionsGate>
  );
};

export default Templates;
