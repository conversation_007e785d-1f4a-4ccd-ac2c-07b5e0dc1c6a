'use client';

import React, { type MouseEvent } from 'react';
import { useRouter } from 'next/navigation';
import {
  isValidTemplateCategoryType,
  type TemplateCategoryType,
} from '@shared/utils/api/template';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Container from '@app/settings/partials/components/SettingsHandler/SettingsHandler';
import { SCOPES } from 'shared/constants/userRoles.scopes';
import PermissionDeniedAlert from 'shared/components/Organism/PermissionDeniedAlert';
import PermissionsGate from 'shared/components/molecules/PermissionsGate';
import useTextTemplates from '@shared/utils/hooks/useTextTemplates';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import Flex from '@shared/uikit/Flex';
import ModalFooter from '@shared/uikit/Modal/ModalFooter';
import Button from '@shared/uikit/Button';
import EmptySectionInModules from '@shared/uikit/EmptySectionInModules/EmptySectionInModules';
import { routeNames } from '@shared/utils/constants';
import TemplatePreviewCard from './details/partials/TemplatePreviewCard';
import TemplatePreviewCardSkeleton from './details/partials/TemplatePreviewCard.skeleton';
import classes from './page.module.scss';

function getPageDataFromParams(category: TemplateCategoryType) {
  return { title: category };
}

export default function page({
  params,
}: {
  params: { category: string };
  searchParams: undefined;
}) {
  const router = useRouter();
  const { t } = useTranslation();
  const category = params?.category;
  if (!isValidTemplateCategoryType(category)) {
    router.replace('/404');
    return null;
  }
  const handleEdit = (id: string) => (event?: MouseEvent<any>) => {
    router.push(routeNames.settingsTextTemplates.makeCrEditRoute(category, id));
  };
  const handleCreate = (event?: MouseEvent<any>) => {
    router.push(routeNames.settingsTextTemplates.makeCrEditRoute(category));
  };
  const { title } = getPageDataFromParams(category);
  const { list, isLoading, setSearchKeyword } = useTextTemplates(category);

  return (
    <PermissionsGate
      scopes={[SCOPES.canSeeSettingsScreen]}
      renderFallback={<PermissionDeniedAlert />}
    >
      <Container
        title={t(title)}
        className={classes.container}
        contentWrapperClassNameDesktop={classes.parentContentWrapper}
      >
        <Flex className={classes.contentWrapper}>
          <SearchInputV2 onChange={setSearchKeyword} debounceTime={5000} />
          {isLoading ? (
            Array(4).map((_, idx) => (
              <TemplatePreviewCardSkeleton key={`skeleton-${idx}`} />
            ))
          ) : !list?.length ? (
            <EmptySectionInModules
              isFullParent
              title={t('no_templates_found')}
            />
          ) : (
            <Flex className={classes.listWrapper}>
              {list?.map((item, idx) => (
                <TemplatePreviewCard
                  category={category}
                  key={`template-preview-card-${item?.id || idx}`}
                  {...item}
                  onClick={handleEdit(item.id)}
                />
              ))}
            </Flex>
          )}
        </Flex>
        <ModalFooter>
          <Button
            schema="semi-transparent"
            label={t('create')}
            onClick={handleCreate}
          />
        </ModalFooter>
      </Container>
    </PermissionsGate>
  );
}
