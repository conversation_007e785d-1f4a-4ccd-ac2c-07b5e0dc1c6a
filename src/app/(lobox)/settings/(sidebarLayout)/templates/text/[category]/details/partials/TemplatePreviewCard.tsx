import React, { type MouseEvent } from 'react';
import Text from '@shared/components/molecules/Text/Text';
import type {
  TemplateCategoryType,
  BETemplate,
} from '@shared/utils/api/template';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Typography from '@shared/uikit/Typography';
import BaseButton from '@shared/uikit/Button/BaseButton';
import { ToggleDefaultTemplateButton } from './ToggleDefaultTemplateButton';
import { pageSettingsTextTemplates } from '@shared/components/layouts/SidebarLayout/sidebarLayout.data';
import Flex from '@shared/uikit/Flex';

type TemplatePreviewCardProps = Partial<BETemplate> & {
  id: string;
  default?: boolean;
  category: TemplateCategoryType;
  onClick: (e: MouseEvent<any>) => void;
};

export default function TemplatePreviewCard({
  id,
  title,
  subject,
  category,
  default: isDefault,
  onClick,
}: TemplatePreviewCardProps) {
  const { t } = useTranslation();
  const hasDefaultButton = pageSettingsTextTemplates[category].hasDefault;

  return (
    <BaseButton
      onClick={onClick}
      className=" p-12 bg-gray_5 hover:bg-hoverPrimary rounded-s border border-techGray_20"
    >
      {title && (
        <Typography font="700" size={20} height={24} mb={12} color="smoke_coal">
          {title}
        </Typography>
      )}

      <Flex flexDir="row" alignItems="center" className="justify-between">
        <Text isRichText label={`${t('subject')}:`} value={subject} />
        {hasDefaultButton && (
          <ToggleDefaultTemplateButton
            id={id}
            category={category}
            isDefault={isDefault}
          />
        )}
      </Flex>
    </BaseButton>
  );
}
