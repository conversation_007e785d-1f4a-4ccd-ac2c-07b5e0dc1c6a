import React from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import Button from '@shared/uikit/Button';
import useTranslation from '@shared/utils/hooks/useTranslation';
import {
  toggleDefaultTemplate,
  TemplateCategoryType,
} from '@shared/utils/api/template';

import QueryKeys from '@shared/utils/constants/queryKeys';

type ToggleDefaultTemplateButtonProps = {
  id: string;
  isDefault?: boolean;
  category: TemplateCategoryType;
};

export const ToggleDefaultTemplateButton = ({
  id,
  category,
  isDefault = false,
}: ToggleDefaultTemplateButtonProps) => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const { mutate, isPending } = useMutation({
    mutationFn: toggleDefaultTemplate,
  });

  const onToggle = (e: React.MouseEvent<HTMLElement> | undefined) => {
    e?.stopPropagation();
    mutate(
      { id, category, isDefault },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: [QueryKeys.searchAllTemplates, category, ''],
          });
        },
      }
    );
  };

  return (
    <Button
      onClick={onToggle}
      variant="thin"
      schema={isDefault ? 'success-semi-transparent' : 'semi-transparent'}
      isLoading={isPending}
      leftIcon="check"
      label={isDefault ? t('default') : t('set_as_default')}
    />
  );
};
