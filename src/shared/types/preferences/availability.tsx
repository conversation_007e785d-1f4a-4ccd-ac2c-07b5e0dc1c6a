import type {
  timeOptions,
  timeOptionsEnd,
} from 'shared/utils/constants/enums/schedulesDb';
import type {
  rawAttendeePermissionsOptions,
  useScheduleFormFieldOptions,
} from 'shared/hooks/schedules/useScheduleFormFieldOptions';
import type { DailyHour, Timesheet, Timezone, WeekDay } from './preferences';
import type { DateType } from '../schedules/schedules';
import type { MenuItem } from '../components/Menu.type';
import type { UserType } from '../user';
import type { UseAvailabilityReturn } from './availability.types';

export interface AvailabilityCardProps {
  timesheet: Timesheet;
  onClick?: (ts: Timesheet) => void;
  onEdit?: (ts: Timesheet) => void;
  onDelete?: (ts: Timesheet) => void;
  onShare?: (ts: Timesheet) => void;
}

export interface CrEditAvailabilityPanelContentProps {
  onSuccess: VoidFunction;
  timesheet?: Timesheet;
  isHeadless?: boolean;
  onClose?: VoidFunction;
}

// UseAvailabilityReturn is now defined in availability.types.ts

export interface AvailabilityDetailsPanelProps
  extends Pick<UseAvailabilityReturn, 'availabilityUser' | 'timesheet'> {
  onOpenAvailabilityCrEdit: UseAvailabilityReturn['openAvailabilityCrEdit'];
  onOpenHoursPanel: UseAvailabilityReturn['openHoursPanel'];
  onDeleteTimesheet: (ts: Timesheet) => void;
  isLoading?: boolean;
  isLoadingCreatorUser?: boolean;
  isBooking?: boolean;
  timezone: Timezone;
  onSetTimezone: UseAvailabilityReturn['setDisplayingTimezone'];
  isHeadless?: boolean;
}

export interface AvailabilityDetailsProps {
  selectedDate?: DateType;
  timesheet: Timesheet;
  timezone: Timezone;
  onSetTimezone: (timezone: Timezone) => void;
  creatorUser?: UserType;
  isLoadingCreatorUser?: boolean;
  avatarMenuItems?: MenuItem[];
  onCellClick?: (date: DateType) => void;
  onTodayClick?: VoidFunction;
  todayInBottom?: boolean;
}

export interface AvailabilityPanelContentProps extends AvailabilityListProps {
  isLoading: boolean;
  isHeadless?: boolean;
  hideBack?: boolean;
  hideClose?: boolean;
  title?: string;
}

export interface AvailabilityListProps {
  timesheets: Timesheet[];
  onOpenAvailabilityCrEdit: (ts?: Timesheet) => void;
  onOpenAvailabilityDetails: (ts: Timesheet) => void;
  onDeleteTimesheet: (ts: Timesheet) => void;
  onShareTimesheet: (ts: Timesheet) => void;
}

export interface CrEditAvailabilityFormContentProps {
  timesheet?: Timesheet;
  formPage: 1 | 2;
}

export type AttendeePermissionsOptions = ReturnType<
  typeof useScheduleFormFieldOptions
>['attendeePermissionsOptions'];

export type RawAttendeePermissionsOptions =
  typeof rawAttendeePermissionsOptions;

export type WeekDayInAvailability = {
  [weekDays in WeekDay]?: DailyHour[];
};

export type TimeOptionValue = (typeof timeOptions)[number]['value'];
export type TimeOptionEndValue = (typeof timeOptionsEnd)[number]['value'];

export interface CrEditFormHoursGroupProps {
  name: string;
  value?: Timesheet['dailyHours'][WeekDay];
  [s: string]: any;
}

export interface BookedTime {
  id: string;
  end: string;
  start: string;
}

// Re-export UseAvailabilityProps from availability.types.ts
export type { UseAvailabilityProps } from './availability.types';
