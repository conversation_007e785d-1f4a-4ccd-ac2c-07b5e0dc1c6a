import type { useAvailability } from '@shared/components/Organism/AvailabilityModule/partials/useAvailability';

export interface UseAvailabilityProps {
  timesheetId?: string | 'default';
  userId?: string;
  rawSelectedDay?: string;
  rawSelectedHour?: string;
}

export interface BookedTime {
  id: string;
  end: string;
  start: string;
}

export type UseAvailabilityReturn = ReturnType<typeof useAvailability>;

// Types needed by useAvailability hook
export type {
  DailyHour,
  Timesheet,
  Timezone,
  WeekDay,
  BETimesheet,
} from './preferences';
