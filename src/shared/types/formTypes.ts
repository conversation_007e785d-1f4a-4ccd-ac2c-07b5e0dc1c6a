import type { ComponentProps } from 'react';
import type { FormikErrors } from 'formik';

export type CallbackParams = {
  step: number;
  setStep: React.Dispatch<React.SetStateAction<number>>;
  values?: any;
  isSingleStep?: boolean;
  dirty?: boolean;
  resetForm?: () => any;
  setValues?: (x: any) => any;
  setFieldValue?: (
    field: string,
    value: any,
    shouldValidate?: boolean | undefined
  ) => Promise<void | FormikErrors<any>>;
  isSubmitting?: boolean;
  isValid?: boolean;
  status?: Record<any, any>;
  validateForm?: (values?: any) => Promise<FormikErrors<any>>;
  submitForm?: () => Promise<any>;
  setFieldTouched?: (arg: any) => void;
  resetBody?: () => any;
};

export type MultiStepFormFooterProps = Pick<
  CallbackParams,
  | 'isSubmitting'
  | 'setStep'
  | 'isValid'
  | 'status'
  | 'validateForm'
  | 'step'
  | 'values'
  | 'setValues'
> & {
  isSubmitStep?: boolean;
  jobId?: string;
};

export interface MultiStepFormStepProps {
  stepKey: string;
  getHeaderProps?: (x: CallbackParams) => ComponentProps<any>;
  getStepHeaderProps: (x: CallbackParams) => {
    title?: string;
    iconProps?: ComponentProps<any>;
    renderTitle?: (x: CallbackParams) => React.ReactNode;
    setFieldValue?: (
      field: string,
      value: any,
      shouldValidate?: boolean | undefined
    ) => Promise<void | FormikErrors<any>>;
  };
  renderBody: (x: CallbackParams) => React.ReactNode;
  renderFooter: (
    x: CallbackParams & { isSubmitStep?: boolean }
  ) => React.ReactNode;
  getValidationSchema?: (x: CallbackParams) => any;
  onSuccess?: (x: CallbackParams) => void;
  formName?: string;
  url?: string;
  transform?: (x: CallbackParams) => Function;
}

export type FormNames =
  | 'profileAboutEdit'
  | 'editProfileSections'
  | 'resumeUpload'
  | 'createPageForm'
  | 'invitePeople'
  | 'submitToVendor'
  | 'submitJob'
  | 'jobApplication'
  | 'createProjectForm'
  | 'createJobForm'
  | 'createCandidateForm'
  | 'linkJobForm'
  | 'deleteEntityModal'
  | 'checkout'
  | 'submitToClientForm'
  | 'addCompany'
  | 'submitJobModal';
