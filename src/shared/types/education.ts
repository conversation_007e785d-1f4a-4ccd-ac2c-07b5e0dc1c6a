import type { BELocation } from 'shared/types/lookup';

export type Education = {
  schoolPageId: string;
  schoolName: string;
  majorName: string;
  majorLookupId: string;
  degree: string;
  startDate: string;
  originalId?: string;
  endDate: string;
  id: string;
  description: string;
  currentlyStudying: boolean;
  pageCroppedImageUrl?: string;
  location?: BELocation;
  recruiterData?: boolean;
};
