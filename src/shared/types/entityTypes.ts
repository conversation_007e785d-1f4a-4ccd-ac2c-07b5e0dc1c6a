import type { ComponentProps } from 'react';

export type ObjectCardVariantType =
  | 'contactInfo'
  | 'message'
  | 'simple'
  | 'portalItem'
  | 'fullWidth'
  | 'business';

export interface BaseObjectDetail {
  id: string;
  isPage: boolean;
  usernameAtSign?: string;
  croppedImageUrl?: string;
  location?: { title: string };
}

export interface PageObjectDetail extends BaseObjectDetail {
  isPage: true;
  title?: string;
  category?: any;
}

export interface PersonObjectDetail extends BaseObjectDetail {
  isPage: false;
  fullName?: string;
  occupation?: any;
}

export type ObjectDetail = PageObjectDetail | PersonObjectDetail;

export const MoreMenuKeys = {
  shareVP: 'shareVp',
  shareMSG: 'shareMSG',
  remove: 'remove',
  unfollow: 'unfollow',
  block: 'block',
  report: 'report',
  recommend: 'recommend',
} as const;

export const PageOwnersForbiddenActions = ['report', 'block'] as const;

export type MoreMenuItemType = {
  key: keyof typeof MoreMenuKeys;
  iconName: string;
  label: string;
  onClick: (...args: any[]) => any;
};

export interface CardObjectProps {
  title?: string;
  name?: string;
  surname?: string;
  followStatus?: string;
  unfollowSuccess?: (...args: any[]) => any;
  block?: (...args: any[]) => any;
  category?: { value: string; label: string };
}

export interface UnitSectionItemProps {
  item: any;
  avatarProps?: any;
  fallbackData?: any;
  isLoading?: boolean;
  noLinkOnClick?: boolean;
  className?: string;
  variant?: ObjectCardVariantType;
  moreInfoAction?: React.ReactNode;
  visibleHorizontalInfoBox?: boolean;
  visibleActionButtons?: boolean;
}

export interface EntitiyPopperProps {
  username: string;
  classNames?: {
    popper?: string;
    menu?: string;
    buttonWrapper?: string;
  };
  isPage?: boolean;
  popperMenuProps?: any;
  placement?: ComponentProps<any>['placement'];
  useId?: boolean;
  fallbackData?: {
    name?: string;
    usernameAtSign?: string;
    icon?: string;
    isPage?: boolean;
  };
  unitSectionItemClassName?: string;
  variant?: ObjectCardVariantType;
  onClick?: (e: any) => void;
  showRole?: boolean;
}

export interface EntityPopperState {
  isTabletAndLess: boolean;
  isLoggedIn: boolean;
}
