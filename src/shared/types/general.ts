import type { IconName } from '@shared/uikit/Icon/types';
import type { APP_ENTITIES } from './app-entities';

export type ValueOf<T> = T[keyof T];

export type DateVersionType = {
  version?: string;
  createdDate?: Date;
  lastModifiedDate?: Date;
};

export type ValueLabelType<T = any> = {
  value?: T;
  label?: string;
};

export type ValueLabelIconType<T = any> = Required<ValueLabelType<T>> & {
  icon: IconName;
  type?: 'far' | 'fas';
  tooltip?: string;
};

export type GenderType =
  | 'MALE'
  | 'FEMALE'
  | 'NON_BINARY'
  | 'TRANSGENDER'
  | 'GAY'
  | 'LESBIAN'
  | 'BISEXUAL'
  | 'ASEXUAL'
  | 'PREFER_NOT_TO_SAY';

export type AgeRangeType =
  // | 'UNDER_18'
  'A' | 'B' | 'C' | 'D' | 'E' | 'F';
// | 'PREFER_NOT_TO_SAY';

export type RaceType =
  | 'PREFER_NOT_TO_SAY'
  | 'ASIAN'
  | 'HISPANIC'
  | 'NATIVE_AMERICAN'
  | 'WHITE'
  | 'MIDDLE_EASTERN'
  | 'BLACK';

export type CriminalRecordType =
  | 'TRAFFIC_VIOLATIONS'
  | 'EXPUNGED_RECORDS'
  | 'CLEAN_RECORD'
  | 'FELONIES'
  | 'COMPLETED_PROBATION'
  | 'MISDEMEANORS'
  | 'UNDER_INVESTIGATION';

export type IDDocTypes =
  | 'TRANSCRIPTS'
  | 'EML_FILE'
  | 'PASSPORT'
  | 'DRIVING_LICENCE'
  | 'RESUME'
  | 'SSN'
  | 'DIPLOMA';

export type NoticePeriodType =
  | 'IMMEDIATELY'
  | 'ONE_WEEK'
  | 'TWO_WEEKS'
  | 'ONE_MONTH'
  | 'TWO_MONTHS'
  | 'THREE_MONTHS'
  | 'MORE_THAN_THERE_MONTHS';

export type CandidateStatusType = 'YES' | 'NO' | 'PREFER_NOT_TO_SAY';

export type VeteranStatusType = 'YES' | 'NO' | 'PREFER_NOT_TO_SAY';

export type RelocationStatusType = 'YES' | 'NO' | 'PREFER_NOT_TO_SAY';

export type DisabilityStatusType = 'Yes' | 'No' | 'PREFER_NOT_TO_SAY';

export type AppEntitiesType = ValueOf<typeof APP_ENTITIES>;

export type TimeFormatType = '24' | '12';
export type TimeFormat = 'HH:mm' | 'h:mm A';

export type DateFormatType = 'LONG' | 'SHORT' | 'YMD' | 'YDM' | 'MDY' | 'DMY';
export type DateFormat =
  | 'MMM DD, YYYY'
  | 'MMM D YYYY'
  | 'YYYY/MM/DD'
  | 'YYYY/DD/MM'
  | 'MM/DD/YYYY'
  | 'DD/MM/YYYY';

export type JobSiteType =
  | 'NAUKRI'
  | 'INDEED'
  | 'FACEBOOK'
  | 'GLASSDOOR'
  | 'LINKED_IN'
  | 'CAMPUS_PORTAL'
  | 'POST_JOB_FREE'
  | 'EMPLOYEE_REFERRAL'
  | 'WINDOWS_PLUGIN'
  | 'PASSIVE_SOURCING'
  | 'ADZUNA'
  | 'TWITTER'
  | 'RESUME_INBOX'
  | 'WALK_IN'
  | 'OTHERS'
  | 'JOBSITE'
  | 'DICE'
  | 'ZIPRECRUITER'
  | 'CRAIGLIST'
  | 'OFFICE_365'
  | 'TECH_FETCH'
  | 'EMPLOYMENT_AGENCY'
  | 'STACK_OVERFLOW'
  | 'CAREER_BUILDER'
  | 'INTERNAL_JOB_POSTING'
  | 'JOB_FAIR'
  | 'OUTLOOK'
  | 'REFERRAL_PORTAL'
  | 'USER_REFERRAL'
  | 'SCHOOL_SPRING'
  | 'MONSTER'
  | 'GMAIL'
  | 'CAREER_PORTAL'
  | 'NEUVOO';

export type LanguageListType = 'english' | 'turkish';

export type QueryKeyType = string | Array<string>;

export type PartialRecord<K extends keyof any, T> = Partial<Record<K, T>>;

export type PublicSettingType = DateVersionType & {
  id: string;
  dateFormat: DateFormatType;
  language: LanguageListType;
  timeFormat: TimeFormatType;
  hideProfileOnSearchEngines: boolean;
  profileVisibilityToNonLoboxVisitors: boolean;
  visibleBio: boolean;
  visibleCollections: boolean;
  visibleCourses: boolean;
  visibleCoverPhoto: boolean;
  visibleCurrentExperience: boolean;
  visibleEducation: boolean;
  visibleEmail: boolean;
  visibleFeed: boolean;
  visibleHashtags: boolean;
  visibleHonorAwards: boolean;
  visibleJobTitle: boolean;
  visibleLanguages: boolean;
  visibleLink: boolean;
  visibleLocation: boolean;
  visiblePages: boolean;
  visiblePastExperience: boolean;
  visiblePatent: boolean;
  visiblePhone: boolean;
  visibleProfilePhoto: boolean;
  visiblePublications: boolean;
  visibleRecommendations: boolean;
  visibleResume: boolean;
  visibleSkills: boolean;
  theme: null;
};
