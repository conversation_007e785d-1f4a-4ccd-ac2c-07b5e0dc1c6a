import type {
  DETAILED_PAGE_ROLES,
  PAGE_MEMBER_STATUS,
  PAGE_ROLES,
} from 'shared/utils/constants/enums';
import type { ProfileImageType } from 'shared/types/profile';
import type { AppEntitiesType, ValueLabelType } from './general';
import type { ILocation, BELocation } from './lookup';
import type { UserType } from './user';

interface IPageLocation {
  name: string;
  primaryLocation: boolean;
  location: ILocation;
}

interface IPageLocationApi extends BELocation {
  primaryLocation: any;
  name: string;
}

export type PageCategoryType =
  | 'BASIC'
  | 'ARTIST'
  | 'COMMUNITY'
  | 'COMPANY'
  | 'EDUCATION_INSTITUTION'
  | 'INSTITUTION'
  | 'NON_PROFIT_ORGANIZATION'
  | 'PUBLIC_FIGURE'
  | 'PUBLIC_SERVICE';
export type PageCompanySizeType = 'A' | 'B' | 'C' | 'D' | 'E' | 'F' | 'G' | 'H';
export type PageStatusType = 'UNPUBLISHED' | 'PUBLISHED';
export type PageMemberStatusType = keyof typeof PAGE_MEMBER_STATUS;
export type RoleType = keyof typeof PAGE_ROLES;
export type DetailedRoleType = (typeof DETAILED_PAGE_ROLES)[number];
export type MembershipType = {
  createdDate: Date;
  id: string;
  lastModifiedDate: Date;
  role: RoleType;
  userId: string;
  version: number | string;
};
export type NetworkModelType = {
  back: boolean;
  backStatus: 'ACCEPTED' | 'PENDING';
  follow: boolean;
  followersCounter: string;
  followingsCounter: string;
  followStatus?: any;
  hasAnyHashtagFollowings: boolean;
  hasAnyPageFollowings: boolean;
  postsCounter: string;
  croppedHeaderImageLink: string | null;
  locationLat: string | null;
  locationLong: string | null;
  locationTitle: string | null;
  privateProfile: string | null;
};

export type PageApiResponseType = {
  category?: PageCategoryType;
  companySize?: PageCompanySizeType;
  createdDate?: Date;
  croppedHeaderImageUrl?: string;
  croppedImageUrl?: string;
  description?: string;
  email?: string;
  establishmentDate: Date;
  headerImageUrl?: string;
  id: string;
  imageUrl?: string;
  industryLookupId?: string;
  industryName?: string;
  areaOfInterestLookupId?: string;
  areaOfInterestName?: string;
  lastModifiedDate?: Date;
  link?: string;
  locations: Array<IPageLocationApi>;
  phone?: string;
  status?: PageStatusType;
  title?: string;
  username?: string;
  version?: string;
  myMemberships?: Array<MembershipType>;
  pageNetworkModel?: NetworkModelType;
  hasAnyPublicCollection: boolean;
  adultContent: boolean;
  allowOthersToShareYourPosts: boolean;
  allowOthersToTagOrMentionYou: boolean;
  youHaveBlocked: boolean;
  ownerId?: boolean;
  hideIt?: boolean;
  languageName?: string;
  languageLookupId?: string;
};

export type BeforeCachePageDetailType = Omit<
  PageApiResponseType,
  | 'category'
  | 'companySize'
  | 'industryLookupId'
  | 'industryName'
  | 'areaOfInterestLookupId'
  | 'areaOfInterestName'
  | 'locations'
  | 'pageNetworkModel'
> & {
  category?: ValueLabelType<PageCategoryType>;
  companySize?: ValueLabelType<PageCompanySizeType>;
  title: string;
  usernameAtSign: string;
  pageLink: string;
  industry: ValueLabelType<string>;
  areaOfInterest: ValueLabelType<string>;
  locations: Array<IPageLocation>;
  location: ILocation;
  network?: NetworkModelType;
  hideIt?: boolean;
  type?: AppEntitiesType;
  isPage?: boolean;
  language: ValueLabelType<string>;
  profileImages?: ProfileImageType;
};

export type FollowType = {
  id: string;
};

export type PageType = {
  back: boolean;
  follow: boolean;
  category: PageCategoryType;
  croppedImageUrl: string;
  followersCounter: string;
  followingsCounter: string;
  mutualCounter: string;
  id: string;
  imageUrl: string;
  point: string;
  pointMultiplier: string;
  score: string;
  status: PageStatusType;
  title: string;
  description?: string;
  username?: string;
  hideIt?: boolean;
  postsCounter?: string;
  locationTitle?: string;
  croppedHeaderImageLink?: string;
  ownerId?: string;
  lastModifiedDate?: Date;
  createdDate?: Date;
  location?: BELocation;
};

export interface IPage extends PageType {
  fullName: string;
  subTitle: string;
}

export type PageMembershipType = {
  id: string;
  userId: UserType['id'];
  pageId: UserType['id'];
  role: RoleType;
  status: PageMemberStatusType;
  userIsOwnerOfPage?: boolean;
};

export interface PortalAccessType {
  id: string;
  userId: UserType['id'];
  pageId: UserType['id'];
  role: DetailedRoleType;
  portal: 'USER' | 'EDITOR' | 'RECRUITER' | 'SERVICE' | 'CAMPAIGN' | 'SALES';
}

interface PlanType {
  id: string;
  pageId: UserType['id'];
  planId: string;
  startDate: string;
  endDate: string;
}

export interface PageAccessibilityType extends PageType {
  croppedImageData: string | null;
  headerImageUrl: string | null;
  croppedHeaderImageUrl: string | null;
  croppedHeaderImageData: string | null;
  pageMemberships: PageMembershipType[];
  portalAccesses: PortalAccessType[];
  currentEnrolledPlans: PlanType[];
}
export interface PageFullAccessibilitiesReturnType {
  userId: UserType['id'];
  profileInfo: null;
  accessibilities: PageAccessibilityType[];
}

export interface BEPagePlan {
  id: string;
  pageId: string;
  planId: string;
  startDate: string;
  endDate: string;
}

interface PlanFeature {
  featureName: 'BASIC';
  value: 'string';
}
export type PlanTimeSpan = 'WEEKLY' | 'MONTHLY' | 'ANNUALLY' | 'UNLIMITED';
export type PaymentRequestType =
  | 'BUY_PLAN'
  | 'ADD_SEAT'
  | 'REFUND_SEAT'
  | 'PUBLISH_JOB';
export interface BEPlan {
  price: number;
  planName: string;
  features: PlanFeature[];
  active: boolean;
}
export interface BEBilling {
  id: string;
  price: number;
  numberOfSeats: number;
  requestType: string;
  billingType?: PaymentRequestType;
  endDate: string; // date ISO
  planName: string;
  timeSpan: PlanTimeSpan;
  taxAmount: number | null;
  status: string;
  remainingDays: number;
  invoiceNumber: string;
  last4digits: string | null;
  paymentDate: string; // date YYYY-MM-DD
  nameOnCard?: string;
}
