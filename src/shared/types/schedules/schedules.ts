import type dayjs from 'dayjs';
import type {
  ConferenceProviderName,
  ProviderData,
  ProviderType,
} from 'shared/components/molecules/EventsIntegration/utils/type';
import type {
  BETimezone,
  BookedMeeting,
  Timesheet,
  WeekDay,
} from 'shared/types/preferences';
import type {
  getBirthdayDetails,
  getHolidayDetails,
  getMeetingDetails,
  getReminderDetails,
  getTaskDetails,
} from 'shared/utils/api/schedules';
import type { contactType } from 'shared/utils/constants/enums/schedulesDb';
import type { BELocation } from 'shared/types/lookup';
import type {
  getCalendarEventsNormalizer,
  normalizeNormalizedCalendarEvent,
} from 'shared/utils/normalizers/schedules';
import type { Dayjs } from 'dayjs';
import type { UnwrapPromise } from 'next/dist/lib/coalesced-function';
import type { schedulesDb } from 'shared/utils/constants';
import type { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import type { UserApiResponse, UserType } from '../user';

// Keep migrating all schedule related types in here

export type CreatableSchedulesEventTypes =
  | ScheduleEventTypes.MEETING
  | ScheduleEventTypes.TASK
  | ScheduleEventTypes.REMINDER;

export type NonCreatableSchedulesEvents =
  | ScheduleEventTypes.OFF_DAY
  | ScheduleEventTypes.HOLIDAY
  | ScheduleEventTypes.BIRTHDAY;

export type BEScheduleEventTypes =
  | ScheduleEventTypes.MEETING
  | ScheduleEventTypes.TASK
  | ScheduleEventTypes.REMINDER
  | ScheduleEventTypes.HOLIDAY
  | ScheduleEventTypes.BIRTHDAY;

export type CalendarBoardType = 'day' | 'week' | 'month' | 'year';

export type DateType = ReturnType<typeof dayjs>;

export type SavedEventType = Omit<NormalizedCalendarEvent, 'type'> & {
  type: ScheduleEventTypes;
};

// TODO: What is this doing in schedules?
export type UnArray<ArrType> = ArrType extends (infer ElementType)[]
  ? ElementType
  : never;

export type Data = {
  filteredEvents: ScheduleEventTypes[];
  availableWeekDays?: WeekDay[];
  viewDate: {
    start: DateType;
    dayOfWeek: string;
    dayOfWeekShortenedLabel: string;
    dayNumberInMonth: string | number;
    monthAndYearLabel: string;
  };
  savedEvents: SavedEventType[];
  isLoaded?: boolean;
};

export type DayUnit = CalendarBoardType | 'hour';

export type EventCreationFromCalendarInitialData = Partial<{
  startDate?: string;
  startTime?: { label: string; value: string };
  endDate?: string;
  endTime?: { label: string; value: string };
  schedulesEventType?: CreatableSchedulesEventTypes;
  [x: string]: any;
}>;

export interface EventNotFoundProps {
  eventType: ScheduleEventTypes;
}

export interface BETaskAssignee {
  modifyPermission: boolean;
  user: UserApiResponse;
  userId: UserType['id'];
}

export enum TaskStatus {
  OPEN = 'OPEN',
  DONE = 'DONE',
  IN_PROGRESS = 'IN_PROGRESS',
}

export enum MeetingAttendeePermission {
  MODIFY_MEETING = 'MODIFY_MEETING',
  INVITE_OTHERS = 'INVITE_OTHERS',
  SEE_OTHER_ATTENDEES = 'SEE_OTHER_ATTENDEES',
}

export enum EventPermissions {
  VIEW = 'VIEW',
  SHARE = 'SHARE',
  EDIT = 'EDIT',
  DELETE = 'DELETE',
  REPORT = 'REPORT',
}

export type MeetingAttendeeStatus = 'WAIT' | 'ACCEPT' | 'DECLINE';

export interface BEMeetingAttendee {
  email: string | null;
  id: string;
  permissions: MeetingAttendeePermission[];
  status: MeetingAttendeeStatus;
  userId: UserApiResponse['id'];
  userModel: UserApiResponse;
}

export enum MeetingDuration {
  _15_MINUTES = '_15_MINUTES',
  _30_MINUTES = '_30_MINUTES',
  _60_MINUTES = '_60_MINUTES',
  _120_MINUTES = '_120_MINUTES',
}

export enum MeetingReminderDuration {
  _5_MIN_BEFORE = '_5_MIN_BEFORE',
  _10_MIN_BEFORE = '_10_MIN_BEFORE',
  _15_MIN_BEFORE = '_15_MIN_BEFORE',
  _30_MIN_BEFORE = '_30_MIN_BEFORE',
  _1_HOUR_BEFORE = '_1_HOUR_BEFORE',
  _1_DAY_BEFORE = '_1_DAY_BEFORE',
}

export enum MeetingType {
  GROUP = 'GROUP',
  ONE_TO_ONE = 'ONE_TO_ONE',
}

export enum ContactType {
  REMOTE = 'REMOTE',
  ON_SITE = 'ON_SITE',
}

export type MeetingModelOptions = typeof contactType;

export enum MeetingChannel {
  LOBOX_ACCOUNT = 'LOBOX_ACCOUNT',
  PERSONAL_ACCOUNT = 'PERSONAL_ACCOUNT',
  CUSTOM_LINK = 'CUSTOM_LINK',
}

export enum MeetingDatetimeType {
  PROVIDE_AVAILABILITY = 'PROVIDE_AVAILABILITY',
  CONSUME_AVAILABILITY = 'CONSUME_AVAILABILITY',
  FIXED_TIME = 'FIXED_TIME',
}

export interface MeetingChannelOption {
  label: ProviderData<ProviderType.Conference>['externalUserName'];
  value: ProviderData<ProviderType.Conference>;
}

export interface BEBaseCalendarEvent {
  id: string;
  title: string;
  description: string | null;
  start: string;
}

export interface BEReminder extends BEBaseCalendarEvent {
  allDay: boolean;
  creator: UserApiResponse;
  currentUserIsCreator: boolean;
  end: string;
  type: ScheduleEventTypes.REMINDER;
}

export type ReminderStatus = 'OPEN' | 'DONE' | 'ALARM' | 'CANCEL';
// TODO: need to change BEBase to remove description from it
export interface BEReminderDetails extends BEBaseCalendarEvent {
  end: string;
  allDay: boolean;
  creator: UserApiResponse;
  currentUserIsCreator: true;
  status: ReminderStatus;
}
export interface BETaskDetails extends BEBaseCalendarEvent {
  allDay: boolean;
  assignee?: BETaskAssignee;
  attachmentFileIds: string[] | null;
  creator: UserApiResponse;
  currentUserIsCreator: boolean;
  end: string;
  status: TaskStatus;
}

export interface BETask extends BETaskDetails {
  type: ScheduleEventTypes.TASK;
}

export interface BEMeeting extends BEBaseCalendarEvent, BETimezone {
  attendees: BEMeetingAttendee[];
  creator: UserApiResponse;
  customLink: string | null;
  conferenceLink: string | null;
  contactType: ContactType;
  currentUserIsCreator: boolean;
  end: string;
  // meetingType: MeetingType;
  type: ScheduleEventTypes.MEETING;
}

export interface BEMeetingDetails extends BEBaseCalendarEvent, BETimezone {
  attachmentFileIds: [];
  attendees: BEMeetingAttendee[];
  customLink: string | null;
  conferenceLink: string | null;
  contactType: ContactType;
  creator: UserApiResponse;
  currentUserIsCreator: boolean;
  duration: MeetingDuration;
  externalConferenceProviderType: ConferenceProviderName | null;
  externalConferenceProviderId: number | null;
  location: BELocation | null;
  remind: MeetingReminderDuration;
  title: string;
  locationDetails: string | null;
}

export interface BaseScheduleEvent {
  id: string;
  title: string;
  schedulesEventType: ScheduleEventTypes;
  openDetailModal?: VoidFunction;
  startDate: string;
  startTime: string;
  timezone: { value: string; label: string; offset: string };
  currentUserIsCreator: boolean;
  creator: UserType;
}

export interface BEHoliday extends Omit<BEBaseCalendarEvent, 'description'> {
  end: string;
  type: ScheduleEventTypes.HOLIDAY;
}
export interface BEHolidayDetails {
  id: string;
  name: string;
  countryCode: string | null;
  location: BELocation | null;
  date: string | null;
}

export interface BEBirthday extends Omit<BEBaseCalendarEvent, 'description'> {
  user: UserApiResponse;
  type: ScheduleEventTypes.BIRTHDAY;
}
export interface BEBirthdayDetails {
  id: string;
  user: UserApiResponse;
  date: string;
  congratulationMessageSent: boolean;
}

export interface ICreateMeetingData {
  attachmentFileIds: Array<string>;
  description: string;
  duration: MeetingDuration;
  // meetingType: MeetingType;
  endTimezone: string;
  externalConferenceProviderType: keyof typeof schedulesDb.meetingTool;
  id?: string;
  attendees: Array<{ id: string }>;
  remind: MeetingReminderDuration;
  startDate: string;
  startTime: string;
  timezoneId: string;
  title: string;
}

export type EventDetails =
  | MeetingDetails
  | TaskDetails
  | ReminderDetails
  | HolidayDetails
  | BirthDayDetails;

export type MeetingDetails = UnwrapPromise<
  ReturnType<typeof getMeetingDetails>
>;

export type TaskDetails = UnwrapPromise<ReturnType<typeof getTaskDetails>>;
export type ReminderDetails = UnwrapPromise<
  ReturnType<typeof getReminderDetails>
>;
export type BirthDayDetails = UnwrapPromise<
  ReturnType<typeof getBirthdayDetails>
>;
export type HolidayDetails = UnwrapPromise<
  ReturnType<typeof getHolidayDetails>
>;

export interface ICreateReminderData {
  allDay: true;
  date: string;
  isRepeated: boolean;
  repeatDuration: number;
  repeatEndsAfterOccurrences: number;
  repeatEndsNever: boolean;
  repeatEndsOnDate: string;
  repeatOnDays: Array<keyof typeof schedulesDb.reminderRepeat>;
  repeatPeriod: keyof typeof schedulesDb.reminderRepeat;
  repeatType: keyof typeof schedulesDb.reminderRepeat;
  time: string;
  title: string;
}

export interface ICreateTaskData {
  assigneeId: string;
  attachmentFileIds: Array<string>;
  categoryId: Array<string>;
  collaborationIds: Array<string>;
  description: string;
  endDate: string;
  endTime: string;
  hashtags: Array<string>;
  startDate: string;
  startTime: string;
  title: string;
}

export interface Reminder extends BaseScheduleEvent {
  scheduleEventType: ScheduleEventTypes.REMINDER;
  allDay: boolean;
  repeat: {
    days: string[];
    duration: string;
    endsAfterOccurrences: string;
    endsNever: boolean;
    endsOnDate: string;
    id: string;
    period: string;
    type: string;
  };
}

export type CalendarEvent = ReturnType<
  typeof getCalendarEventsNormalizer
>[number];

export type CalendarEventByType<T extends ScheduleEventTypes> = Extract<
  CalendarEvent,
  { type: T }
>;

export type MeetingCalendarEvent =
  CalendarEventByType<ScheduleEventTypes.MEETING>;
export type ReminderCalendarEvent =
  CalendarEventByType<ScheduleEventTypes.REMINDER>;
export type HolidayCalendarEvent =
  CalendarEventByType<ScheduleEventTypes.HOLIDAY>;
export type BirthdayCalendarEvent =
  CalendarEventByType<ScheduleEventTypes.BIRTHDAY>;
export type TaskCalendarEvent = CalendarEventByType<ScheduleEventTypes.TASK>;
export type OffDayCalendarEvent =
  CalendarEventByType<ScheduleEventTypes.OFF_DAY>;

export type NormalizedCalendarEvent = ReturnType<
  typeof normalizeNormalizedCalendarEvent
>;

export type BECreatableCalendarEvent = BEMeeting | BETask | BEReminder;
export type BECalendarEvent = BECreatableCalendarEvent | BEHoliday | BEBirthday;

export interface SetScheduleStateOptions {
  replace?: boolean;
}
export interface ScheduleEventsPanelData {
  isInCrEdit?: boolean;
  isInCandidateManager?: boolean;
  schedulesEventType?: ScheduleEventTypes;
  eventId?: string;
  isFromNotification?: boolean;
  creationInitialData?: EventCreationFromCalendarInitialData;
  isInAttendeeSelection?: boolean;
  isInAttendeesView?: boolean;
  isInIntegrations?: boolean;
}

export interface ScheduleAvailabilityPanelData {
  isInAvailabilities?: boolean;
  isInDetails?: boolean;
  isInCrEdit?: boolean;
  selectedDay?: Dayjs;
  selectedHour?: Dayjs;
  timeSheetId?: Timesheet['id'];
  userId?: string;
}

export interface ScheduleUrlState {
  scheduleEventsPanelData?: ScheduleEventsPanelData;
  scheduleAvailabilityPanelData?: ScheduleAvailabilityPanelData;
}

export interface AvailableHoursPanelProps {
  timesheet: Timesheet;
  day: Dayjs;
  bookedMeetings?: BookedMeeting[];
  isSelectMode?: boolean;
  onHourClick?: (time: Dayjs) => void;
  isHeadless?: boolean;
}

export interface AvailableHoursListProps {
  timesheet: AvailableHoursPanelProps['timesheet'];
  day: AvailableHoursPanelProps['day'];
  bookedMeetings?: BookedMeeting[];
  isSelectMode?: AvailableHoursPanelProps['isSelectMode'];
  className?: string;
  onHourClick?: AvailableHoursPanelProps['onHourClick'];
  classNames?: {
    container?: string;
    button?: string;
  };
}

export interface ScheduleDetailsCreatorCardProps {
  event: MeetingDetails | ReminderDetails | TaskDetails;
}
