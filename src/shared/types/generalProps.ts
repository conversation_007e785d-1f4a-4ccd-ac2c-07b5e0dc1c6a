import type { JobSkillProps, LanguageProps } from './jobsProps';
import type { CategoryType } from './lookup';
import type { ProjectProps } from './project';

export interface CreateEntityModalProps<T> {
  data?: T;
}

export type DBStaticItemProps<T> = {
  label: string;
  value?: T;
};

export interface FiltersDataProps {
  titles: string[];
  categories: CategoryType[];
  cities: { code: string; name: string }[];
  skills: JobSkillProps[];
  languages: LanguageProps[];
  travelRequirements?: string[];
  projects: Pick<ProjectProps, 'id' | 'title'>[];
  salaryRange: {
    currencyCode: {};
    id: string;
    max: string;
    min: string;
    period: string;
  };
  clientIds?: { id: string; title: string }[];
  vendorIds?: { id: string; title: string }[];
  collaborators: { id: string; name: string; surname: string }[];
  pointOfContacts: { id: string; name: string; surname: string }[];
  creators: { id: string; name: string; surname: string }[];
  owners: { id: string; name: string; surname: string }[];
  workAuthorization: { title: string; id: string }[];
  tags: string[];
  hashtags: string[];
  jobs: { id: string; title: string }[];
  statuses: string[];
}

export type RemindDurationType =
  | '_5_MIN_BEFORE'
  | '_10_MIN_BEFORE'
  | '_15_MIN_BEFORE'
  | '_30_MIN_BEFORE'
  | '_1_HOUR_BEFORE'
  | '_1_DAY_BEFORE';
