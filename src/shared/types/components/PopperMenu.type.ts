import type { PopperPlacementType } from '@mui/material';
import type { IconName } from 'shared/uikit/Icon/types';
import type { colorsKeys } from '@shared/uikit/helpers/theme';
import type { TextProps, TypographyProps } from '@shared/uikit/Typography';
import type { ReactNode, Ref } from 'react';

export interface PopperMenuProps {
  buttonComponent: ReactNode | ((isOpen: boolean) => ReactNode);
  popperContainerClassName?: string;
  bottomSheetClassName?: string;
  menuClassName?: string;
  children: React.ReactNode;
  offsetX?: number;
  offsetY?: number;
  clickCallBack?: (e: any) => void;
  placement?: PopperPlacementType;
  disablePortal?: boolean;
  hasArrow?: boolean;
  closeOnScroll?: boolean;
  showWithHover?: boolean;
  hoverDelay?: number;
  closeDelay?: number;
  noDrawer?: boolean;
  onClose?: () => void;
  onCloseOutside?: () => void;
  onOpen?: () => void;
  disableCloseOnClickOutSide?: boolean;
  disableCloseOnClickInSide?: boolean;
  noBottomSheetForMobile?: boolean;
  popperWidth?: (width: number) => number;
  classNames?: {
    arrow: string;
    [x: string]: string;
  };
  useInside?: boolean;
  disabled?: boolean;
  ref?: Ref<any>;
}

export interface PopperItemProps {
  iconName?: IconName;
  label: string;
  onClick?: any;
  link?: string;
  iconType?: 'far' | 'fas' | 'fal';
  isSelected?: boolean;
  secondaryLabel?: string;
  labelClassName?: string;
  labelsContainerClassName?: string;
  className?: string;
  labelTruncated?: boolean;
  secondLabelTruncated?: boolean;
  iconSize?: number;
  iconColor?: colorsKeys;
  iconClassName?: { wrapper?: string; icon?: string };
  action?: React.ReactNode | (() => React.ReactNode);
  secondaryLabelProps?: Omit<TypographyProps, 'children'> & {
    showTooltip?: boolean;
  };
  labelProps?: TextProps<any>;
  secondaryLabelSize?: number;
  secondaryLabelColor?: colorsKeys;
  disabled?: boolean;
}
