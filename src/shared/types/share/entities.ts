import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';

export enum ShareEntities {
  RESUME = 'resume',
  PROFILE = 'profile',
  POST = 'post',
  PAGE = 'page',
  JOB = 'job',
  CANDIDATE = 'candidate',
  AVAILABILITY = 'availability',
  MEETING = ScheduleEventTypes.MEETING,
  REMINDER = ScheduleEventTypes.REMINDER,
  TASK = ScheduleEventTypes.TASK,
  BIRTHDAY = ScheduleEventTypes.BIRTHDAY,
  HOLIDAY = ScheduleEventTypes.HOLIDAY,
  PROJECT = ScheduleEventTypes.PROJECT,
}

export enum ShareEntityTab {
  COPY_LINK = 'copy_link',
  SHARE_VIA_POST = 'share_via_post',
  SHARE_VIA_MESSAGE = 'share_via_message',
  SHARE_VIA_EMAIL = 'share_via_email',
}
