import type { ILocation, ILocationAccess } from 'shared/types/lookup';
import type { AttachmentProps } from 'shared/types/attachment';
import type { PipelineItemProps } from 'shared/uikit/Pipelines';
import type { StageCardProps } from '@shared/components/atoms/containers/StageCard';
import type {
  ContractDurationType,
  DaysType,
  EducationDegreeType,
  EmploymentType,
  ExperienceLevelType,
  PeriodType,
  PipelineFollowUpPeriodType,
  PipelineProps,
  PipelineTemplateProps,
  PriorityType,
  ResponseTimeType,
  WillingToTravelType,
  WorkPlaceType,
} from './pipelineTypes';
import type { BeforeCachePageDetailType } from './page';
import type { ProjectProps } from './project';
import type { UserApiResponse, UserType } from './user';
import type { PeopleType } from './people';
import type { QuestionAPIFormProps, QuestionFormProps } from './questionsProps';
import type { PipelineInfo } from './pipelineProps';

export type JobStatusType = 'OPEN' | 'CLOSED' | 'UNPUBLISHED' | 'ARCHIVE';

export interface WorkAuthorizationProps {
  id: string;
  title: string;
  countryCode: string;
  country: string;
}

export interface JobBenefitProps {
  id: string;
  title: string;
}

export interface QuestionChoiceProps {
  answer: string;
  isIdealAnswer: boolean;
}
export interface QuestionProps {
  question: string;
  questionType: 'TEXT_INPUT' | 'MULTI_SELECT' | 'SINGLE_SELECT';
  isMustHave: boolean;
  idealAnswer: string;
  questionChoices: QuestionChoiceProps[];
  order: number;
}

export interface ISuggestedJob {
  title: string;
}

export interface IRecentJobSearch {
  id: string;
  alertModel?: any;
  lang: any;
  location: string;
  cityCode?: string;
  locationTitle?: string;
  stateCode?: string;
  countryCode?: string;
  searchBody: any;
  text: string;
  userId: string;
}

export interface JobSuggestionsForSearchResult {
  recentSearches: IRecentJobSearch[];
  trySearchForList: ISuggestedJob[];
}

export interface JobFormData {
  projectIds: string;
}

export interface JobLocationProps extends Omit<ILocation, 'label'> {
  id?: string;
  title?: string;
  label?: string;
  access?: ILocationAccess;
  districtCode?: string;
  stateCode?: string;
  countyCode?: string;
  detail?: string;
  preview?: boolean;
}

export type SkillLevelType =
  | 'BEGINNER'
  | 'INTERMEDIATE'
  | 'ADVANCED'
  | 'EXPERT';

export interface JobSkillProps {
  skillId: string;
  skillName: string;
  skillLevel: SkillLevelType;
}

export type LanguageLevelType =
  | 'A1'
  | 'A2'
  | 'B1'
  | 'B2'
  | 'C1'
  | 'C2'
  | 'Native';

export interface LanguageProps {
  languageId: number;
  languageLevel: LanguageLevelType;
  languageName: string;
}

export interface JobCollaboratorProps {
  croppedImageUrl: string;
  pointOfContact: boolean;
  name: string;
  surname: string;
  username: string;
  occupation: string;
  location: string;
}

export interface WorkAuthorizationValueProps {
  country: { value: string; label: string };
  authorization: { value: string; label: string };
}

export interface JobProps {
  id: string;
  title: string;
  pageId: string;
  pageTitle: string;
  pageCroppedImageUrl: string;
  pageIndustry: string;
  pageLocation: string;
  workPlaceType: WorkPlaceType;
  tags?: string[];
  priority: PriorityType;
  status: JobStatusType;
  creatorUser?: UserApiResponse;
  projects: ProjectProps[];
  categoryName: string;
  employmentType: EmploymentType;
  responseTime?: ResponseTimeType;
  numberOfHires?: number;
  rangeMin?: number;
  rangeMax?: number;
  period?: PeriodType;
  currencySymbol?: string;
  currencyCode?: string;
  taxTermName?: string;
  markup?: number;
  contractDuration?: ContractDurationType;
  travelRequirement?: WillingToTravelType;
  hoursPerDay?: number;
  hoursPerWeek?: number;
  location: JobLocationProps;
  description: string;
  hashtags?: string[];
  skills: JobSkillProps[];
  languages?: LanguageProps[];
  experienceLevel?: ExperienceLevelType;
  educationDegree?: EducationDegreeType;
  workDays: DaysType[];
  workAuthorizations?: WorkAuthorizationProps[];
  benefits?: JobBenefitProps[];
  collaborators: JobCollaboratorProps[];
  ownerFullName: string;
  ownerUsername: string;
  ownerPortalAccesses: any;
  lastModifiedDate: string;
  fileIds?: number[];
}

export interface CreateJobAPIDataProps {
  projectIds: string[];
  categoryId: string;
  categoryName: string;
  workPlaceType: WorkPlaceType;
  employmentType: EmploymentType;
  responseTime?: ResponseTimeType;
  skills: { skillId: string; skillName: string; skillLevel: string }[];
  collaborators: JobCollaboratorProps[];
  priority: PriorityType;
  workDays: DaysType[];
  titleId?: string;
  title: string;
  numberOfHires?: number;
  schedulePublishDateTime?: string;
  scheduleCloseDateTime?: string;
  location?: JobLocationProps;
  globalApplyAllowed?: boolean;
  external?: boolean;
  websiteUrl?: string;
  description?: string;
  languages?: LanguageProps[];
  hashtags?: string[];
  tags?: string[];
  attachments?: AttachmentProps[];
  emailRequired?: boolean;
  phoneRequired?: boolean;
  resumeRequired?: boolean;
  coverLetterRequired?: boolean;
  experienceLevel?: ExperienceLevelType;
  educationDegree?: EducationDegreeType;
  contractDuration?: ContractDurationType;
  travelRequirement?: WillingToTravelType;
  hoursPerDay?: number;
  hoursPerWeek?: number;
  timezoneId?: string;
  timezoneCode?: string;
  timezoneLabel?: string;
  timezoneOffset?: string;
  workAuthorizations?: WorkAuthorizationProps[];
  salaryCurrencyId?: string;
  salaryCurrencyName?: string;
  salaryCurrencySymbol?: string;
  salaryCurrencyCode?: string;
  salaryPeriod?: PeriodType;
  salaryRangeMin?: number;
  salaryRangeMax?: number;
  taxTermId?: string;
  taxTermName?: string;
  taxTerm?: number;
  markup?: string;
  benefits?: JobBenefitProps[];
  currencyId?: string;
  currencyName?: string;
  currencySymbol?: string;
  currencyCode?: string;
  pipelines?: PipelineProps[];
  questions: QuestionAPIFormProps[];
  totalScore?: number;
  maxWrongQuestionCount?: number;
  period?: PeriodType;
  rangeMin?: number;
  rangeMax?: number;
  fileIds?: string[];
}

export interface CreateJobFormDataProps
  extends Omit<
    CreateJobAPIDataProps,
    | 'projectIds'
    | 'categoryId'
    | 'categoryName'
    | 'workPlaceType'
    | 'employmentType'
    | 'responseTime'
    | 'skills'
    | 'collaborators'
    | 'priority'
    | 'workDays'
    | 'taxTerm'
    | 'taxTermId'
    | 'taxTermName'
    | 'salaryPeriod'
    | 'markup'
    | 'contractDuration'
    | 'travelRequirement'
    | 'salaryRangeMin'
    | 'salaryRangeMax'
    | 'salaryCurrencyName'
    | 'salaryCurrencySymbol'
    | 'salaryCurrencyCode'
    | 'experienceLevel'
    | 'educationDegree'
    | 'workAuthorizations'
    | 'benefits'
    | 'languages'
    | 'pipelines'
    | 'hoursPerDay'
    | 'hoursPerWeek'
    | 'timezoneId'
    | 'timezoneCode'
    | 'timezoneLabel'
    | 'timezoneOffset'
    | 'title'
  > {
  projects: ProjectProps[];
  externalLinkAdded?: boolean;
  category: { value?: string; label?: string; id?: string };
  workPlaceType: { value?: WorkPlaceType; label?: WorkPlaceType };
  employmentType: { value?: EmploymentType; label?: EmploymentType };
  responseTime?: { value?: ResponseTimeType; label?: string };
  skills: {
    id: string;
    label: string;
    skillLevel: string;
    progress: number;
    level: string;
  }[];
  languages?: {
    id: string;
    label: string;
    skillLevel: string;
    progress: number;
    languageLevel: LanguageLevelType;
  }[];
  collaboratorUsers?: { user: UserType; userId: string }[];
  priority: { value: PriorityType; label: string };
  workDays: { value: DaysType; label: string }[];
  tax_term?: { value: string; label: string };
  salaryPeriod?: { value?: PeriodType; label?: string };
  markup?: { value: string; label: string };
  contractDuration?: { value?: ContractDurationType; label: string };
  travelRequirement?: { value?: WillingToTravelType; label: string };
  min_salary?: string;
  max_salary?: string;
  currency?: {
    label?: string;
    symbol?: string;
    name?: string;
    code?: string;
    value?: string;
  };
  experienceLevel?: { value?: ExperienceLevelType; label: string };
  educationDegree?: { value?: EducationDegreeType; label: string };
  workAuthorizations?: WorkAuthorizationValueProps[];
  benefits?: { value: string; label: string }[];
  primary_collab?: { user: UserType; userId: string };
  stagesPipelines?: PipelineItemProps[];
  onboardingPipelines?: PipelineItemProps[];
  _stagesPipelines?: PipelineItemProps[];
  _onboardingPipelines?: PipelineItemProps[];
  timezone?: { value?: string; label?: string; code?: string; offset?: string };
  hoursPerDay?: { value?: number; label: string };
  hoursPerWeek?: { value?: number; label: string };
  title: { value?: string; label: string };
  questionsData?: JobQuestionsData;
  id?: string;
  taxTermData?: {
    value: number;
    label: string;
    name: string;
  };
}

export type JobPipelineTemplateProps = Omit<
  PipelineTemplateProps,
  'followupMessagePeriod'
> & {
  followupMessagePeriod: { value: PipelineFollowUpPeriodType; label: string };
};

export interface JobAPIProps {
  applicantsCount?: string;
  candidatesCount?: string;
  rejectionsCount?: string;
  category: string;
  categoryName?: string;
  collaboratorsCount?: string;
  createdDate: string;
  id: string;
  location: string;
  pageCroppedImageUrl: string;
  pageTitle: string;
  pageUsername: string;
  priority: PriorityType;
  projects: ProjectProps[];
  status: JobStatusType;
  title: string;
  userFullName: string;
  username: string;
  ownerUsername?: string;
  ownerFullName: string;
  lastModifiedDate: string;
  collaborators?: JobDetailsCollaboratorProps[];
  pipelines?: PipelineInfo[];
  tags?: string[];
}

export type SingleJobAPIProps = JobProps &
  Pick<
    JobAPIProps,
    | 'status'
    | 'priority'
    | 'createdDate'
    | 'pageUsername'
    | 'applicantsCount'
    | 'candidatesCount'
  >;

export type ApplicantType = Pick<
  PeopleType,
  | 'id'
  | 'username'
  | 'imageUrl'
  | 'croppedImageUrl'
  | 'name'
  | 'surname'
  | 'occupationId'
  | 'occupationName'
  | 'birthDate'
> & {
  email: string;
  location: {
    id: string;
    title: string;
  };
};

export interface CandidateType {
  id: string;
  profile: Pick<
    ApplicantType,
    | 'croppedImageUrl'
    | 'username'
    | 'occupationName'
    | 'occupationId'
    | 'name'
    | 'surname'
  > & { userId: string; fullName: string };
}

export interface ApplicantBaseType {
  id: string;
  jobId: string;
  jobTitle: string;
  dateTime: string;
  pipeline: PipelineProps;
  pipelines: PipelineProps[];
  tags: string[];
  skillBoard: {
    id: string;
    lastModificationUser: ApplicantType;
  };
  rejected: boolean;
  notesCount: number;
  todosCount: number;
  meetingsCount: number;
  avgScore: number;
  yearsOfExperience: number;
  lastDegree: EducationDegreeType;
  relatedSkillsCount: number;
  relatedLanguagesCount: number;
  matchingExpectationsCount: number;
}
export interface ApplicationProps extends ApplicantBaseType {
  applicant: ApplicantType;
}

export interface CandidateProps extends ApplicantBaseType {
  candidate: CandidateType;
}

export interface CandidateJobCardProps extends CandidateProps {
  id: string;
  fullName: string;
  image: string;
  location: string;
  occupation: string;
  username: string;
}

export type JobDetailsCollaboratorProps = Pick<
  UserType,
  | 'id'
  | 'name'
  | 'surname'
  | 'username'
  | 'location'
  | 'croppedImageUrl'
  | 'fullName'
  | 'occupation'
  | 'usernameAtSign'
> & { occupationName: string; portalAccesses: any };
export interface JobCollaboratorsProps {
  pointOfContact: JobDetailsCollaboratorProps;
  collaborators: JobDetailsCollaboratorProps[];
}

export interface JobCompanyProps
  extends Omit<BeforeCachePageDetailType, 'location'> {
  location: string;
  industryName: string;
  sharedImageUrls: string[];
}

export interface JobQuestionsData {
  totalScore: string;
  questions: QuestionFormProps[];
  maxWrongQuestionCount?: number;
  hasWAC?: boolean;
  list_name?: string;
}

export type AddQuestionsStepType = 'questions' | 'save';

export interface BaseJobDataForm {
  id: string;
}

export interface JobDataFormProjectsInfo extends BaseJobDataForm {
  projectIds: string[];
}
export interface JobDataFormBasicInfo extends Partial<BaseJobDataForm> {
  titleId?: string;
  title: string;
  categoryId: string;
  categoryName: string;
  workPlaceType: WorkPlaceType;
  employmentType: EmploymentType;
  responseTime?: ResponseTimeType;
  numberOfHires?: number;
  schedulePublishDateTime?: string;
  scheduleCloseDateTime?: string;
  location?: JobLocationProps;
  globalApplyAllowed?: boolean;
  external?: boolean;
  websiteUrl?: string;
}

export type JobDataCreate = JobDataFormBasicInfo &
  Omit<JobDataFormProjectsInfo, 'id'>;

export interface JobDataFormGeneralInfo extends BaseJobDataForm {
  description?: string;
  skills: { skillId: string; skillName: string; skillLevel: string }[];
  languages?: LanguageProps[];
  hashtags?: string[];
}

export interface JobDataFormManagement extends BaseJobDataForm {
  collaborators: JobCollaboratorProps[];
  priority: PriorityType;
  tags?: string[];
  fileIds?: string[];
}

export interface JobDataFormApplicationForm extends BaseJobDataForm {
  phoneRequired?: boolean;
  coverLetterRequired?: boolean;
}
export interface JobDataFormRequirements extends BaseJobDataForm {
  experienceLevel?: ExperienceLevelType;
  educationDegree?: EducationDegreeType;
  contractDuration?: ContractDurationType;
  travelRequirement?: WillingToTravelType;
  workDays: DaysType[];
  hoursPerDay?: number;
  hoursPerWeek?: number;
  timezoneId?: string;
  timezoneCode?: string;
  timezoneLabel?: string;
  timezoneOffset?: string;
  workAuthorizations?: WorkAuthorizationProps[];
}

export interface JobDataFormBenefits extends BaseJobDataForm {
  salaryCurrencyId?: string;
  salaryCurrencyName?: string;
  salaryCurrencySymbol?: string;
  salaryCurrencyCode?: string;
  salaryPeriod?: PeriodType;
  salaryRangeMin?: number;
  salaryRangeMax?: number;
  taxTermId?: string;
  taxTermName?: string;
  taxTerm?: number;
  markup?: string;
  benefits?: JobBenefitProps[];
}

export interface JobDataFormPipeline extends BaseJobDataForm {
  pipelines?: PipelineProps[];
}

export type JobParticipationModel = Pick<
  ApplicantBaseType,
  | 'id'
  | 'dateTime'
  | 'pipeline'
  | 'skillBoard'
  | 'rejected'
  | 'notesCount'
  | 'todosCount'
  | 'meetingsCount'
> & {
  type: 'APPLICANT' | 'CANDIDATE';
  job: JobAPIProps;
  applicant: ApplicantType;
  candidate: CandidateType;
  avgScore: number;
  hired: boolean;
  pinned: boolean;
  automated?: boolean;
  collaboratorSubmitted: StageCardProps.CollaboratorSubmitted;
  vendorSubmitted: StageCardProps.VendorSubmitted;
  repeated: boolean;
};

export type NormalizedJobParticipationModel = JobParticipationModel & {
  user: Pick<
    UserApiResponse,
    | 'id'
    | 'croppedImageUrl'
    | 'fullName'
    | 'occupationName'
    | 'username'
    | 'location'
  > & { usernameAtSign: string };
};

export interface CreateJobOptions {
  options: {
    subForm?: string;
    step?: number;
  };
}

export type PipelineStageSortBy = 'DATE' | 'AVERAGE_SCORE';

export interface PipelineStageFilter {
  sortBy: PipelineStageSortBy;
}
