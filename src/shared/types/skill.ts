import type { ISkillLevelEnum } from './job';

export type Skill = {
  id: number | string;
  name: string;
  level: string | { value: string; label: string };
  realData: {
    name: {
      label: string;
      type: string | null;
      value: string | null;
    };
    id: number | string;
    level: {
      value: string;
      label: string;
    };
    originalId?: string;
    progress: number;
    type: string | null;
  };
  progress: number;
  label: string;
  type: string | null;
  skillLevel: string;
  recruiterData?: boolean;
};

export type BESkill = {
  id: string;
  createdDate: Date;
  lastModifiedDate: Date;
  version: string;
  originalId: string;
  type: string | null;
  level: ISkillLevelEnum;
  skillLookupId: string | null;
  name: string;
};
