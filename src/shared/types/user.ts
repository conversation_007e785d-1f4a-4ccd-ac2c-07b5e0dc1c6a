import type { privacyValues } from 'shared/utils/constants/enums';
import { type APP_ENTITIES } from './app-entities';
import type {
  CollaboratorSubmitted,
  VendorSubmitted,
} from 'shared/components/atoms/containers/StageCard';
import type { NetworkModelType } from './page';
import type {
  DateVersionType,
  PublicSettingType,
  GenderType,
  ValueOf,
} from './general';
import type { BELocation } from './lookup';
import type beforeCacheUserInfo from '../utils/normalizers/beforeCacheUserInfo';

export interface ILoginResponse {
  accessToken: string;
  refreshToken: string;
  signUpCompleted: boolean;
  userId: string;
  username: string;
  app?: keyof typeof APP_ENTITIES;
}

export type PrivacyValueType = ValueOf<typeof privacyValues>;

export type UserApiResponse = DateVersionType & {
  id: string;
  birthDate?: Date;
  birthDateAccess: PrivacyValueType;
  allowNonLoboxUsersToFindYou?: boolean;
  hasPassword?: boolean;
  locationAccess: boolean;
  locationId: string;
  locationName: string;
  croppedImageUrl?: string;
  email?: string;
  emailAccess: PrivacyValueType;
  emailVerified?: boolean;
  imageUrl?: string;
  name: string;
  occupationId?: string;
  occupationName?: string;
  occupationPublic?: boolean;
  phone?: string;
  phoneAccess: PrivacyValueType;
  provider?: string;
  providerId?: string;
  signUpCompleted?: boolean;
  surname?: string;
  username: string;
  resumeLink?: string;
  personNetworkModel?: NetworkModelType;
  hasAnyPublicCollection: boolean;
  youHaveBlocked: boolean;
  youAreBlocked: boolean;
  gender?: GenderType;
  genderDescription?: string;
  publicSetting?: PublicSettingType;
  location: BELocation;
  croppedHeaderImageLink: string | null;
  deactivated?: boolean;
  mutualCounter: number | null;
  privateProfile: boolean;
  fullName?: string;
  occupation?: { label: string };
  collaboratorSubmitted: CollaboratorSubmitted;
  vendorSubmitted: VendorSubmitted;
  repeated: boolean;
};

export type UserType = ReturnType<typeof beforeCacheUserInfo<UserApiResponse>>;

export type UserTypeInList = Pick<
  UserType,
  | 'id'
  | 'email'
  | 'username'
  | 'imageUrl'
  | 'croppedImageUrl'
  | 'name'
  | 'surname'
  | 'location'
  | 'birthDate'
  | 'phone'
  | 'gender'
  | 'genderDescription'
> & {
  occupationId?: string;
  occupationName?: string;
  allowPageRoleAssign?: boolean;
  portalAccesses?: any;
};
