export type Language = {
  name: { label: string; value: string };
  level: { label: string; value: string };
  id?: string;
  description: string;
  createdDate: string;
  realData: {
    name: {
      label: string;
      type: string | null;
      value: string | null;
    };
    id: number | string;
    level: {
      value: string;
      label: string;
    };
    originalId?: string;
    progress: number;
    type: string | null;
  };
  recruiterData?: boolean;
};

export type IJobLanguage = {
  id: number;
  label: string;
  progress: number;
  level: string;
};
