import type { APP_ENTITIES } from './app-entities';
import type { ValueOf } from './general';

export type ContactStatusType =
  | 'INVITE'
  | 'MADEINVITATIONREQUEST'
  | 'FOLLOW'
  | 'MADEFOLLOWREQUEST'
  | 'PENDING'
  | 'MADEINVITATIONBYEMAIL'
  | 'ACCEPTED'
  | 'BLOCKED';

export type ContactFollowItemType = {
  createdDate: Date;
  email: string;
  family: string;
  follow: boolean;
  id: string;
  lastModifiedDate: Date;
  name: string;
  signUpCompleted: boolean;
  status: ContactStatusType;
  unsubscribed: boolean;
  userId: string;
  ownerProfileInfoId: string;
  version: string;
};

export type FailedInvitedType = {
  croppedImageUrl: 'string';
  email: 'string';
  name: 'string';
  surname: 'string';
  title: 'string';
  userType: ValueOf<typeof APP_ENTITIES>;
  username: 'string';
};

export type SendInvitationResponseType = {
  failureList: Array<FailedInvitedType>;
};

export type SendCandidateInvitationRequestBody = {
  email: string;
  sender: true;
  candidateId: string;
};

export enum InvitationEntityType {
  PEOPLE = 'PEOPLE',
  COMPANY = 'COMPANY',
}
