import type { colorsKeys } from 'shared/uikit/helpers/theme';

export type WorkPlaceType = 'ON_SITE' | 'HYBRID' | 'REMOTE';
export type EmploymentType =
  | 'FULL_TIME'
  | 'PART_TIME'
  | 'CONTRACT'
  | 'CONTRACT_TO_HIRE'
  | 'SELF_EMPLOYED'
  | 'FREELANCE'
  | 'TEMPORARY'
  | 'VOLUNTEER'
  | 'INTERNSHIP'
  | 'APPRENTICESHIP'
  | 'SEASONAL';
export type ResponseTimeType =
  | 'IMMEDIATE'
  | 'WITHIN_1_DAY'
  | 'WITHIN_2_DAYS'
  | 'WITHIN_3_DAYS'
  | 'WITHIN_1_WEEK'
  | 'WITHIN_2_WEEKS'
  | 'WITHIN_1_MONTH'
  | 'WITHIN_1_YEAR'
  | 'FLEXIBLE';
export type PriorityType = 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
export type DaysType =
  | 'MONDAY'
  | 'TUESDAY'
  | 'WEDNESDAY'
  | 'THURSDAY'
  | 'FRIDAY'
  | 'SATURDAY'
  | 'SUNDAY';
export type ExperienceLevelType =
  | 'ENTRY_LEVEL'
  | 'MIDDLE_LEVEL'
  | 'SENIOR_LEVEL'
  | 'PRINCIPAL_LEVEL'
  | 'ASSOCIATE'
  | 'DIRECTOR'
  | 'EXECUTIVE'
  | 'INTERNSHIP';
export type EducationDegreeType =
  | 'BACHELOR'
  | 'MASTER'
  | 'DOCTORAL'
  | 'ASSOCIATE'
  | 'POSTDOCTORAL'
  | 'MIDDLE_SCHOOL'
  | 'HIGH_SCHOOL'
  | 'ELEMENTARY'
  | 'PROFESSIONAL'
  | 'DIPLOMA'
  | 'CERTIFICATE'
  | 'HONORARY'
  | 'NON';
export type ContractDurationType =
  | 'THREE_MONTHS'
  | 'SIZ_MONTHS'
  | 'ONE_YEAR'
  | 'FULL_TIME';
export type WillingToTravelType =
  | 'YES'
  | 'NO'
  | 'OCCASIONALLY'
  | 'CONSIDERABLE';
export type PeriodType =
  | 'HOURLY'
  | 'DAILY'
  | 'WEEKLY'
  | 'BIWEEKLY'
  | 'MONTHLY'
  | 'YEARLY';

export type PipelineFollowUpPeriodType =
  | '_3_DAYS'
  | '_5_DAYS'
  | '_7_DAYS'
  | '_14_DAYS';

export type PipelineStageType = 'HIRING' | 'ONBOARDING';

export interface PipelineTemplateProps {
  autoReplyTitle?: string;
  autoReplyMessage?: string;
  hasFollowUpMessage?: boolean;
  followUpTitle?: string;
  followUpMessage?: string;
  followupMessagePeriod?: PipelineFollowUpPeriodType;
}

export interface PipelineProps extends PipelineTemplateProps {
  id?: string;
  title?: string;
  type?:
    | 'REVIEW'
    | 'INTERVIEW'
    | 'OFFERED'
    | 'HIRED'
    | 'ON_BOARDED'
    | 'CUSTOMIZE';
  applicantTrack?: boolean;
  order?: number;
  color?: colorsKeys;
  stageType?: PipelineStageType;
  customizeStageType?: PipelineStageType;
  dateTime?: string;
}

export type AutoReplyProps = Pick<
  PipelineProps,
  | 'autoReplyMessage'
  | 'autoReplyTitle'
  | 'followUpMessage'
  | 'followUpTitle'
  | 'followupMessagePeriod'
  | 'hasFollowUpMessage'
>;

export type TemplateDynamicValueType =
  | 'APPLICANT_NAME'
  | 'PAGE_NAME'
  | 'JOB_TITLE'
  | 'RECRUITER_NAME';
