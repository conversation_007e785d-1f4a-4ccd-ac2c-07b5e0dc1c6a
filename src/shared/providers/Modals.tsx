'use client';

import React, { useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  useGlobalDispatch,
  useGlobalState,
} from 'shared/contexts/Global/global.provider';
import {
  selectIsOpen,
  useNineDotPanelState,
} from '@shared/stores/nineDotPanelStore';
import { PresenceAnimationWrapper } from 'shared/components/molecules/PresenceAnimationWrapper';
import { useMultiStepFormState } from 'shared/hooks/useMultiStepForm';
import { useAsyncPickerModalProps } from '@shared/components/Organism/AsyncPickerModal';
import { useSearchParams } from 'next/navigation';
import SocialConnectionsModal from '@shared/components/Organism/SocialConnectionsModal';
import ListOfInvitationFailuresModal from '@shared/components/Organism/ListOfInvitationFailuresModal';
import ListOfInvitedPeople from '@shared/components/Organism/ListOfInvitedPeople';
import ShareEntityTabbedModal from '@shared/components/Organism/ShareEntityTabbedModal';
import ViewMedia from '@shared/components/Organism/Message/components/organism/ViewMedia';
import InvitePeopleMultiStepForm from '@shared/components/Organism/MultiStepForm/InvitePeople/InvitePeople';
import useLocation from '@shared/utils/hooks/useLocation';
import useMedia from '@shared/uikit/utils/useMedia';

const ProfileAboutMultiStepForm = dynamic(
  () =>
    import(
      'shared/components/Organism/MultiStepForm/ProfileAboutEdit/ProfileAboutMultiStepForm'
    ),
  { ssr: false }
);
const ProfileSectionsMultiStepForm = dynamic(
  () =>
    import(
      'shared/components/Organism/MultiStepForm/ProfileSections/ProfileSectionsMultiStepForm'
    ),
  { ssr: false }
);

const SubmitVendor = dynamic(
  () =>
    import(
      'shared/components/Organism/MultiStepForm/SubmitVendor/SubmitVendor'
    ),
  {
    ssr: false,
  }
);

const SubmitJob = dynamic(
  () => import('shared/components/Organism/MultiStepForm/SubmitJob/SubmitJob'),
  {
    ssr: false,
  }
);

const ProfileUploadResume = dynamic(
  () =>
    import(
      'shared/components/Organism/MultiStepForm/ProfileUploadResume/ProfileUploadResume'
    ),
  { ssr: false }
);

const CreateEntityPanel = dynamic(
  () => import('shared/components/Organism/CreateEntityPanel'),
  { ssr: false }
);

const DuplicationVendorsPanel = dynamic(
  () => import('shared/components/Organism/DuplicationVendorsPanel'),
  { ssr: false }
);

const NineDotPanel = dynamic(
  () => import('shared/components/Organism/NineDotPanel'),
  { ssr: false }
);

const CandidateManager = dynamic(
  () => import('shared/components/Organism/CandidateManager'),
  { ssr: false }
);

const CreateCandidateForm = dynamic(
  () => import('shared/components/Organism/MultiStepForm/CreateCandidateForm'),
  {
    ssr: false,
    loading: () => <>CreateCandidateForm is loading...</>,
  }
);

const AsyncPickerModal = dynamic(
  () => import('@shared/components/molecules/BaseAsyncPickerModal'),
  { ssr: false }
);

const PostReactionsModal = dynamic(
  () => import('@shared/components/Organism/FeedCard/modals/PostReactions'),
  { ssr: false }
);

const PostCommentsModal = dynamic(
  () => import('@shared/components/Organism/FeedCard/modals/Comments'),
  { ssr: false }
);

const NotificationModal = dynamic(
  () =>
    import('@shared/components/layouts/AppLayout/partials/NotificationModal'),
  { ssr: false }
);
const ConfirmBlockModal = dynamic(
  () => import('@shared/components/Organism/ConfirmBlockModal'),
  { ssr: false }
);
const CheckoutModal = dynamic(
  () => import('@shared/components/Organism/MultiStepForm/CheckoutForm'),
  { ssr: false }
);
const AutomationModal = dynamic(
  () => import('@shared/components/Organism/AutomationModal/AutomationModal'),
  { ssr: false }
);
const AutoMoveModal = dynamic(
  () => import('@shared/components/Organism/AutomationModal/AutoMoveModal'),
  { ssr: false }
);
const RequirementsModal = dynamic(
  () => import('@shared/components/Organism/AutomationModal/RequirementsModal'),
  { ssr: false }
);
const LinkedJobModal = dynamic(
  () => import('@shared/components/Organism/LinkedJobModal/LinkedJobModal'),
  { ssr: false }
);
const FeedbackModal = dynamic(
  () => import('shared/components/Organism/FeedbackModal'),
  {
    ssr: false,
  }
);
const CreatePostModal = dynamic(
  () => import('shared/components/Organism/CreatePostModal'),
  { ssr: false }
);
const ReportModal = dynamic(
  () => import('shared/components/Organism/ReportModal'),
  { ssr: false }
);
const CreateJobModalInUserProject = dynamic(
  () => import('shared/components/Organism/CreateJobModalInUserProject'),
  { ssr: false }
);
const ProfileMenu = dynamic(
  () => import('shared/components/layouts/AppLayout/AppLayout.ProfileMenu'),
  { ssr: false }
);
const CreateProjectFrom = dynamic(
  () => import('shared/components/Organism/MultiStepForm/CreateProjectForm'),
  {
    ssr: false,
  }
);
const CreateJobForm = dynamic(
  () => import('shared/components/Organism/MultiStepForm/CreateJobForm'),
  {
    ssr: false,
  }
);
const LinkJobForm = dynamic(
  () => import('shared/components/Organism/MultiStepForm/LinkJobForm'),
  {
    ssr: false,
  }
);

const DeleteEntityModal = dynamic(
  () => import('shared/components/Organism/MultiStepForm/DeleteEntityModal'),
  {
    ssr: false,
  }
);
const EditAssigneeModal = dynamic(
  () => import('@shared/components/molecules/EditAssigneeModal'),
  {
    ssr: false,
  }
);
const CompareModal = dynamic(
  () => import('shared/components/Organism/CompareModal'),
  {
    ssr: false,
  }
);

const SubmitToClientForm = dynamic(
  () => import('shared/components/Organism/MultiStepForm/SubmitToClientForm'),
  {
    ssr: false,
  }
);

const Modals = () => {
  const profileSectionsState = useMultiStepFormState('editProfileSections');
  const profileAboutEditState = useMultiStepFormState('profileAboutEdit');
  const profileResumeUploadState = useMultiStepFormState('resumeUpload');
  const createCandidateForm = useMultiStepFormState('createCandidateForm');
  const isNineDotPaneOpen = useNineDotPanelState(selectIsOpen);
  const candidateManager = useGlobalState('candidateManager');
  const isCreateEntityPanelOpen = useGlobalState('isCreateEntityPanelOpen');
  const isDuplicationVendorsPanelOpen = useGlobalState(
    'duplicationVendorsModal'
  )?.isOpen;
  const asyncPickerProps = useAsyncPickerModalProps();
  const commentModalData = useGlobalState('commentModalData');
  const searchParams = useSearchParams();
  const reactionsModalData = useGlobalState('reactionsModalData');
  const isOpenNotificationModal = useGlobalState('isOpenNotificationModal');
  const isOpenConfirmBlockModal = useGlobalState('confirmBlockModal')?.isOpen;
  const isEditProfileModalOpen = searchParams.get('openEditModal') === 'true';
  const checkoutModalState = useMultiStepFormState('checkout');
  const automationModalState = useGlobalState('automationModal');
  const autoMoveModalState = useGlobalState('autoMoveModal');
  const requirementsModalState = useGlobalState('requirementsModal');
  const { isOpen: isSubmitToVendorOpen } =
    useMultiStepFormState('submitToVendor');
  const isSubmitJobOpen = true;
  const isLinkedJobOpen = useGlobalState('isSettingModalOpen');
  const { pathname } = useLocation();
  const { isOpen: isInviteFormOpen } = useMultiStepFormState('invitePeople');
  const { isOpen: isCreateProjectopen, ...createProjectProps } =
    useMultiStepFormState('createProjectForm');
  const { isOpen: isCreateJobForm, data } =
    useMultiStepFormState('createJobForm');
  const { isOpen: isLinkJobFormopen, ...linkJobProps } =
    useMultiStepFormState('linkJobForm');
  const { isOpen: isDeleteEntitypen } =
    useMultiStepFormState('deleteEntityModal');
  const { isOpen: isSubmitToClientFormopen, ...submitToClientProps } =
    useMultiStepFormState('submitToClientForm');
  const globalDispatch = useGlobalDispatch();
  const objectNetworkModal = useGlobalState('objectNetworkModal');
  const isFeedbackModalOpen = useGlobalState('isFeedbackModalOpen');
  const isOpenProfilePanel = useGlobalState('isOpenProfilePanel');
  const { isMoreThanTablet } = useMedia();
  const { isOpen: isOpenReportModal } = useGlobalState('reportModal');
  const { isOpen: isOpenFailuresModal } = useGlobalState(
    'invitationFailuresModal'
  );
  const { isOpen: isOpenInvitationsList } = useGlobalState(
    'invitationsListModal'
  );
  const isOpenCreateJobModalInUserProject = useGlobalState(
    'isOpenCreateJobModalInUserProject'
  );
  const viewMedia = useGlobalState('viewMedia');
  const editAssigneeData = useGlobalState('editAssigneeModalData');

  const { isOpen: isShareEntityModalOpen } = useGlobalState(
    'shareEntityTabbedModal'
  );
  const compareModalData = useGlobalState('compareModal');

  useEffect(() => {
    if (commentModalData) {
      globalDispatch({
        type: 'TOGGLE_COMMENTS_MODAL',
        payload: undefined,
      });
    }
    if (reactionsModalData) {
      globalDispatch({
        type: 'TOGGLE_FEED_REACTIONS_MODAL',
        payload: undefined,
      });
    }
    if (isOpenNotificationModal) {
      globalDispatch({
        type: 'SET_IS_OPEN_NOTIFICATION_PANEL',
        payload: false,
      });
    }
  }, [pathname]);

  return (
    <>
      <PresenceAnimationWrapper isOpen={profileAboutEditState?.isOpen}>
        <ProfileAboutMultiStepForm />
      </PresenceAnimationWrapper>
      {profileResumeUploadState?.isOpen && <ProfileUploadResume />}
      <PresenceAnimationWrapper
        isOpen={profileSectionsState?.isOpen || isEditProfileModalOpen}
      >
        <ProfileSectionsMultiStepForm />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={isCreateEntityPanelOpen}>
        <CreateEntityPanel />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={isDuplicationVendorsPanelOpen}>
        <DuplicationVendorsPanel />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={isNineDotPaneOpen}>
        <NineDotPanel />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={createCandidateForm?.isOpen}>
        <CreateCandidateForm />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={candidateManager?.isOpen}>
        <CandidateManager />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={!!asyncPickerProps}>
        {asyncPickerProps ? <AsyncPickerModal {...asyncPickerProps} /> : null}
      </PresenceAnimationWrapper>
      {!!commentModalData && <PostCommentsModal />}
      {!!reactionsModalData && <PostReactionsModal />}
      {isSubmitToVendorOpen && <SubmitVendor />}
      {isSubmitJobOpen && <SubmitJob />}
      {isLinkedJobOpen && <LinkedJobModal />}
      <PresenceAnimationWrapper isOpen={isOpenNotificationModal}>
        <NotificationModal />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={isOpenConfirmBlockModal}>
        <ConfirmBlockModal />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={checkoutModalState?.isOpen}>
        <CheckoutModal {...checkoutModalState} />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={automationModalState?.isOpen}>
        <AutomationModal />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={autoMoveModalState?.isOpen}>
        <AutoMoveModal />
      </PresenceAnimationWrapper>
      <PresenceAnimationWrapper isOpen={requirementsModalState?.isOpen}>
        <RequirementsModal />
      </PresenceAnimationWrapper>
      {isMoreThanTablet && (
        <PresenceAnimationWrapper isOpen={isOpenProfilePanel}>
          <ProfileMenu />
        </PresenceAnimationWrapper>
      )}
      {isFeedbackModalOpen && <FeedbackModal />}
      {objectNetworkModal?.isOpen && <SocialConnectionsModal />}
      {isOpenFailuresModal && <ListOfInvitationFailuresModal />}
      {isOpenInvitationsList && <ListOfInvitedPeople />}
      {isShareEntityModalOpen ? (
        <ShareEntityTabbedModal />
      ) : (
        <CreatePostModal />
      )}

      {viewMedia?.isOpen && (
        <ViewMedia
          medias={viewMedia?.medias}
          selectedItem={viewMedia?.selectedItem}
        />
      )}
      {isOpenReportModal && <ReportModal />}
      {isOpenCreateJobModalInUserProject && <CreateJobModalInUserProject />}
      {isInviteFormOpen && <InvitePeopleMultiStepForm />}
      {isCreateJobForm && <CreateJobForm initialData={data as any} />}
      {isCreateProjectopen && <CreateProjectFrom {...createProjectProps} />}
      {isLinkJobFormopen && <LinkJobForm data={linkJobProps.data} />}
      {isDeleteEntitypen && <DeleteEntityModal />}
      {editAssigneeData?.open && <EditAssigneeModal {...editAssigneeData} />}
      {compareModalData?.open && <CompareModal {...compareModalData} />}
      {isSubmitToClientFormopen && <SubmitToClientForm />}
    </>
  );
};

export default Modals;
