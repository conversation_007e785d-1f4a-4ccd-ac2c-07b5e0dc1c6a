import { MutationCache, QueryCache, QueryClient } from '@tanstack/react-query';
import useToast from 'shared/uikit/Toast/useToast';
import defaultQueryClientOptions from '@shared/utils/constants/defaultQueryClientOptions';
import { appEnvironment } from '@shared/utils/constants';

const ignoredToasterErrors = [
  'ProfileNotFoundException',
  'PageRoleAccessException',
  'MethodArgumentNotValidException',
  'PostNotFoundException',
  'UserNotFoundException',
  'PageNotFoundException',
  'BadCredentialsException',
  'WrongTotpTokenException',
  'WrongCredentialException',
  'ZeroPageIsNotCalledException',
  'BadCredentialsException',
  'EmailNotVerifiedException',
  'EmailNotFoundException',
  'RateLimiterExceedException',
  'DuplicateRecommendationForSameRoleException',
  'JobNotFoundException',
  'ExpiredVerificationCodeException',
  'MeetingNotFoundException',
  'MaximumRolePerPageExceedException',
  'SkillAlreadyExistInProfileException',
  'InvalidFileTypeException',
  'PlanRateLimiterExceedException',
  'JobArchivedException',
];
const isDevMode = appEnvironment.prodEnv === 'dev';

const useGetQueryClient = () => {
  const toast = useToast();

  return new QueryClient({
    queryCache: new QueryCache({
      onError: ({ response } = {}, query) => {
        const error = response?.data?.error || 'something_went_wrong';
        const showToast = !ignoredToasterErrors.includes(error);

        console.error('queryCache', { response, query });
        if (showToast && isDevMode) {
          toast({
            type: 'error',
            icon: 'times-circle',
            id: query?.options?.queryKey || error,
            title: error,
            message: `${query?.options?.queryKey} on ${response?.data?.path}`,
          });
        }
      },
    }),
    mutationCache: new MutationCache({
      onError: ({ response } = {}, _, __, mutation) => {
        const error = response?.data?.error || 'something_went_wrong';
        const showToast = !ignoredToasterErrors.includes(error);

        if (showToast) {
          toast({
            type: 'error',
            icon: 'times-circle',
            id: 'SERVER_ERROR',
            title: error,
            message: `${response?.data?.path}`,
          });
        }
      },
    }),
    defaultOptions: defaultQueryClientOptions,
  });
};

export default useGetQueryClient;
