import type {
  BECandidateNote,
  BECandidateReview,
  BECandidateTodo,
  BECandidateSearchResult,
  CandidateAPIData,
  CandidateDemographicFormData,
  CandidateFormData,
  CandidateLegalInfoFormData,
  CandidatePreferenceInfoAPIRequestBody,
  CandidatePreferenceInfoFormData,
  CandidateSocialInfoFormData,
  CandidateStatusInfoAPIRequestBody,
  CandidateStatusInfoFormData,
  ICandidateListItemProps,
  ICandidateNote,
  ICandidateReview,
  ICandidateTodo,
  BECandidateSummary,
  ICandidateSummaryOptions,
} from '@shared/types/candidates';
import type { Education } from '@shared/types/education';
import { socialMediaOrigins } from '@shared/constants/socialmedia';
import { TODO_STATUS_VALUES } from '@shared/types/todo';
import dayjs from 'dayjs';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import {
  GENDER_VALUES,
  AGE_RANGE_VALUES,
  RACE_VALUES,
  CRIMINAL_RECORD_VALUES,
  VETERAN_STATUS_VALUES,
  DISABILITY_STATUS_VALUES,
  NOTICE_PERIOD_VALUES,
  RELOCATION_STATUS_VALUES,
  CANDIDATE_STATUS_VALUES,
  ID_DOC_TYPE_VALUES,
} from '../constants/enums';
import { EMPLOYMENT_TYPES } from '../constants/enums/db';
import {
  WORK_SPACE_MODEL,
  experienceLevels,
  SALARY_PERIOD,
  WILLING_TRAVEL,
} from '../constants/enums/jobsDb';
import beforeCacheUserInfo from './beforeCacheUserInfo';
import skillNormalizer from './skillNormalizer';
import languageNormalizer from './languageNormalizer';
import type { NormalizedExperience } from '../experience.utils';
import { experienceNormalizer } from '../experience.utils';
import {
  educationNormalizer,
  type NormalizedEducation,
} from '../education.utils';
import {
  NOTE_VISIBILITY_VALUES,
  REVIEW_VISIBILITY_VALUES,
} from '../constants/enums/candidateDb';

/** Candidate Full Normalizer */
const beforeCacheCandidateInfo = (c?: CandidateAPIData): CandidateFormData => ({
  id: c?.id ?? '',
  createdDate: c?.createdDate,
  resumeUrl: c?.resumeUrl,
  lastModifiedDate: c?.lastModifiedDate,
  creatorUserId: c?.creatorUserId ?? '',
  creatorPageId: c?.creatorPageId ?? '',
  isLoboxCandidate: c?.isLoboxCandidate ?? false,
  isLoboxUser: c?.isLoboxUser ?? false,
  ...candidateDemographicNormalizer(c),
  ...candidateLegalStepNormalizer(c),
  ...candidatePreferenceStepNormalizer(c),
  ...candidateSocialStepNormalizer(c),
  ...candidateStatusInfoStepNormalizer(c),

  profile: c?.profile ? beforeCacheUserInfo(c?.profile) : ({} as any),
  invited: c?.invited ?? false,
  _experiences: candidateExperiencesNormalizer(c),
  /**
   * _educations is not normalized:
   * educationNormalizer needs translation method!
   */
  _educations: c?.profile?.educations ?? [],
  _skills: candidateSkillsNormalizer(c),
  _languages: candidateLanguagesNormalizer(c),

  calendarEvents: c?.calendarEvents ?? [],
  countOfJobs: Number(c?.countOfJobs) ?? 0,
  notes: c?.notes ?? [],
  reviews: c?.reviews ?? [],
  meetingsCount: Number(c?.meetingsCount),
  todosCount: Number(c?.todosCount),
  notesCount: Number(c?.notesCount),
  socialInformationEdited: c?.socialInformationEdited,
  backgroundInformationEdited: c?.backgroundInformationEdited,
  expectedInformationEdited: c?.expectedInformationEdited,
  legalInformationEdited: c?.legalInformationEdited,
  isManuallyCreated: c?.isManuallyCreated ?? false,
  isSocialCandidate: c?.isSocialCandidate ?? false,
});

export default beforeCacheCandidateInfo;

function candidateDemographicNormalizer(
  c?: CandidateAPIData
): CandidateDemographicFormData {
  const bd = c?.birthdate ?? c?.profile?.birthDate;
  return {
    gender: (c?.gender && GENDER_VALUES[c?.gender]) || undefined,
    genderEnteredByUser: c?.genderEnteredByUser || false,
    ageRange: (c?.ageRange && AGE_RANGE_VALUES[c?.ageRange]) || undefined,
    ageRangeEnteredByUser: c?.ageRangeEnteredByUser || false,
    race: (c?.race && RACE_VALUES[c?.race]) || undefined,
    raceEnteredByUser: c?.raceEnteredByUser || false,
    veteranStatus:
      (c?.veteranStatus && VETERAN_STATUS_VALUES[c?.veteranStatus]) ||
      undefined,
    veteranStatusEnteredByUser: c?.veteranStatusEnteredByUser || false,
    disabilityStatus:
      (c?.disabilityStatus && DISABILITY_STATUS_VALUES[c?.disabilityStatus]) ||
      undefined,
    disabilityStatusEnteredByUser: c?.disabilityStatusEnteredByUser || false,
    birthdate: bd ? new Date(bd) : undefined,
    ssn: c?.ssn,
    ssnEnteredByUser: c?.ssnEnteredByUser || false,
  };
}

function candidateLegalStepNormalizer(
  c?: CandidateAPIData
): CandidateLegalInfoFormData {
  return {
    workAuthorization: c?.workAuthorizationId
      ? {
          value: `${c.workAuthorizationId}`,
          label: c?.workAuthorizationTitle,
        }
      : undefined,
    workAuthorizationExpiryDate: c?.workAuthorizationExpiryDate
      ? new Date(c?.workAuthorizationExpiryDate)
      : undefined,
    visaHeldByUser:
      c?.visaHeldByUserId || c?.visaHeldByUserName || c?.visaHeldByUserSurname
        ? {
            value: c.visaHeldByUserId,
            image: c?.visaHeldByUserCroppedImageUrl,
            label: `${c?.visaHeldByUserName} ${c?.visaHeldByUserSurname}`,
            name: c?.visaHeldByUserName,
            surname: c?.visaHeldByUserSurname,
          }
        : undefined,
    referralUser:
      c?.referralUserId || c?.referralUserName || c?.referralUserSurname
        ? {
            value: c.referralUserId,
            image: c?.referralUserCroppedImageUrl,
            label: `${c?.referralUserName} ${c?.referralUserSurname}`,
            name: c?.referralUserName,
            surname: c?.referralUserSurname,
          }
        : undefined,
    referralCompany:
      c?.referralCompanyId || c?.referralCompanyName
        ? {
            value: c.referralCompanyId,
            image: c?.referralCompanyCroppedImageUrl,
            label: c?.referralCompanyName ?? '',
          }
        : undefined,
    referralEmiil: c?.referralEmail || '',
    referralPhone: c?.referralPhone || '',
    referralUrl: c?.referralUrl || '',
    attachments: c?.fileIds?.map((id) => Number(id)),
    fileIds: c?.fileIds ?? [],
    expectedTaxTerm: c?.expectedTaxTermId
      ? { value: c?.expectedTaxTermId, label: c?.expectedTaxTermTitle }
      : undefined,
    expectedMarkup: c?.expectedMarkup || 0,
    criminalRecord:
      (c?.criminalRecord && CRIMINAL_RECORD_VALUES[c?.criminalRecord]) ||
      undefined,
    country: c?.countryCode
      ? { value: c.countryCode, label: c.country }
      : undefined,
    identificationDocument:
      (c?.identificationDocumentType &&
        ID_DOC_TYPE_VALUES[c?.identificationDocumentType]) ||
      undefined,
    identificationDocumentTypeEnteredByUser:
      c?.identificationDocumentTypeEnteredByUser || false,
  };
}

function candidatePreferenceStepNormalizer(
  c?: CandidatePreferenceInfoAPIRequestBody
): CandidatePreferenceInfoFormData {
  const preferredEmploymentType = EMPLOYMENT_TYPES.find(
    (option) => option.value === c?.preferredEmploymentType
  );
  const preferredWorkPlaceType = WORK_SPACE_MODEL.find(
    (option) => option.value === c?.preferredWorkPlaceType
  );
  const preferredExperienceLevel = experienceLevels.find(
    (option) => option.value === c?.preferredExperienceLevel
  );
  const expectedSalaryPeriod = SALARY_PERIOD.find(
    (option) => option.value === c?.expectedSalaryPeriod
  );
  return {
    preferredJob: c?.preferredJobTitle
      ? { label: c.preferredJobTitle }
      : undefined,
    preferredEmploymentType,
    preferredWorkPlaceType,
    preferredExperienceLevel,
    noticePeriod:
      (c?.noticePeriod && NOTICE_PERIOD_VALUES[c.noticePeriod]) || undefined,
    preferredLocation: c?.preferredLocation
      ? {
          ...c.preferredLocation,
          value: c.preferredLocation.externalId,
          label: cleanRepeatedWords(c.preferredLocation.title),
        }
      : undefined,
    relocation: c?.relocation
      ? RELOCATION_STATUS_VALUES[c?.relocation]
      : undefined,
    travelRequirement: c?.travelRequirement
      ? WILLING_TRAVEL.find((item) => item?.value === c?.travelRequirement)
      : undefined,
    expectedCurrency:
      c?.expectedCurrencyTitle || c?.expectedCurrencyCode
        ? {
            label: c?.expectedCurrencyTitle,
            value: c?.expectedCurrencyId,
            code: c?.expectedCurrencyCode,
          }
        : undefined,

    expectedSalaryPeriod,
    expectedMinimumSalary: c?.expectedMinimumSalary || 0,
    expectedMaximumSalary: c?.expectedMaximumSalary || 0,
  };
}

function candidateSocialStepNormalizer(
  c?: CandidateAPIData
): CandidateSocialInfoFormData {
  return {
    cellNumber: c?.cellNumber || '',
    workNumber: c?.workNumber || '',
    homeNumber: c?.homeNumber || '',
    skypeId: c?.skypeId || '',
    linkedinUrl: c?.linkedinUrl,
    facebookUrl: c?.facebookUrl,
    twitterUrl: c?.twitterUrl,
    linkedinUsername: (c?.linkedinUrl || '').replace(
      socialMediaOrigins.linkedin,
      ''
    ),
    facebookUsername: (c?.facebookUrl || '').replace(
      socialMediaOrigins.facebook,
      ''
    ),
    twitterUsername: (c?.twitterUrl || '')
      .replace(socialMediaOrigins.x, '')
      .replace(socialMediaOrigins.twitter, ''),
    otherUrls: c?.otherUrls || [''],
    fullAddress: c?.fullAddress || '',
  };
}

function getTags(tags?: string[]): string[] {
  return (tags ?? []).filter((t) => !!t) ?? [];
}

function candidateStatusInfoStepNormalizer(
  c?: CandidateStatusInfoAPIRequestBody
): CandidateStatusInfoFormData {
  return {
    resumeUrl: c?.resumeUrl || undefined,
    tags: getTags(c?.tags),
    openToWork: c?.openToWork
      ? CANDIDATE_STATUS_VALUES[c?.openToWork]
      : undefined,
    note: c?.note || undefined,
    jobSite: c?.jobSite ? { label: c.jobSite } : undefined,
  };
}

function candidateExperiencesNormalizer(
  c?: CandidateAPIData
): NormalizedExperience[] {
  return (c?.profile?.experiences ?? [])
    ?.sort((item1, item2) => {
      const a = {
        start: new Date(item1.startDate),
        end: !item1.currentlyWorking ? new Date(item1.endDate) : new Date(),
      };
      const b = {
        start: new Date(item2.startDate),
        end: !item2.currentlyWorking ? new Date(item2.endDate) : new Date(),
      };
      const dStart = b.start - a.start;
      const dEnd = b.end - a.end;
      return dStart || dEnd;
    })
    .reduce(experienceNormalizer, []);
}

export function candidateEducationNormalizer(
  educations?: Education[],
  t = (st: string) => st
): NormalizedEducation[] {
  return (educations ?? [])
    .map((item) => {
      const { realData, ...education } = educationNormalizer(item, t);
      return {
        ...education,
        realData,
        badgeText: item.recruiterData ? t('recruiter_data') : undefined,
      };
    })
    ?.sort((item1, item2) => {
      const a = {
        start: new Date(item1.realData.startDate),
        end: !item1.realData.currentlyStudying
          ? new Date(item1.realData.endDate)
          : new Date(), // bigger than year 2500
      };
      const b = {
        start: new Date(item2.realData.startDate),
        end: !item2.realData.currentlyStudying
          ? new Date(item2.realData.endDate)
          : new Date(),
      };
      const dStart = b.start - a.start;
      const dEnd = b.end - a.end;
      return dStart || dEnd;
    });
}

function candidateSkillsNormalizer(c?: CandidateAPIData): Skill[] {
  return (c?.profile?.skills ?? [])
    .map(skillNormalizer)
    .sort((a, b) => (a.progress < b.progress ? 1 : -1))
    .sort((item1, item2) => {
      const a = +!!item1?.realData.originalId;
      const b = +!!item2?.realData.originalId;
      return b - a;
    });
}

function candidateLanguagesNormalizer(c?: CandidateAPIData): Language[] {
  return (c?.profile?.languages ?? [])
    ?.map(languageNormalizer)
    .sort((a, b) => (a.progress < b.progress ? 1 : -1))
    .sort((item1, item2) => {
      const a = +!!item1?.realData.originalId;
      const b = +!!item2?.realData.originalId;
      return b - a;
    });
}

export function normalizeCandidateTodo(todo: BECandidateTodo): ICandidateTodo {
  const [startDate, fullStartTime] = todo.start?.split('T') ?? [];
  const [shh = '00', smm = '00'] = fullStartTime?.split(':') ?? [];
  const startTime = `${shh}:${smm}`;

  const [endDate, fullEndTime] = todo.end?.split('T') ?? [];
  const [ehh = '00', emm = '00'] = fullEndTime?.split(':') ?? [];
  const endTime = `${ehh}:${emm}`;
  return {
    id: `${todo.id}`,
    title: todo.title,
    description: todo.description,
    start: todo.start,
    startDate: startDate ? `${startDate}T00:00:00.000` : undefined,
    startTime: { value: startTime, label: startTime },
    end: todo.end,
    endDate: endDate ? `${endDate}T00:00:00.000` : undefined,
    endTime: { value: endTime, label: endTime },
    creator: todo.creator,
    creatorPageId: todo.creatorPageId,
    creatorType: todo.creatorType,
    assigneeUser: todo.assigneeUser,
    remind: todo.remind,
    status: TODO_STATUS_VALUES[todo.status] ?? TODO_STATUS_VALUES.OPEN,
  };
}

export function normalizeCandidateNote(note: BECandidateNote): ICandidateNote {
  return {
    ...note,
    visibility: NOTE_VISIBILITY_VALUES[note.visibility],
    timeGroupUnix: dayjs(note.createDateTime).startOf('day').unix(),
  };
}

export function normalizeCandidateReview(
  review: BECandidateReview
): ICandidateReview {
  return {
    ...review,
    visibility: REVIEW_VISIBILITY_VALUES[review.visibility],
  };
}

export function candidateSearchItemNormalizer(
  c: BECandidateSearchResult
): ICandidateListItemProps {
  return {
    id: c.id,
    pageId: c.pageId,
    profileId: c.profileId,
    firstText: `${c.name} ${c.surname}`,
    secondText: c.username ? `@${c.username}` : c.email,
    thirdText: c.occupation,
    fourthText: cleanRepeatedWords(c.location?.title) || undefined,
    avatar: c.croppedImageUrl || undefined,
    createDateTime: c.createDateTime,
    usagesCount: c.usagesCount,
    notesCount: c.notesCount,
    todosCount: c.todosCount,
    meetingsCount: c.meetingsCount,
    isManual: !c.surname,
  };
}

export function normalizeCandidateMeeting(x: any) {
  return x;
}

export function normalizeCandidateSummary(
  cs: BECandidateSummary
): ICandidateSummaryOptions {
  return [
    ...(cs?.participationSummaries?.map((item) => ({
      ...item,
      id: String(item.id),
    })) ?? []),
    {
      jobTitle: `${cs.candidateName} ${cs.candidateSurname}`,
      candidateId: cs.candidateId,
      pageTitle: 'candidate_mode',
      pageCroppedImageUrl: cs.candidateCroppedImageUrl,
      type: 'ORIGINAL_CANDIDATE',
    },
  ];
}
