import { weekDaysValueArray } from 'shared/utils/constants/enums/schedulesDb';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { UPPERtoCapital } from './UPPERtoCapital';

export const COLORS = {
  [ScheduleEventTypes.MEETING]: 'cornflowerBlue',
  [ScheduleEventTypes.BIRTHDAY]: 'heliotrope',
  [ScheduleEventTypes.HOLIDAY]: 'lightBrown',
  [ScheduleEventTypes.REMINDER]: 'darkTangerine',
  [ScheduleEventTypes.TASK]: 'darkError',
  [ScheduleEventTypes.TEMPORARY]: 'brand',
} as const;

export const COLORS_DARK: typeof COLORS = Object.keys(COLORS).reduce(
  (acc, cur) => ({
    ...acc,
    [cur]: COLORS[cur]?.concat(cur === 'TEMPORARY' ? '' : 'Dark'),
  }),
  COLORS
);

export const EVENT_LABELS = {
  [ScheduleEventTypes.MEETING]: 'e_meetings',
  [ScheduleEventTypes.BIRTHDAY]: 'e_birthdays',
  [ScheduleEventTypes.HOLIDAY]: 'e_public_holidays',
  [ScheduleEventTypes.REMINDER]: 'e_reminders',
  [ScheduleEventTypes.TASK]: 'e_tasks',
} as const;

export const allEventTypes = Object.keys(
  EVENT_LABELS
) as (keyof typeof EVENT_LABELS)[];

export const weekDay3LetterLabels = weekDaysValueArray.map((wd) =>
  UPPERtoCapital(wd.slice(0, 3))
);

export const MEDIA_BOX_SIZE = {
  WIDTH: 650,
  HEIGHT: 650,
};
