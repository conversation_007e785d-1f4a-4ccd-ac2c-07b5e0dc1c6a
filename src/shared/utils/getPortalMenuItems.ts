import { appPortalsObject, getPortal } from './getAppEnv';

const getPortalMenuItems = (items: Array<Record<string, any>>) => {
  const portal = getPortal();

  const menuStructure = {
    [appPortalsObject.user]: [
      'home',
      'discover',
      'clubs',
      'news',
      'articles',
      'jobs',
      'people',
      'pages',
      'schedules',
      'messages',
    ],
    [appPortalsObject.editor]: [
      'home',
      'discover',
      'projects',
      'news',
      'articles',
      'clubs',
      'people',
      'pages',
      'pipelines',
      'dashboard',
      'schedules',
      'messages',
    ],
    [appPortalsObject.recruiter]: [
      'discover',
      'projects',
      'candidates',
      'companies',
      'jobs',
      'pipelines',
      'dashboard',
      'schedules',
      'messages',
    ],
    [appPortalsObject.service]: [
      'discover',
      'projects',
      'services',
      'clients',
      'pipelines',
      'dashboard',
      'schedules',
      'messages',
    ],
    [appPortalsObject.campaign]: [
      'discover',
      'projects',
      'campaigns',
      'ads',
      'clients',
      'pipelines',
      'dashboard',
      'schedules',
      'messages',
    ],
    [appPortalsObject.sales]: [
      'discover',
      'projects',
      'sales',
      'customers',
      'pipelines',
      'dashboard',
      'schedules',
      'messages',
    ],
  };
  const menuItemsMap = items.reduce((acc, item) => {
    acc[item.id] = item;
    return acc;
  }, {});
  return menuStructure[portal]?.map((key) => menuItemsMap[key]);
};

export default getPortalMenuItems;
