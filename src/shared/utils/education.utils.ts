import collectionToObjectByKey from 'shared/utils/toolkit/collectionToObjectByKey';
import datesDuration from 'shared/utils/toolkit/datesDuration';
import { db } from 'shared/utils/constants/enums';
import formatDate from 'shared/utils/toolkit/formatDate';
import type { Education } from '@shared/types/education';
import { isValidDate } from '@shared/utils/toolkit/isValidDate';
import { displayDuration } from '../../app/(lobox)/(profile)/[username]/(about)/partials/utils';

const degrees = collectionToObjectByKey(db.DEGREE_OPTIONS);

export const convertEducationApiToForm = (curr: Education) => {
  const {
    schoolPageId,
    schoolName,
    majorName,
    majorLookupId,
    degree,
    startDate,
    originalId,
    endDate,
    id,
    description,
    currentlyStudying,
    location,
    pageCroppedImageUrl,
  } = curr;

  const degreeLabel = degrees[degree]?.label || 'NON';
  return {
    school: {
      label: schoolName,
      value: schoolPageId,
      image: pageCroppedImageUrl,
    },
    location: location
      ? {
          ...location,
          label: location.title,
          value: location.externalId,
        }
      : undefined,
    major: { label: majorName, value: majorLookupId },
    degree: { value: degree, label: degreeLabel },
    id,
    originalId,
    endDate,
    startDate,
    description,
    currentlyStudying,
  };
};

export const educationNormalizer = (item: Education, t: Function) => {
  const {
    schoolPageId,
    schoolName,
    majorName,
    degree,
    startDate,
    endDate,
    id,
    description,
    pageCroppedImageUrl,
    location,
  } = item;
  const degreeLabel = t(degrees[degree]?.label);
  const realData = convertEducationApiToForm(item);
  const secondText = [majorName, degreeLabel].filter(Boolean).join(', ');
  const formattedEndDate = endDate
    ? formatDate(endDate, 'MMM YYYY')
    : 'Present';
  const formattedStartDate = formatDate(startDate, 'MMM YYYY');
  const durationObj = datesDuration({
    startDate,
    endDate: endDate || new Date().toISOString(),
  });
  const duration = durationObj ? displayDuration(durationObj) : '';

  return {
    id,
    image: pageCroppedImageUrl,
    firstText: schoolName,
    firstTextAdditionalProps: { objectId: schoolPageId },
    secondText: isValidDate(startDate)
      ? `${formattedStartDate} - ${formattedEndDate}`
      : 'No date entered',
    secondTextHelper: isValidDate(startDate) ? duration : undefined,
    fourthText: secondText?.length > 0 ? secondText : undefined,
    fourthTextDotHelper: [
      secondText?.length > 0 && location ? location?.title : undefined,
    ].filter(Boolean),
    realData,
    longText: description,
    objectId: schoolPageId,
    durationObj,
  };
};

export type NormalizedEducation = ReturnType<typeof educationNormalizer>;
