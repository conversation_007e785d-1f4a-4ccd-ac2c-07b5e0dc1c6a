import type { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import QueryKeys from 'shared/utils/constants/queryKeys';

const getSchedulesSectionsQueryKey = (
  schedulesEventType: ScheduleEventTypes
) => {
  const upComingQueryKey = [QueryKeys.getUpcomingMeetings, schedulesEventType];
  const pastQueryKey = [QueryKeys.getPastMeetings, schedulesEventType];
  const meetingQueryKey = [QueryKeys.candidateMeetings];
  return { pastQueryKey, upComingQueryKey, meetingQuery<PERSON>ey };
};

export default getSchedulesSectionsQueryKey;
