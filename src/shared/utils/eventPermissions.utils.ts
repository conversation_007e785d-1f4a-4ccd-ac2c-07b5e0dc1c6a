import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import {
  EventPermissions,
  MeetingAttendeePermission,
  type SavedEventType,
} from 'shared/types/schedules/schedules';

type EventPermission = EventPermissions | MeetingAttendeePermission;

export default function getEventPermission(
  event: SavedEventType
): EventPermission[] {
  const permissions: EventPermission[] = [];

  permissions.push(EventPermissions.REPORT);
  permissions.push(EventPermissions.VIEW);
  if (event.type !== ScheduleEventTypes.REMINDER)
    permissions.push(EventPermissions.SHARE);

  if (
    ![
      ScheduleEventTypes.REMINDER,
      ScheduleEventTypes.TASK,
      ScheduleEventTypes.MEETING,
      ScheduleEventTypes.OFF_DAY,
    ].includes(event.type)
  ) {
    return permissions;
  }

  if (event.assignee?.modifyPermission) {
    permissions.push(EventPermissions.EDIT);
    return permissions;
  }

  if ('currentUserIsCreator' in event && event?.currentUserIsCreator) {
    permissions.push(EventPermissions.EDIT);
    permissions.push(EventPermissions.DELETE);
    Object.keys(MeetingAttendeePermission).forEach((item) =>
      permissions.push(item as MeetingAttendeePermission)
    );

    return permissions;
  }

  const attendeePermissions = event.attendees?.[0]?.permissions ?? [];
  if (permissions.length) {
    Object.values(attendeePermissions).forEach((item) =>
      permissions.push(item as MeetingAttendeePermission)
    );
  }

  return permissions;
}

export function canView(permissions: EventPermission[]): boolean {
  return permissions.includes(EventPermissions.VIEW);
}

export function canEdit(permissions: EventPermission[]): boolean {
  return (
    permissions.includes(EventPermissions.EDIT) ||
    permissions.includes(MeetingAttendeePermission.MODIFY_MEETING) ||
    permissions.includes(MeetingAttendeePermission.INVITE_OTHERS)
  );
}

export function canShare(permissions: EventPermission[]): boolean {
  return permissions.includes(EventPermissions.SHARE);
}

export function canDelete(permissions: EventPermission[]): boolean {
  return permissions.includes(EventPermissions.DELETE);
}
