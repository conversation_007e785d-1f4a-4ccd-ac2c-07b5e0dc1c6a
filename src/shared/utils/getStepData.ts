import type { CallbackParams } from 'shared/components/Organism/MultiStepForm/MultiStepForm';
import { MultiStepFormProps } from 'shared/components/Organism/MultiStepForm/MultiStepForm';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import type { ComponentProps } from 'react';

export default function getStepData<T extends keyof MultiStepFormStepProps>(
  key: T,
  data: MultiStepFormStepProps[]
): (x: CallbackParams) => ComponentProps<any> {
  return ({ step, ...rest }: CallbackParams) =>
    (data[step][key] as (x: CallbackParams) => ComponentProps<any>)?.({
      step,
      ...rest,
    });
}
