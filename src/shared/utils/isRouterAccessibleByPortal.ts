import { routeNames } from 'shared/utils/constants/routeNames';
import { appPortalsObject, getPortal } from 'shared/utils/getAppEnv';

const commons = [routeNames.search, routeNames.settingsFeedPref];
const invalidRouteForPortals = {
  [appPortalsObject.user]: [
    routeNames.sales,
    routeNames.services,
    routeNames.searchRecruiterJobs,
    routeNames.searchRecruiterProjects,
  ],
  [appPortalsObject.editor]: [
    routeNames.sales,
    routeNames.services,
    routeNames.searchJobs,
    routeNames.searchAll,
  ],
  [appPortalsObject.service]: [
    ...commons,
    routeNames.jobs,
    routeNames.sales,
    routeNames.searchAll,
  ],
  [appPortalsObject.sales]: [
    ...commons,
    routeNames.jobs,
    routeNames.services,
    routeNames.searchAll,
  ],
  [appPortalsObject.campaign]: [
    ...commons,
    routeNames.jobs,
    routeNames.sales,
    routeNames.services,
    routeNames.searchAll,
  ],
  [appPortalsObject.recruiter]: [
    routeNames.settingsFeedPref,
    routeNames.sales,
    routeNames.services,
    routeNames.searchAll,
  ],
};

const isRouterAccessibleByPortal = (routeName: string) => {
  const isRestricted = invalidRouteForPortals[getPortal()]?.some?.(
    (prefix: string) => routeName.startsWith(prefix)
  );
  return !isRestricted;
};

export default isRouterAccessibleByPortal;
