import type { PaginatedParams } from '@shared/types/params';
import type { PaginateResponse } from '@shared/types/response';
import templateEndPoints from '../constants/servicesEndpoints/services/template';
import request from '../toolkit/request';

export type BETemplate = {
  title: string;
  subject: string;
  message: string;
  fileIds: string[];
} & (
  | {
      hasFollowup: false;
    }
  | {
      hasFollowup: true;
      followupTitle: string;
      followupMessage: string;
      followupPeriod: string;
      default: boolean;
    }
);
export type BETemplateResponse = {
  id: string;
  createdDate: string;
  userId: string;
  pageId: string;
} & BETemplate;
export enum TemplateCategoryTypes {
  rejection = 'rejection',
  message = 'message',
  meeting = 'meeting',
  email = 'email',
}
export type TemplateCategoryType = keyof typeof TemplateCategoryTypes;

export const isValidTemplateCategoryType = (
  value?: string
): value is TemplateCategoryType =>
  !!value && ['rejection', 'message', 'meeting', 'email'].includes(value);

export const searchAllTemplates = async ({
  category,
  ...params
}: PaginatedParams<{ category: TemplateCategoryType }>) => {
  const { data } = await request.get<PaginateResponse<BETemplateResponse>>(
    templateEndPoints.searchAll(category),
    { params }
  );

  return data;
};
export const getAllTemplates = async (
  params: PaginatedParams<{ category: TemplateCategoryType }>
) => {
  const { data } = await request.get<PaginateResponse<BETemplateResponse>>(
    templateEndPoints.getAll,
    { params }
  );
  return data;
};

export const getTemplate = async ({
  category,
  id,
  ...params
}: {
  category: TemplateCategoryType;
  id?: string;
}) => {
  const data = await request.get<BETemplateResponse>(
    templateEndPoints.byId(category, id),
    { params }
  );
  return data.data;
};

export const toggleDefaultTemplate = async ({
  id,
  category,
  isDefault = false,
}: {
  id: string;
  isDefault: boolean;
  category: TemplateCategoryType;
}) => {
  const url = templateEndPoints.toggleDefault(category, id);

  if (isDefault) {
    const data = await request.delete(url);
    return data.data;
  }
  const data = await request.post(url, {});
  return data.data;
};
