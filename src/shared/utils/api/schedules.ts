import type {
  ICreateMeetingData,
  ICreateReminderData,
  ICreateTaskData,
  MeetingDetails,
  <PERSON>minder,
  BETaskDetails,
  BECalendarEvent,
  BEMeetingDetails,
  BEReminderDetails,
  BEHolidayDetails,
  BEBirthdayDetails,
} from 'shared/types/schedules/schedules';
import type {
  CalendarProviderName,
  ProviderData,
} from 'shared/components/molecules/EventsIntegration/utils/type';
import { ProviderType } from 'shared/components/molecules/EventsIntegration/utils/type';
import type {
  BEPreferences,
  BETimesheet,
  NewTimeSheet,
} from 'shared/types/preferences/preferences';
import type {
  GetUserTimesheetResponse,
  GetUserTimesheetProps,
  GetBookedMeetingsResponse,
} from 'shared/types/preferences';
import { schedulesEndpoints } from 'shared/utils/constants/servicesEndpoints';
import type { PaginateResponse } from 'shared/types/response';
import type { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import request from '../toolkit/request';
import {
  getCalendarEventsNormalizer,
  getCalendarUpcomingNormalizer,
  getMeetingDetailsNormalizer,
  getReminderDetailsNormalizer,
  getTaskDetailsNormalizer,
  preferencesNormalizer,
  userTimesheetNormalizer,
  getHolidayDetailsNormalizer,
  getBirthdayDetailsNormalizer,
  getAllProvidersNormalizer,
} from '../normalizers/schedules';
import { schedulesEventTypes } from '../constants/enums';

export const getCalendarEvents = async ({ params }: any) => {
  const { data } = await request.get<BECalendarEvent[]>(
    schedulesEndpoints.getCalendarEvents,
    {
      params,
    }
  );
  return getCalendarEventsNormalizer(data);
};

export const getCalendarUpcoming = async (params: any) => {
  const { data } = await request.get<PaginateResponse<any>>(
    schedulesEndpoints.getCalendarUpcoming,
    {
      params,
    }
  );
  return getCalendarUpcomingNormalizer(data);
};
export const getCalendarPast = async (params: any) => {
  const { data } = await request.get<PaginateResponse<any>>(
    schedulesEndpoints.getCalendarPast,
    {
      params,
    }
  );
  return getCalendarUpcomingNormalizer(data);
};

export const getMeetingDetails = async ({
  params,
}: {
  params: { id: string };
}) => {
  if (!params.id) {
    throw new Error('To get meet details id is required.');
  }
  const { data } = await request.get<BEMeetingDetails>(
    schedulesEndpoints.getMeetingDetails(params.id)
  );
  return getMeetingDetailsNormalizer(data);
};

export const getReminderDetails = async ({ params }: any) => {
  if (!params.id) {
    throw new Error('To get reminder details id is required.');
  }
  const { data } = await request.get<BEReminderDetails>(
    schedulesEndpoints.getReminderDetails(params.id)
  );
  return getReminderDetailsNormalizer(data);
};

export const getTaskDetails = async ({ params }: any) => {
  if (!params.id) {
    throw new Error('To get task details id is required.');
  }
  const { data } = await request.get<BETaskDetails>(
    schedulesEndpoints.getTaskDetails(params.id)
  );
  return getTaskDetailsNormalizer(data);
};

export const deleteEvent = async (params: {
  schedulesEventType: ScheduleEventTypes;
  eventId: string;
}) => {
  const { schedulesEventType, eventId } = params || {};
  if (!eventId) {
    throw new Error('To delete meeting id is required.');
  }
  const urls = {
    [schedulesEventTypes.MEETING]: schedulesEndpoints.getMeetingDetails,
    [schedulesEventTypes.REMINDER]: schedulesEndpoints.getReminderDetails,
    [schedulesEventTypes.TASK]: schedulesEndpoints.getTaskDetails,
    [schedulesEventTypes.BIRTHDAY]: schedulesEndpoints.getBirthdayDetails,
  } as any;

  const url = urls[schedulesEventType]?.(eventId);
  const { data } = await request.delete(url);
  return data;
};

export const createMeeting = async (meetingData: ICreateMeetingData) => {
  const { data } = await request.post(
    schedulesEndpoints.createMeeting,
    meetingData
  );
  return data;
};

export const createReminder = async (reminderData: ICreateReminderData) => {
  const { data } = await request.post(
    schedulesEndpoints.createReminder,
    reminderData
  );
  return data;
};

export const createTask = async (taskData: ICreateTaskData) => {
  const { data } = await request.post(schedulesEndpoints.createTask, taskData);
  return data;
};

export const updateReminder = async (reminderData: Reminder) => {
  const { data } = await request.put(
    `${schedulesEndpoints.createReminder}/${reminderData.id}`,
    reminderData
  );
  return data;
};

export const updateMeeting = async (meetingData: MeetingDetails) => {
  const { data } = await request.put(
    `${schedulesEndpoints.createMeeting}/${meetingData.id}`,
    meetingData
  );
  return data;
};

export const updateTask = async (taskData: BETaskDetails) => {
  const { data } = await request.put(
    `${schedulesEndpoints.createTask}/${taskData.id}`,
    taskData
  );
  return data;
};

export const getHolidayDetails = async ({ params }: any) => {
  if (!params.id) {
    throw new Error('To get holiday details id is required.');
  }
  const { data } = await request.get<BEHolidayDetails>(
    schedulesEndpoints.getHolidayDetails(params.id)
  );

  return getHolidayDetailsNormalizer(data);
};

export const getBirthdayDetails = async ({ params }: any) => {
  if (!params.id) {
    throw new Error('To get reminder details id is required.');
  }
  const { data } = await request.get<BEBirthdayDetails>(
    schedulesEndpoints.getBirthdayDetails(params.id)
  );

  return getBirthdayDetailsNormalizer(data);
};

export const getPreferences = async (params: any) => {
  const { data } = await request.get<BEPreferences>(
    schedulesEndpoints.getPreferences,
    {
      params,
    }
  );
  return preferencesNormalizer(data);
};

export const postTimeSheet = async (payload: NewTimeSheet) => {
  const { data } = await request.post(
    schedulesEndpoints.postTimeSheet,
    payload
  );
  return data;
};
export const putTimeSheet = async (payload: BETimesheet) => {
  const { data } = await request.put(
    `${schedulesEndpoints.postTimeSheet}/${payload.id}`,
    payload
  );
  return data;
};

export const deleteTimesheet = async ({ id }: { id: string }) => {
  const { data } = await request.delete(schedulesEndpoints.deleteTimesheet(id));
  return data;
};

export const getUserTimesheet = async ({ userId }: GetUserTimesheetProps) => {
  const { data } = await request.get<GetUserTimesheetResponse>(
    schedulesEndpoints.getUserTimesheet(userId)
  );
  return userTimesheetNormalizer(data);
};

export const getBookedMeetings = async ({
  params,
}: {
  params: { userId: string; from: string; to: string };
}) => {
  const { data } = await request.get<GetBookedMeetingsResponse>(
    schedulesEndpoints.getBookedMeetings,
    {
      params,
    }
  );
  return data;
};

export const bookMeeting = async (payload: {
  timesheetId: string;
  start: string;
}) => {
  const { data } = await request.post<BEMeetingDetails>(
    schedulesEndpoints.bookMeeting,
    payload
  );
  return data;
};

export const getAllProviders = async <PT extends ProviderType>({
  params: { providerType = ProviderType.Calendar as PT, ...params },
}: {
  params: {
    providerType: PT;
    [s: string]: any;
  };
}) => {
  const api =
    providerType === ProviderType.Conference
      ? schedulesEndpoints.conferenceProvider
      : schedulesEndpoints.calendarProvider;
  const { data } = await request.get<ProviderData<PT>[]>(api, {
    params,
  });
  return getAllProvidersNormalizer(data, providerType);
};

export const getAllProviderUrls = async ({
  params: { providerType = ProviderType.Calendar, ...params },
}: {
  params: {
    providerType: ProviderType;
    [s: string]: any;
  };
}) => {
  const api =
    providerType === ProviderType.Conference
      ? schedulesEndpoints.conferenceProviderUrls
      : schedulesEndpoints.calendarProviderUrls;
  const { data } = await request.get(api, {
    params,
  });
  return data;
};

export const addProvider = async ({
  providerType = ProviderType.Calendar,
  providerName,
  ...args
}: {
  externalUserName?: string;
  authorizationCode: string;
  providerName: CalendarProviderName;
  providerType?: ProviderType;
  title?: string;
  username?: string;
}) => {
  const api =
    providerType === ProviderType.Conference
      ? schedulesEndpoints.conferenceProvider
      : schedulesEndpoints.calendarProvider;
  // TODO change providerName to type
  const { data } = await request.post(api, { ...args, type: providerName });
  return data;
};

export const connectProvider = async ({
  providerType = ProviderType.Calendar,
  ...args
}: {
  id: string;
  providerType?: ProviderType;
}) => {
  const api =
    providerType === ProviderType.Conference
      ? schedulesEndpoints.connectConferenceProvider
      : schedulesEndpoints.connectCalendarProvider;
  const { data } = await request.post(api, args);
  return data;
};

export const disconnectProvider = async ({
  providerType = ProviderType.Calendar,
  ...args
}: {
  id: string;
  providerType?: ProviderType;
}) => {
  const api =
    providerType === ProviderType.Conference
      ? schedulesEndpoints.disconnectConferenceProvider
      : schedulesEndpoints.disconnectCalendarProvider;
  const { data } = await request.post(api, args);
  return data;
};

export const removeProvider = async ({
  id,
  providerType = ProviderType.Calendar,
}: {
  id: string;
  providerType?: ProviderType;
}) => {
  const api =
    providerType === ProviderType.Conference
      ? schedulesEndpoints.removeConferenceProvider(id)
      : schedulesEndpoints.removeCalendarProvider(id);
  const { data } = await request.delete(api);
  return data;
};

export const celebrateHoliday = async (args: {
  id: string;
  message: string;
  userIds: Array<string>;
}) => {
  const { data } = await request.post(
    schedulesEndpoints.celebrateholiday,
    args
  );
  return data;
};

export const celebrateBirthday = async (args: {
  id: string;
  message: string;
}) => {
  const { data } = await request.post(
    schedulesEndpoints.celebrateBirthday,
    args
  );
  return data;
};

export const acceptMeeting = async (args: { id: string; comment?: string }) => {
  const { data } = await request.post(schedulesEndpoints.acceptMeeting, args);
  return data;
};

export const declineMeeting = async (args: {
  id: string;
  comment?: string;
}) => {
  const { data } = await request.post(schedulesEndpoints.declineMeeting, args);
  return data;
};

export const leaveMeeting = async (args: { id: string; comment?: string }) => {
  const { data } = await request.post(schedulesEndpoints.leaveMeeting, args);
  return data;
};

export const acceptTask = async ({ id, ...args }: { id: string }) => {
  const { data } = await request.post(schedulesEndpoints.acceptTask(id), args);
  return data;
};

export const declineTask = async ({ id, ...args }: { id: string }) => {
  const { data } = await request.post(schedulesEndpoints.declineTask(id), args);
  return data;
};

const defaultExports = {
  celebrateBirthday,
  celebrateHoliday,
  removeProvider,
  getAllProviderUrls,
  getAllProviders,
  addProvider,
  connectProvider,
  disconnectProvider,
  createMeeting,
  createReminder,
  updateMeeting,
  updateReminder,
  createTask,
  updateTask,
  getCalendarEvents,
  getMeetingDetails,
  getReminderDetails,
  getTaskDetails,
  deleteEvent,
  getCalendarUpcoming,
  getCalendarPast,
  getBirthdayDetails,
  getHolidayDetails,
  getPreferences,
  postTimeSheet,
  putTimeSheet,
  deleteTimesheet,
  getUserTimesheet,
  getBookedMeetings,
  bookMeeting,
};
export default defaultExports;
