const Endpoints = {
  Auth: {
    postLogin: 'auth-service/api/v1/login',
    postForgetPassword: 'auth-service/api/v1/forgot-password',
    postForgetPasswordConfirm: 'auth-service/api/v1/reset-password',
    postEmail: 'auth-service/api/v1/signup',
    postDetail: 'auth-service/api/v1/signup-completion',
    postResendEmail: 'auth-service/api/v1/resend-email-verification',
    checkToken: 'auth-service/api/v1/verify-email',
    logout: 'auth-service/api/v1/logout',
    getAuthUserInfo: 'auth-service/api/v1/users/me',
    birthDate: 'auth-service/api/v1/users/birth-date',
    phone: 'auth-service/api/v1/users/phone',
    setLocation: 'auth-service/api/v1/users/location',
    setUserAvatar: 'auth-service/api/v1/users/image',
    validateUsername: 'auth-service/api/v1/validate-username',
  },
  App: {
    Common: {
      employmentTypesApi: 'lookup/v1/api/contract-types',
      getCountries: 'geo-service/api/v1/country/search',
      getCities: 'lookup/v1/api/location/search-cities',
      getOccupations: 'lookup-service/api/v1/occupation/search',
      getMajors: 'lookup-service/api/v1/major/search',
      upload: 'storage-service/api/v1/upload',
      suggestPlace: 'user-search-service/api/v1/suggest-page',
      getSkills: 'lookup-service/api/v1/skill/search',
      getLanguages: 'lookup-service/api/v1/language/search',
      getIndustry: 'lookup-service/api/v1/industry/search',
      getGeocode: 'lookup-service/api/v1/geocode',
      discoverPlace: 'lookup-service/api/v1/discover-place',
      searchCurrency: 'lookup-service/api/v1/currency/search',
      searchInstrument: 'lookup-service/api/v1/instrument/search',
      searchHobby: 'lookup-service/api/v1/hobby/search',
      searchSport: 'lookup-service/api/v1/sport/search',
      searchCertification: 'lookup-service/api/v1/certification/search',
      searchAward: 'lookup-service/api/v1/award/search',
      searchAuthorization: 'lookup-service/api/v1/work-authorization/search',
      searchBenefit: 'lookup-service/api/v1/benefit/search',
      searchTaxterm: 'lookup-service/api/v1/tax-term/search',
    },
    User: {
      Profile: {
        get: 'profile-service/api/v1/profile',
        getUserProfileByUserPublicID: `auth-service/api/v1/user-limited-information`,
        getUserProfileByUserPublicIDLight: `auth-service/api/v1/user-limited-information/light`,
        getAllProfiles: 'profile-service/api/v1/profiles',
      },
      Update: {
        webSites: 'profile-service/api/v1/links',
        cover: 'profile-service/api/v1/header-image',
        biography: 'profile-service/api/v1/bio',
      },
      Experiences: {
        get: 'profile-service/api/v1/experience',
      },
      Volunteers: {
        get: 'profile-service/api/v1/volunteer',
      },
      Education: {
        get: 'profile-service/api/v1/education',
      },
      Course: {
        get: 'profile-service/api/v1/course',
      },
      Licence: {
        get: 'profile-service/api/v1/certification',
      },
      Publication: {
        get: 'profile-service/api/v1/publication',
      },
      Honor: {
        get: 'profile-service/api/v1/award',
      },
      Patent: {
        get: 'profile-service/api/v1/patent',
      },
      Language: {
        get: 'profile-service/api/v1/language',
      },
      Skill: {
        get: 'profile-service/api/v1/skill',
        one: (id: number): string => `profile-service/api/v1/skill/${id}`,
      },
      Recommendation: {
        ask: 'profile-service/api/v1/recommendation/ask',
        revision: (id: string): string =>
          `profile-service/api/v1/recommendation/${id}/ask-revision`,
        decline: (id: string): string =>
          `profile-service/api/v1/recommendation/${id}/decline`,
        delete: (id: string): string =>
          `profile-service/api/v1/recommendation/${id}`,
        accept: (id: string): string =>
          `profile-service/api/v1/recommendation/${id}/accept`,
        recommend: 'profile-service/api/v1/recommendation',
        writeRecommend: (id: string): string =>
          `profile-service/api/v1/recommendation/${id}`,
      },
      Resume: {
        get: 'profile-service/api/v1/resume',
      },
    },
    Page: {
      base: 'page-service/api/v1/page',
      get: 'page-service/api/v1/page',
      getLight: 'page-service/api/v1/page/light',
      deleteStatus: 'page-service/api/v1/page/delete/status',
      validatePageUsername: 'page-service/api/v1/validate-page-username',
      myPages: 'page-service/api/v1/page-member/me',
      pageMember: 'page-service/api/v1/page-member',
      portalAccess: 'page-service/api/v1/portal-access',
      transferOwnership: 'page-service/api/v1/transfer-ownership',
      portalMember: 'page-service/api/v1/portal-member',
      getMembers: 'page-service/api/v1/page-members',
      setAvatar: (id: string): string => `page-service/api/v1/page/${id}/image`,
      setHeaderBackground: (id: string): string =>
        `page-service/api/v1/page/${id}/header-image`,
      publish: (id: string): string => `page-service/api/v1/page/${id}/publish`,
      getFeaturePrice: (featureName: string) =>
        `page-service/api/v1/feature/price/${featureName}`,
    },
    Post: {
      create: 'post-service/api/v1/post',
      feedList: 'post-search-service/api/v1/feed',
      feedListByHashtag: 'post-search-service/api/v1/posts/by-hashtag',
    },
    profile: {
      addExperience: 'profile-service/api/v1/experience',
    },
    search: {
      suggestObject: '/user-search-service/api/v1/suggest-user',
      getSuggestCompany: `/user-search-service/api/v1/suggest-company`,
    },
    companyService: {
      getVendorsIncluded: `/company-service/api/v1/vendors/get`,
      getVendorsExcluded: (id: string) =>
        `/company-service/api/v1/vendors/${id}`,
      postMultiJobSubmit: '/company-service/api/v1/job/multi-submit',

      suggestObject: 'user-search-service/api/v1/suggest-user',
    },
    Project: {
      base: 'project-service/api/v1/project',
      addJobs: 'project-service/api/v1/project/job',
      search: 'project-service/api/v1/project/search',
      getPastMeetings: (id: string) =>
        `project-service/api/v1/project/meeting/past/${id}`,
      getUpcomingMeetings: (id: string) =>
        `project-service/api/v1/project/meeting/upcoming/${id}`,
      getTodos: (id: string) => `project-service/api/v1/project/todo/${id}`,
      collaborators: (projectId: string) =>
        `project-service/api/v1/project/collaborator/${projectId}`,
      openStatusProject: (projectId: string) =>
        `project-service/api/v1/project/open/${projectId}`,
      archiveStatusProject: (projectId: string) =>
        `project-service/api/v1/project/archive/${projectId}`,
      checkTitle: `project-service/api/v1/project/exist`,
      edit: (projectId: string) =>
        `project-service/api/v1/project/${projectId}`,
      tags: (projectId: string) =>
        `project-service/api/v1/project/tag/${projectId}`,
    },
    Job: {
      base: 'job-service/api/v1/job',
      getApplication: (jobId: string) =>
        `job-service/api/v1/job/application/${jobId}`,
      changePipelineOfApplicant: (applicationId: string) =>
        `job-service/api/v1/pipeline/${applicationId}`,
      getCandidacy: (jobId: string) => `job-service/api/v1/candidacy/${jobId}`,
      changePipelineOfCandidate: (candidateId: string) =>
        `job-service/api/v1/candidacy/pipeline/${candidateId}`,
      getCollaborators: (jobId: string) =>
        `job-service/api/v1/job/collaborator/${jobId}`,
      getActivities: (jobId: string) =>
        `job-service/api/v1/activity/job/${jobId}`,
      getCandidateActivities: (candidateId: string) =>
        `job-service/api/v1/activity/candidate/${candidateId}`,
      getReviews: `job-service/api/v1/job/review`,
      getProjectActivities: (projectId: string) =>
        `job-service/api/v1/activity/project/${projectId}`,
      getProjectApplicants: (projectId: string) =>
        `job-service/api/v1/application/project/${projectId}`,
      getProjectCandidates: (projectId: string) =>
        `job-service/api/v1/candidacy/project/${projectId}`,
      getJobProjects: (projectId: string) =>
        `job-service/api/v1/job/project/${projectId}`,
      collaborators: (jobId: string) =>
        `job-service/api/v1/job/collaborator/${jobId}`,
      tags: (jobId: string) => `job-service/api/v1/job/tag/${jobId}`,
      previousCL: `job-service/api/v1/cover-letter/last`,
      addCandidate: `job-service/api/v1/candidacies`,
      getAllJobCandidacy: (jobId: string) =>
        `job-service/api/v1/candidate/id/${jobId}`,
      searchActivities: `job-search-service/api/v1/activity/search`,
      searchTodo: `job-search-service/api/v1/activity/search`,
      submittedUnsubmitted: `job-search-service/api/v1/job/submitted-unsubmitted/suggest`,
    },
  },
};

export default Endpoints;
