import type { TemplateCategoryType } from '@shared/utils/api/template';

const TEMPLATE_SERVICE = 'template-service';
const API_VERSION = '/api/v1';

const templateEndPoints = {
  searchAll: (category: TemplateCategoryType) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/${category}/search`,
  getAll: `${TEMPLATE_SERVICE}${API_VERSION}/template/email`,

  byId: (category: TemplateCategoryType, id?: string) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/${category}${id ? `/${id}` : ''}`,

  toggleDefault: (category: TemplateCategoryType, id: string) =>
    `${TEMPLATE_SERVICE}${API_VERSION}/template/${category}/default/${id}`,
};

export default templateEndPoints;
