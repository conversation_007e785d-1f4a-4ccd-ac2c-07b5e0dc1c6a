import type {
  ValueLabelType,
  LanguageListType,
  TimeFormatType,
  DateFormatType,
  GenderType,
  AgeRangeType,
  RaceType,
  CriminalRecordType,
  VeteranStatusType,
  DisabilityStatusType,
  CandidateStatusType,
  NoticePeriodType,
  RelocationStatusType,
  JobSiteType,
  ValueLabelIconType,
  IDDocTypes,
} from 'shared/types/general';
import type { ColorsKeys } from '@shared/uikit/types';
import type { IconProps } from '@shared/uikit/Icon/types';
import type { WorkStatusType, ResumePermitType } from 'shared/types/profile';
import type { DBStaticItemProps } from '@shared/types/generalProps';
import { ScheduleEventTypes } from './scheduleEventTypes';
import QueryKeys from '../queryKeys';
import * as db from './db';
import * as jobsDb from './jobsDb';
import * as schedulesDb from './schedulesDb';

const TAB_PAGES_HEADER_SEARCH_ID = 'tabPagesHeaderSearch';
const MAIN_CENTER_WRAPPER_ID = 'mainCenterWrapper';
const APP_DESKTOP_FOOTER = 'appDesktopFooter';
const CREATE_POST_BOX = 'CREATE_POST_BOX';
export const privacyValues = {
  ONLY_ME: 'ONLY_ME',
  MY_FOLLOWERS: 'MY_FOLLOWERS',
  EVERYONE_AT_LOBOX: 'EVERYONE_AT_LOBOX',
} as const;

const INVITE_STATUS = {
  invite: 'INVITE',
  madeInvitationRequest: 'MADEINVITATIONREQUEST',
  follow: 'FOLLOW',
  madeFollowRequest: 'MADEFOLLOWREQUEST',
  pending: 'PENDING',
  madeInvitationByEmail: 'MADEINVITATIONBYEMAIL',
  accepted: 'ACCEPTED',
  blocked: 'BLOCKED',
} as const;

const WORK_STATUS_VALUES: {
  [key in WorkStatusType]: ValueLabelType<WorkStatusType>;
} = {
  CITIZEN: {
    value: 'CITIZEN',
    label: 'Citizen of the country',
  },
  PERMANENT: {
    value: 'PERMANENT',
    label: 'Permanent work permit',
  },
  TEMPORARY: {
    value: 'TEMPORARY',
    label: 'Temporary work permit',
  },
  CONTRACT: {
    value: 'CONTRACT',
    label: 'Contract-based work permit',
  },
  SEASONAL: {
    value: 'SEASONAL',
    label: 'Seasonal work permit',
  },
  NO: {
    value: 'NO',
    label: 'No work permit',
  },
  OTHER: {
    value: 'OTHER',
    label: 'Other (Please specify)',
  },
} as const;

const RESUME_LINK_VALUES: {
  [key in ResumePermitType]: ValueLabelType<ResumePermitType>;
} = {
  RECRUITERS: {
    value: 'RECRUITERS',
    label: 'Recruiters',
  },
  FOLLOWERS: {
    value: 'FOLLOWERS',
    label: 'Followers',
  },
  EVERYONE: {
    value: 'EVERYONE',
    label: 'Everyone',
  },
  ONLY_SHARE: {
    value: 'ONLY_SHARE',
    label: 'Only people I share',
  },
} as const;

const NOTICE_PERIOD_VALUES: {
  [key in NoticePeriodType]: ValueLabelType<NoticePeriodType>;
} = {
  IMMEDIATELY: {
    value: 'IMMEDIATELY',
    label: 'IMMEDIATELY',
  },
  ONE_WEEK: {
    value: 'ONE_WEEK',
    label: 'ONE_WEEK',
  },
  TWO_WEEKS: {
    value: 'TWO_WEEKS',
    label: 'TWO_WEEKS',
  },
  ONE_MONTH: {
    value: 'ONE_MONTH',
    label: 'ONE_MONTH',
  },
  TWO_MONTHS: {
    value: 'TWO_MONTHS',
    label: 'TWO_MONTHS',
  },
  THREE_MONTHS: {
    value: 'THREE_MONTHS',
    label: 'THREE_MONTHS',
  },
  MORE_THAN_THERE_MONTHS: {
    value: 'MORE_THAN_THERE_MONTHS',
    label: 'MORE_THAN_THERE_MONTHS',
  },
} as const;

const LANGUAGES_LIST_VALUES: {
  [key in LanguageListType]: ValueLabelType<LanguageListType> & {
    isoKey: string;
  };
} = {
  english: {
    value: 'english',
    label: 'English',
    isoKey: 'en',
  },
  turkish: {
    value: 'turkish',
    label: 'Türkçe',
    isoKey: 'tr',
  },
} as const;

const TIME_FORMAT_VALUES: {
  [key in TimeFormatType]: ValueLabelType<TimeFormatType>;
} = {
  '24': {
    value: '24',
    label: '24 hours',
  },
  '12': {
    value: '12',
    label: '12 hours',
  },
} as const;

const DATE_FORMAT_VALUES: {
  [key in DateFormatType]: ValueLabelType<DateFormatType>;
} = {
  LONG: {
    value: 'LONG',
    label: 'Sep 16, 2021',
  },
  SHORT: {
    value: 'SHORT',
    label: '16 Sep 2021',
  },
  YMD: {
    value: 'YMD',
    label: '2021/09/16',
  },
  YDM: {
    value: 'YDM',
    label: '2021/16/09',
  },
  MDY: {
    value: 'MDY',
    label: '09/16/2021',
  },
  DMY: {
    value: 'DMY',
    label: '16/09/2021',
  },
};

const GENDER_VALUES: {
  [key in GenderType]: ValueLabelType<GenderType>;
} = {
  MALE: { label: 'male', value: 'MALE' },
  FEMALE: { label: 'female', value: 'FEMALE' },
  NON_BINARY: { label: 'Non-Binary', value: 'NON_BINARY' },
  TRANSGENDER: { label: 'Transgender', value: 'TRANSGENDER' },
  GAY: { label: 'Gay', value: 'GAY' },
  LESBIAN: { label: 'Lesbian', value: 'LESBIAN' },
  BISEXUAL: { label: 'Bisexual', value: 'BISEXUAL' },
  ASEXUAL: { label: 'Asexual', value: 'ASEXUAL' },
  PREFER_NOT_TO_SAY: { label: 'prefer_not_to_say', value: 'PREFER_NOT_TO_SAY' },
};

const AGE_RANGE_VALUES: {
  [key in AgeRangeType]: ValueLabelType<AgeRangeType>;
} = {
  A: { label: '18-24', value: 'A' },
  B: { label: '25-34', value: 'B' },
  C: { label: '35-44', value: 'C' },
  D: { label: '45-54', value: 'D' },
  E: { label: '55-64', value: 'E' },
  F: { label: '65+', value: 'F' },
};

const RACE_VALUES: {
  [key in RaceType]: ValueLabelType<RaceType>;
} = {
  WHITE: { label: 'race_WHITE', value: 'WHITE' },
  ASIAN: { label: 'race_ASIAN', value: 'ASIAN' },
  HISPANIC: { label: 'race_HISPANIC', value: 'HISPANIC' },
  NATIVE_AMERICAN: { label: 'race_NATIVE_AMERICAN', value: 'NATIVE_AMERICAN' },
  MIDDLE_EASTERN: { label: 'race_MIDDLE_EASTERN', value: 'MIDDLE_EASTERN' },
  BLACK: { label: 'race_BLACK', value: 'BLACK' },
  PREFER_NOT_TO_SAY: {
    label: 'prefer_not_to_say',
    value: 'PREFER_NOT_TO_SAY',
  },
};

const VETERAN_STATUS_VALUES: {
  [key in VeteranStatusType]: ValueLabelType<VeteranStatusType>;
} = {
  YES: { label: 'yes_iam_veteran', value: 'YES' },
  NO: { label: 'no_iam_n_veteran', value: 'NO' },
  PREFER_NOT_TO_SAY: {
    label: 'prefer_not_to_say',
    value: 'PREFER_NOT_TO_SAY',
  },
};

const RELOCATION_STATUS_VALUES: {
  [key in RelocationStatusType]: ValueLabelType<RelocationStatusType>;
} = {
  YES: { label: 'yes', value: 'YES' },
  NO: { label: 'no', value: 'NO' },
  PREFER_NOT_TO_SAY: {
    label: 'prefer_not_to_say',
    value: 'PREFER_NOT_TO_SAY',
  },
};

const DISABILITY_STATUS_VALUES: {
  [key in DisabilityStatusType]: ValueLabelType<DisabilityStatusType>;
} = {
  Yes: { label: 'yes_i_have_disability', value: 'Yes' },
  No: { label: 'no_i_have_n_disability', value: 'No' },
  PREFER_NOT_TO_SAY: {
    label: 'prefer_not_to_say',
    value: 'PREFER_NOT_TO_SAY',
  },
};

const ID_DOC_TYPE_VALUES: {
  [key in IDDocTypes]: ValueLabelType<IDDocTypes>;
} = {
  TRANSCRIPTS: { label: 'id_doc_type_TRANSCRIPTS', value: 'TRANSCRIPTS' },
  EML_FILE: { label: 'id_doc_type_EML_FILE', value: 'EML_FILE' },
  PASSPORT: { label: 'id_doc_type_PASSPORT', value: 'PASSPORT' },
  DRIVING_LICENCE: {
    label: 'id_doc_type_DRIVING_LICENCE',
    value: 'DRIVING_LICENCE',
  },
  RESUME: { label: 'id_doc_type_RESUME', value: 'RESUME' },
  SSN: { label: 'id_doc_type_SSN', value: 'SSN' },
  DIPLOMA: { label: 'id_doc_type_DIPLOMA', value: 'DIPLOMA' },
};

const CRIMINAL_RECORD_VALUES: {
  [key in CriminalRecordType]: ValueLabelType<CriminalRecordType>;
} = {
  CLEAN_RECORD: { label: 'CLEAN_RECORD', value: 'CLEAN_RECORD' },
  TRAFFIC_VIOLATIONS: {
    label: 'TRAFFIC_VIOLATIONS',
    value: 'TRAFFIC_VIOLATIONS',
  },
  EXPUNGED_RECORDS: { label: 'EXPUNGED_RECORDS', value: 'EXPUNGED_RECORDS' },
  FELONIES: { label: 'FELONIES', value: 'FELONIES' },
  COMPLETED_PROBATION: {
    label: 'COMPLETED_PROBATION',
    value: 'COMPLETED_PROBATION',
  },
  MISDEMEANORS: { label: 'MISDEMEANORS', value: 'MISDEMEANORS' },
  UNDER_INVESTIGATION: {
    label: 'UNDER_INVESTIGATION',
    value: 'UNDER_INVESTIGATION',
  },
};

const CANDIDATE_STATUS_VALUES: {
  [key in CandidateStatusType]: ValueLabelType<CandidateStatusType>;
} = {
  YES: { label: 'open_to_work', value: 'YES' },
  NO: { label: 'not_open_to_work', value: 'NO' },
  PREFER_NOT_TO_SAY: {
    label: 'prefer_not_to_say',
    value: 'PREFER_NOT_TO_SAY',
  },
};

const JOB_SITE_VALUES: {
  [key in JobSiteType]: ValueLabelType<JobSiteType>;
} = {
  NAUKRI: { label: 'jbs_NAUKRI', value: 'NAUKRI' },
  INDEED: { label: 'jbs_INDEED', value: 'INDEED' },
  FACEBOOK: { label: 'jbs_FACEBOOK', value: 'FACEBOOK' },
  GLASSDOOR: { label: 'jbs_GLASSDOOR', value: 'GLASSDOOR' },
  LINKED_IN: { label: 'jbs_LINKED_IN', value: 'LINKED_IN' },
  CAMPUS_PORTAL: { label: 'jbs_CAMPUS_PORTAL', value: 'CAMPUS_PORTAL' },
  POST_JOB_FREE: { label: 'jbs_POST_JOB_FREE', value: 'POST_JOB_FREE' },
  EMPLOYEE_REFERRAL: {
    label: 'jbs_EMPLOYEE_REFERRAL',
    value: 'EMPLOYEE_REFERRAL',
  },
  WINDOWS_PLUGIN: { label: 'jbs_WINDOWS_PLUGIN', value: 'WINDOWS_PLUGIN' },
  PASSIVE_SOURCING: {
    label: 'jbs_PASSIVE_SOURCING',
    value: 'PASSIVE_SOURCING',
  },
  ADZUNA: { label: 'jbs_ADZUNA', value: 'ADZUNA' },
  TWITTER: { label: 'jbs_TWITTER', value: 'TWITTER' },
  RESUME_INBOX: { label: 'jbs_RESUME_INBOX', value: 'RESUME_INBOX' },
  WALK_IN: { label: 'jbs_WALK_IN', value: 'WALK_IN' },
  OTHERS: { label: 'jbs_OTHERS', value: 'OTHERS' },
  JOBSITE: { label: 'jbs_JOBSITE', value: 'JOBSITE' },
  DICE: { label: 'jbs_DICE', value: 'DICE' },
  ZIPRECRUITER: { label: 'jbs_ZIPRECRUITER', value: 'ZIPRECRUITER' },
  CRAIGLIST: { label: 'jbs_CRAIGLIST', value: 'CRAIGLIST' },
  OFFICE_365: { label: 'jbs_OFFICE_365', value: 'OFFICE_365' },
  TECH_FETCH: { label: 'jbs_TECH_FETCH', value: 'TECH_FETCH' },
  EMPLOYMENT_AGENCY: {
    label: 'jbs_EMPLOYMENT_AGENCY',
    value: 'EMPLOYMENT_AGENCY',
  },
  STACK_OVERFLOW: { label: 'jbs_STACK_OVERFLOW', value: 'STACK_OVERFLOW' },
  CAREER_BUILDER: { label: 'jbs_CAREER_BUILDER', value: 'CAREER_BUILDER' },
  INTERNAL_JOB_POSTING: {
    label: 'jbs_INTERNAL_JOB_POSTING',
    value: 'INTERNAL_JOB_POSTING',
  },
  JOB_FAIR: { label: 'jbs_JOB_FAIR', value: 'JOB_FAIR' },
  OUTLOOK: { label: 'jbs_OUTLOOK', value: 'OUTLOOK' },
  REFERRAL_PORTAL: { label: 'jbs_REFERRAL_PORTAL', value: 'REFERRAL_PORTAL' },
  USER_REFERRAL: { label: 'jbs_USER_REFERRAL', value: 'USER_REFERRAL' },
  SCHOOL_SPRING: { label: 'jbs_SCHOOL_SPRING', value: 'SCHOOL_SPRING' },
  MONSTER: { label: 'jbs_MONSTER', value: 'MONSTER' },
  GMAIL: { label: 'jbs_GMAIL', value: 'GMAIL' },
  CAREER_PORTAL: { label: 'jbs_CAREER_PORTAL', value: 'CAREER_PORTAL' },
  NEUVOO: { label: 'jbs_NEUVOO', value: 'NEUVOO' },
};

export type CandidateSourceTypes = 'ALL' | 'LOBOX' | 'MANUAL' | 'SOCIAL';

export type DegreeTypes =
  | 'BACHELOR'
  | 'MASTER'
  | 'DOCTORAL'
  | 'ASSOCIATE'
  | 'POSTDOCTORAL'
  | 'MIDDLE_SCHOOL'
  | 'HIGH_SCHOOL'
  | 'ELEMENTARY'
  | 'PROFESSIONAL'
  | 'DIPLOMA'
  | 'CERTIFICATE'
  | 'HONORARY'
  | 'NON';

export type LastActivitiesTypes =
  | 'ANY_TIME'
  | 'PAST_YEAR'
  | 'PAST_6_MONTHS'
  | 'PAST_3_MONTHS'
  | 'PAST_MONTH'
  | 'PAST_WEEK'
  | 'PAST_24_HOURS';

const CANDIDATES_SOURCE_MAP: Record<
  CandidateSourceTypes,
  DBStaticItemProps<CandidateSourceTypes>
> = {
  ALL: { value: 'ALL', label: 'all' },
  LOBOX: { value: 'LOBOX', label: 'lobox' },
  MANUAL: { value: 'MANUAL', label: 'manually_created' },
  SOCIAL: { value: 'SOCIAL', label: 'social' },
};

const LAST_ACTIVITIES_VALUES: Record<
  LastActivitiesTypes,
  DBStaticItemProps<LastActivitiesTypes>
> = {
  ANY_TIME: { value: 'ANY_TIME', label: 'any_time' },
  PAST_YEAR: { value: 'PAST_YEAR', label: 'past_year' },
  PAST_6_MONTHS: { value: 'PAST_6_MONTHS', label: 'past_6_months' },
  PAST_3_MONTHS: { value: 'PAST_3_MONTHS', label: 'past_3_months' },
  PAST_MONTH: { value: 'PAST_MONTH', label: 'past_month' },
  PAST_WEEK: { value: 'PAST_WEEK', label: 'past_week' },
  PAST_24_HOURS: { value: 'PAST_24_HOURS', label: 'past_24_hours' },
};

const DEGREE_VALUES: Record<DegreeTypes, DBStaticItemProps<DegreeTypes>> = {
  BACHELOR: { value: 'BACHELOR', label: 'bachelor' },
  MASTER: { value: 'MASTER', label: 'master' },
  DOCTORAL: { value: 'DOCTORAL', label: 'doctoral' },
  ASSOCIATE: { value: 'ASSOCIATE', label: 'associate' },
  POSTDOCTORAL: { value: 'POSTDOCTORAL', label: 'postdoctoral' },
  MIDDLE_SCHOOL: { value: 'MIDDLE_SCHOOL', label: 'middle_school' },
  HIGH_SCHOOL: { value: 'HIGH_SCHOOL', label: 'high_school' },
  ELEMENTARY: { value: 'ELEMENTARY', label: 'elementary' },
  PROFESSIONAL: { value: 'PROFESSIONAL', label: 'professional' },
  DIPLOMA: { value: 'DIPLOMA', label: 'diploma' },
  CERTIFICATE: { value: 'CERTIFICATE', label: 'certificate' },
  HONORARY: { value: 'HONORARY', label: 'honorary' },
  NON: { value: 'NON', label: 'non' },
};

const PAGE_MEMBER_STATUS = {
  PENDING: 'PENDING',
  DECLINED: 'DECLINED',
  ACCEPTED: 'ACCEPTED',
} as const;

const PAGE_ROLES = {
  OWNER: {
    value: 'OWNER',
    label: 'owner',
  },
  ADMIN: {
    value: 'ADMIN',
    label: 'admin',
  },
  MEMBER: {
    value: 'MEMBER',
    label: 'member',
  },
  EDITOR: {
    value: 'EDITOR',
    label: 'editor',
  },
  RECRUITER: {
    value: 'RECRUITER',
    label: 'recruiter',
  },
  EXPERT: {
    value: 'EXPERT',
    label: 'expert',
  },
} as const;

const DETAILED_PAGE_ROLES = [
  'EDITOR_HEAD',
  'EDITOR_EDITOR_MANAGER',
  'EDITOR_EDITOR',
  'EDITOR_MEDIA_MANAGER',
  'EDITOR_MEDIA_DESIGNER',
  'EDITOR_COMMUNITY_MANAGER',
  'EDITOR_COMMUNITY_COORDINATOR',
  'EDITOR_ANALYST',
  'RECRUITER_HEAD',
  'RECRUITER_RECRUITER_MANAGER',
  'RECRUITER_RECRUITER',
  'RECRUITER_HIRING_MANAGER',
  'RECRUITER_EXPERT_MANAGER',
  'RECRUITER_EXPERT',
  'RECRUITER_ANALYST',
  'CAMPAIGN_HEAD',
  'CAMPAIGN_MARKETING_MANAGER',
  'CAMPAIGN_MARKETING_SPECIALIST',
  'CAMPAIGN_ADS_MANAGER',
  'CAMPAIGN_ADS_SPECIALIST',
  'CAMPAIGN_ANALYST',
  'SALES_HEAD',
  'SALES_SALES_MANAGER',
  'SALES_SALES_SPECIALIST',
  'SALES_FINANCE_MANAGER',
  'SALES_FINANCE_SPECIALIST',
  'SALES_ANALYST',
  'SERVICE_HEAD',
  'SERVICE_SERVICE_MANAGER',
  'SERVICE_SERVICE_SPECIALIST',
  'SERVICE_LEGAL_MANAGER',
  'SERVICE_LEGAL_SPECIALIST',
  'SERVICE_SUPPORT_MANAGER',
  'SERVICE_SUPPORT_SPECIALIST',
  'SERVICE_ANALYST',
] as const;

const jobStatusKeys = {
  all: 'ALL',
  open: 'OPEN',
  closed: 'CLOSED',
  archived: 'ARCHIVE',
  unpublished: 'UNPUBLISHED',
  hired: 'HIRED',
  applied: 'APPLIED',
};

const jobSelectedKeys = {
  open: 'OPEN',
  closed: 'CLOSED',
  applied: 'APPLIED',
  hired: 'HIRED',
};

const jobStatuses = [
  {
    value: jobStatusKeys.open,
    label: 'open',
  },
  {
    value: jobStatusKeys.closed,
    label: 'closed',
  },
  {
    value: jobStatusKeys.archived,
    label: 'archived',
  },
  {
    value: jobStatusKeys.unpublished,
    label: 'unpublished',
  },
];

const schedulesCreatableEvents = {
  [ScheduleEventTypes.MEETING]: ScheduleEventTypes.MEETING,
  [ScheduleEventTypes.TASK]: ScheduleEventTypes.TASK,
  [ScheduleEventTypes.REMINDER]: ScheduleEventTypes.REMINDER,
} as const;

/**
 * @deperecated Don't use this. instead use ScheduleEventTypes.
 * This needs to be cleared and removed from the project.
 */
const schedulesEventTypes = {
  ...schedulesCreatableEvents,
  [ScheduleEventTypes.OFF_DAY]: ScheduleEventTypes.OFF_DAY,
  [ScheduleEventTypes.HOLIDAY]: ScheduleEventTypes.HOLIDAY,
  [ScheduleEventTypes.BIRTHDAY]: ScheduleEventTypes.BIRTHDAY,
} as const;

export type Option = {
  label: string;
  value: string;
  color: ColorsKeys;
  backgroundColor: ColorsKeys;
  icon?: IconProps;
};

enum PriorityTypes {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  HIGHEST = 'HIGHEST',
}
const priorityItems: { [key in keyof typeof PriorityTypes]: Option } = {
  LOW: {
    label: 'priority_low',
    value: 'LOW',
    color: 'disabledGrayDark',
    backgroundColor: 'techGray_20',
    icon: {
      name: 'info-circle',
      color: 'disabledGrayDark',
      size: 13,
    },
  },
  MEDIUM: {
    label: 'priority_medium',
    value: 'MEDIUM',
    color: 'pendingOrange',
    backgroundColor: 'pendingOrange_10',
    icon: {
      name: 'info-circle',
      color: 'pendingOrange',
      size: 13,
    },
  },
  HIGH: {
    label: 'priority_high',
    value: 'HIGH',
    color: 'error',
    backgroundColor: 'error_10',
    icon: {
      name: 'info-circle',
      color: 'error',
      size: 13,
    },
  },
  HIGHEST: {
    label: 'priority_highest',
    value: 'HIGHEST',
    color: 'error',
    backgroundColor: 'error_20',
    icon: {
      name: 'info-circle',
      color: 'error',
      size: 13,
    },
  },
};

enum TicketStatusTypes {
  OPEN = 'OPEN',
  RESPONDED = 'RESPONDED',
  CLOSED = 'CLOSED',
}

const ticketStatusItems: {
  [key in keyof typeof TicketStatusTypes]: Option;
} = {
  OPEN: {
    label: 'open',
    value: 'OPEN',
    color: 'success',
    backgroundColor: 'success_10',
  },
  RESPONDED: {
    label: 'responded',
    value: 'RESPONDED',
    color: 'brand',
    backgroundColor: 'brand_10',
  },
  CLOSED: {
    label: 'closed',
    value: 'CLOSED',
    color: 'disabledGrayDark',
    backgroundColor: 'techGray_10',
  },
};

export {
  QueryKeys,
  db,
  jobStatusKeys,
  jobSelectedKeys,
  jobStatuses,
  jobsDb,
  schedulesDb,
  TAB_PAGES_HEADER_SEARCH_ID,
  MAIN_CENTER_WRAPPER_ID,
  INVITE_STATUS,
  WORK_STATUS_VALUES,
  RESUME_LINK_VALUES,
  LANGUAGES_LIST_VALUES,
  TIME_FORMAT_VALUES,
  NOTICE_PERIOD_VALUES,
  JOB_SITE_VALUES,
  DATE_FORMAT_VALUES,
  CANDIDATE_STATUS_VALUES,
  RELOCATION_STATUS_VALUES,
  CANDIDATES_SOURCE_MAP,
  LAST_ACTIVITIES_VALUES as LAST_ACTIVITIES_ITEMS,
  GENDER_VALUES,
  AGE_RANGE_VALUES,
  RACE_VALUES,
  CRIMINAL_RECORD_VALUES,
  VETERAN_STATUS_VALUES,
  DISABILITY_STATUS_VALUES,
  DEGREE_VALUES,
  PAGE_MEMBER_STATUS,
  PAGE_ROLES,
  DETAILED_PAGE_ROLES,
  CREATE_POST_BOX,
  APP_DESKTOP_FOOTER,
  ID_DOC_TYPE_VALUES,
  schedulesEventTypes,
  TicketStatusTypes,
  ticketStatusItems,
  PriorityTypes,
  priorityItems,
};
