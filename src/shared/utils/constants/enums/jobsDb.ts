import type { ValueLabelType } from '@shared/types/general';
import type {
  EmploymentType,
  PriorityType,
  ResponseTimeType,
  WorkPlaceType,
  ExperienceLevelType,
  EducationDegreeType,
  ContractDurationType,
  WillingToTravelType,
  PeriodType,
} from '@shared/types/pipelineTypes';
import type { DBStaticItemProps } from 'shared/types/generalProps';

export const SALARY_PERIOD: Array<ValueLabelType<PeriodType>> = [
  {
    value: 'HOURLY',
    label: 'hourly',
  },
  {
    value: 'DAILY',
    label: 'daily',
  },
  {
    value: 'WEEKLY',
    label: 'weekly',
  },
  {
    value: 'BIWEEKLY',
    label: 'bi_weekly',
  },
  {
    value: 'MONTHLY',
    label: 'monthly',
  },
  {
    value: 'YEARLY',
    label: 'yearly',
  },
];

export const LANGUAGES = [
  { label: 'English', value: 'en', id: '1' },
  { label: 'Spanish', value: 'es', id: '2' },
  { label: 'Turkish', value: 'tr', id: '3' },
];

export const BUSINESS_SORT_OPTIONS = [
  { label: 'Job title', value: 'job_title' },
  { label: 'Date posted', value: 'date_posted' },
  { label: 'Owned by', value: 'owned_by' },
];

export const WORK_SPACE_MODEL: Array<
  ValueLabelType<WorkPlaceType> & { tooltip: string; icon: string }
> = [
  {
    label: 'ON_SITE',
    value: 'ON_SITE',
    tooltip: 'employ_work_o_s',
    icon: 'building',
  },
  {
    label: 'HYBRID',
    value: 'HYBRID',
    tooltip: 'employ_work_o_s_off',
    icon: 'house-person-leave',
  },
  {
    label: 'REMOTE',
    value: 'REMOTE',
    tooltip: 'employ_work_off',
    icon: 'house-light',
  },
];

export const QUESTION_TYPES = [
  { label: 'Text input', value: 'TEXT_INPUT' },
  { label: 'Multiple select', value: 'MULTI_SELECT' },
  { label: 'Single select', value: 'SINGLE_SELECT' },
];

const searchGroupTypeOptions = [
  {
    label: 'All',
    value: 'ALL',
  },
  {
    label: 'Top suggestions',
    value: 'TOP_SUGGESTION',
  },

  {
    label: 'Popular',
    value: 'POPULAR',
  },
  {
    label: 'Applied',
    value: 'APPLIED',
  },
  {
    label: 'Saved',
    value: 'SAVED',
  },
];

const searchGroupTypeBusinessOptions = [
  {
    label: 'Open',
    value: 'OPEN',
  },
  {
    label: 'Closed',
    value: 'CLOSED',
  },
  {
    label: 'Archived',
    value: 'ARCHIVE',
  },
];

export const datePostedOptions = [
  {
    label: 'ANY_TIME',
    value: 'ANY_TIME',
  },
  {
    label: 'PAST_MONTH',
    value: 'PAST_MONTH',
  },
  {
    label: 'PAST_WEEK',
    value: 'PAST_WEEK',
  },
  {
    label: 'PAST_24_HOURS',
    value: 'PAST_24_HOURS',
  },
];

const workPlaceTypes = [
  {
    label: 'Remote',
    value: 'REMOTE',
  },
  {
    label: 'Hybrid',
    value: 'HYBRID',
  },
  {
    label: 'On-site',
    value: 'ON_SITE',
  },
];

export const experienceLevels: Array<ValueLabelType<ExperienceLevelType>> = [
  {
    label: 'INTERNSHIP',
    value: 'INTERNSHIP',
  },
  {
    label: 'ENTRY_LEVEL',
    value: 'ENTRY_LEVEL',
  },
  {
    label: 'MIDDLE_LEVEL',
    value: 'MIDDLE_LEVEL',
  },
  {
    label: 'SENIOR_LEVEL',
    value: 'SENIOR_LEVEL',
  },
  {
    label: 'PRINCIPAL_LEVEL',
    value: 'PRINCIPAL_LEVEL',
  },
  {
    label: 'ASSOCIATE',
    value: 'ASSOCIATE',
  },
  {
    label: 'DIRECTOR',
    value: 'DIRECTOR',
  },
  {
    label: 'EXECUTIVE',
    value: 'EXECUTIVE',
  },
];

const searchEntities = [
  {
    label: 'Posts',
    value: 'posts',
    leftIcon: 'post',
  },
  {
    label: 'People',
    value: 'people',
    leftIcon: 'people',
  },
  {
    label: 'Pages',
    value: 'pages',
    leftIcon: 'flag',
  },
  {
    label: 'Jobs',
    value: 'jobs',
    leftIcon: 'briefcase-blank',
  },
];

export const sortBy = [
  { value: 'MOST_RECENT', label: 'most_recent' },
  { value: 'MOST_RELEVANT', label: 'most_relevant' },
];

const savedJobsGroupStatus = [
  {
    label: 'Open',
    value: 'OPEN',
  },
  {
    label: 'Applied',
    value: 'APPLIED',
  },

  {
    label: 'Hired',
    value: 'HIRED',
  },
  {
    label: 'Closed',
    value: 'CLOSED',
  },
];

const appliedJobGroupStatus = [
  {
    label: 'Open',
    value: 'OPEN',
  },
  {
    label: 'Hired',
    value: 'HIRED',
  },
  {
    label: 'Closed',
    value: 'CLOSED',
  },
];

const hostedBy = [
  {
    label: 'Internal',
    value: 'internal',
  },
  {
    label: 'External',
    value: 'external',
  },
];

const jobApplicationStatus = [
  {
    label: 'applied',
    value: 'APPLIED',
  },
  {
    label: 'under_review',
    value: 'UNDER_REVIEW',
  },
  {
    label: 'selected_for_interview',
    value: 'SELECTED_FOR_INTERVIEW',
  },
  {
    label: 'interviewed',
    value: 'INTERVIEWED',
  },
  {
    label: 'reference_check',
    value: 'REFERENCE_CHECK',
  },
  {
    label: 'offer',
    value: 'OFFER',
  },
  {
    label: 'hired',
    value: 'HIRED',
  },
  {
    label: 'rejected',
    value: 'REJECTED',
  },
];

export const memberSince = [
  {
    label: 'ANY_TIME',
    value: 'ANY_TIME',
  },
  {
    label: 'PAST_YEAR',
    value: 'PAST_YEAR',
  },
  {
    label: 'PAST_6_MONTHS',
    value: 'PAST_6_MONTHS',
  },
  {
    label: 'PAST_MONTH',
    value: 'PAST_MONTH',
  },
  {
    label: 'PAST_WEEK',
    value: 'PAST_WEEK',
  },
  {
    label: 'PAST_24_HOURS',
    value: 'PAST_24_HOURS',
  },
];

export const JOB_TYPE_MODEL: DBStaticItemProps<ContractDurationType>[] = [
  {
    value: 'FULL_TIME',
    label: 'full_time',
  },
  {
    value: 'ONE_YEAR',
    label: 'one_year',
  },
  {
    value: 'SIZ_MONTHS',
    label: 'six_months',
  },
  {
    value: 'THREE_MONTHS',
    label: 'three_months',
  },
];

export const WILLING_TRAVEL: DBStaticItemProps<WillingToTravelType>[] = [
  {
    value: 'YES',
    label: 'yes',
  },
  {
    value: 'NO',
    label: 'no',
  },
  {
    value: 'OCCASIONALLY',
    label: 'occasionally',
  },
  {
    value: 'CONSIDERABLE',
    label: 'considerable',
  },
];

export const JOB_RESPONSE_TIME_MODEL: DBStaticItemProps<ResponseTimeType>[] = [
  {
    value: 'FLEXIBLE',
    label: 'flexible',
  },
  {
    value: 'IMMEDIATE',
    label: 'immediate',
  },
  {
    value: 'WITHIN_1_DAY',
    label: 'in_1_day',
  },
  {
    value: 'WITHIN_2_DAYS',
    label: 'in_2_days',
  },
  {
    value: 'WITHIN_3_DAYS',
    label: 'in_3_days',
  },
  {
    value: 'WITHIN_1_WEEK',
    label: 'in_1_week',
  },
  {
    value: 'WITHIN_2_WEEKS',
    label: 'in_2_weeks',
  },
  {
    value: 'WITHIN_1_MONTH',
    label: 'in_1_month',
  },
  {
    value: 'WITHIN_1_YEAR',
    label: 'in_1_year',
  },
];

export const JOB_PRIORITY_MODEL: DBStaticItemProps<PriorityType>[] = [
  {
    value: 'CRITICAL',
    label: 'critical',
  },
  {
    value: 'HIGH',
    label: 'high',
  },

  {
    value: 'MEDIUM',
    label: 'medium',
  },
  {
    value: 'LOW',
    label: 'low',
  },
];
export const JOB_EDUCATION_DEGREE_MODEL: DBStaticItemProps<EducationDegreeType>[] =
  [
    {
      value: 'ASSOCIATE',
      label: 'ASSOCIATE',
    },
    {
      value: 'BACHELOR',
      label: 'BACHELOR',
    },

    {
      value: 'CERTIFICATE',
      label: 'CERTIFICATE',
    },
    {
      value: 'DIPLOMA',
      label: 'DIPLOMA',
    },
    {
      value: 'DOCTORAL',
      label: 'DOCTORAL',
    },
    {
      value: 'ELEMENTARY',
      label: 'ELEMENTARY',
    },
    {
      value: 'HIGH_SCHOOL',
      label: 'HIGH_SCHOOL',
    },
    {
      value: 'HONORARY',
      label: 'HONORARY',
    },
    {
      value: 'MASTER',
      label: 'MASTER',
    },
    {
      value: 'MIDDLE_SCHOOL',
      label: 'MIDDLE_SCHOOL',
    },
    {
      value: 'NON',
      label: 'NON',
    },
    {
      value: 'POSTDOCTORAL',
      label: 'POSTDOCTORAL',
    },
    {
      value: 'PROFESSIONAL',
      label: 'PROFESSIONAL',
    },
  ];
export const JOB_EMPLOYMENT_TYPE_MODEL: DBStaticItemProps<EmploymentType>[] = [
  {
    value: 'APPRENTICESHIP',
    label: 'APPRENTICESHIP',
  },
  {
    value: 'CONTRACT',
    label: 'CONTRACT',
  },

  {
    value: 'CONTRACT_TO_HIRE',
    label: 'CONTRACT_TO_HIRE',
  },
  {
    value: 'FREELANCE',
    label: 'FREELANCE',
  },
  {
    value: 'FULL_TIME',
    label: 'FULL_TIME',
  },
  {
    value: 'INTERNSHIP',
    label: 'INTERNSHIP',
  },
  {
    value: 'PART_TIME',
    label: 'PART_TIME',
  },
  {
    value: 'SEASONAL',
    label: 'SEASONAL',
  },
  {
    value: 'SELF_EMPLOYED',
    label: 'SELF_EMPLOYED',
  },
  {
    value: 'TEMPORARY',
    label: 'TEMPORARY',
  },
  {
    value: 'VOLUNTEER',
    label: 'VOLUNTEER',
  },
];

export const JOB_STATUS = [
  { value: 'OPEN', label: 'open' },
  { value: 'ARCHIVE', label: 'archived' },
  { value: 'CLOSED', label: 'closed' },
  { value: 'UNPUBLISHED', label: 'unpublished' },
];
export const PIPELINE_STATUS_DATA = [
  { value: 'OPEN', label: 'open' },
  { value: 'CLOSED', label: 'closed' },
];

export const jobSearchOptions = {
  hostedBy,
  searchGroupType: (
    isBusinessApp: boolean
  ): {
    label: string;
    value: string;
  }[] =>
    isBusinessApp ? searchGroupTypeBusinessOptions : searchGroupTypeOptions,
  dateRangeType: datePostedOptions,
  workPlaceTypes,
  experienceLevels,
  searchEntities,
  sortBy,
  savedJobsGroupStatus,
  appliedJobGroupStatus,
  jobApplicationStatus,
  memberSince,
};
