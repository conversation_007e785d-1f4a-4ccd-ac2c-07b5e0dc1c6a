import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import type { IconName } from '@shared/uikit/Icon/types';
import type { ProjectProps } from '@shared/types/project';
import { ShareEntities, ShareEntityTab } from '@shared/types/share/entities';
import { openMultiStepForm } from './useMultiStepForm';

type ActionType = 'edit' | 'share' | 'delete';

const useRecruiterProjectMoreActions = () => {
  const appDispatch = useGlobalDispatch();

  const onEdit = (project: ProjectProps) =>
    openMultiStepForm({
      formName: 'createProjectForm',
      data: project,
    });
  const onShare = (project: ProjectProps) =>
    appDispatch({
      type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA',
      payload: {
        isOpen: true,
        tabs: [
          ShareEntityTab.COPY_LINK,
          ShareEntityTab.SHARE_VIA_MESSAGE,
          ShareEntityTab.SHARE_VIA_EMAIL,
        ],
        entityData: {
          attachment: {
            type: ShareEntities.PROJECT,
            data: {
              ...project,
              category: { label: (project.pageInfo as any)?.category },
              title: { label: project.title },
              workPlaceType: { label: project.status },
              isBusiness: true,
              type: 'project',
            },
          },
        },
      },
    });
  const onDelete = (project: ProjectProps) =>
    openMultiStepForm({
      formName: 'deleteEntityModal',
      data: project,
    });

  const onAction = (label: ActionType, project: ProjectProps) => {
    switch (label) {
      case 'edit':
        return onEdit(project);
      case 'share':
        return onShare(project);
      case 'delete':
        return onDelete(project);
      default:
        return () => {};
    }
  };

  return { actions, onAction };
};

export default useRecruiterProjectMoreActions;

const actions: {
  icon: IconName;
  label: ActionType;
}[] = [
  {
    icon: 'share',
    label: 'share',
  },
  {
    icon: 'edit',
    label: 'edit',
  },
  {
    icon: 'trash',
    label: 'delete',
  },
];
