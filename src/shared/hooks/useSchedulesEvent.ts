import useGetEventDetails from 'shared/hooks/useGetEventDetails';
import { schedulesEventTypes } from 'shared/utils/constants/enums';

import { focusOnRichText } from 'shared/utils/focusOnRichText';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import { useMemo } from 'react';
import { useStore } from '@tanstack/react-store';
import { replace, urlStateStore } from 'shared/stores/urlStateStore';
import type { BEScheduleEventTypes } from 'shared/types/schedules/schedules';
import { MeetingAttendeePermission } from 'shared/types/schedules/schedules';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { useSchedulesUrlState } from './useSchedulesUrlState';

const useSchedulesEvent = <
  T extends BEScheduleEventTypes = BEScheduleEventTypes,
>() => {
  const { authUser } = useGetAppObject();
  const state = useStore(urlStateStore, (st) => st.urlState);
  const {
    state: scheduleState,
    setScheduleEventsPanelData,
    resetUrl,
  } = useSchedulesUrlState();
  const {
    schedulesEventType = ScheduleEventTypes.MEETING,
    eventId,
    creationInitialData,
    ...rest
  } = scheduleState.scheduleEventsPanelData || {};

  const isCreationMode = !eventId;
  const queryResult = useGetEventDetails({
    type: schedulesEventType as T,
    id: eventId!,
    enabled: !isCreationMode,
  });
  const event = queryResult.data;

  const resetUrlState = () => {
    if (state.defaultSchedulesEventType) {
      replace((prev) => ({ ...prev, defaultSchedulesEventType: undefined }));
    }
  };

  const closeHandler = () => {
    resetUrl();
  };
  const backHandler = () => {
    resetUrlState();
    setScheduleEventsPanelData({
      ...rest,
      isInCrEdit: false,
      schedulesEventType: event?.schedulesEventType,
      eventId: event?.id,
    });
  };

  // Has to go away
  const hasEditAccess = useMemo(
    () =>
      (event &&
        'currentUserIsCreator' in event &&
        event.currentUserIsCreator) ||
      (event?.schedulesEventType === schedulesEventTypes.MEETING
        ? event?.attendees?.some(
            (attendee) =>
              attendee?.id === authUser?.id &&
              attendee?.permissions?.includes(
                MeetingAttendeePermission.MODIFY_MEETING
              )
          )
        : event?.schedulesEventType === schedulesEventTypes.TASK
          ? event?.assigneePermission
          : false),
    [authUser?.id, event]
  );

  if (event?.schedulesEventType === schedulesEventTypes.MEETING) {
    Object.keys(event.permissions).forEach((key) => {
      if (isCreationMode)
        event.permissions[key as MeetingAttendeePermission] = true;
    });
  }

  const handleEdit = (focusOnEditor?: boolean) => {
    if (!hasEditAccess) return;

    setScheduleEventsPanelData({
      creationInitialData,
      ...rest,
      isInCrEdit: true,
      schedulesEventType,
      eventId,
    });
    if (focusOnEditor) {
      setTimeout(focusOnRichText, 1000);
    }
  };

  return {
    schedulesEventType: schedulesEventType as T,
    eventId,
    isCreationMode,
    event,
    queryResult,
    closeHandler,
    backHandler,
    hasEditAccess,
    creationInitialData,
    handleEdit,
  };
};

export default useSchedulesEvent;
