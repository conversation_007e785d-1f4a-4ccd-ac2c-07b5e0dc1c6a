import {
  searchFilterQueryParams,
  searchGroupTypes,
} from 'shared/constants/search';
import { QueryKeys, jobStatusKeys, routeNames } from 'shared/utils/constants';
import { useQueryClient } from '@tanstack/react-query';
import uniqBy from 'lodash/uniqBy';
import { useMemo } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import useSearchEntity from '@shared/hooks/useSearchEntity';
import useDynamicFilters from './useDynamicFilters';

const useSearchQueries = () => {
  const searchEntity = useSearchEntity();
  const dynamicFilters = useDynamicFilters();
  const queryClient = useQueryClient();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const searchGroupType =
    searchParams.get(searchFilterQueryParams.searchGroupType) ||
    searchGroupTypes.ALL;
  const occupationId = searchParams.get(searchFilterQueryParams.occupationId);
  const currentEntityId = searchParams.get(
    searchFilterQueryParams.currentEntityId
  );

  const isLocationTitleFiltered = searchParams.get(
    searchFilterQueryParams.isLocationTitleFiltered
  );
  const isAllFilter = searchGroupType === searchGroupTypes.ALL;
  const isSavedFilter =
    searchParams.get(searchFilterQueryParams.searchGroupType) ===
    searchGroupTypes.SAVED;
  const jobStatus =
    searchParams.getAll(searchFilterQueryParams.status) || jobStatusKeys.open;
  const locationTitle = searchParams.get(searchFilterQueryParams.locationTitle);
  const cityCode = searchParams.get(searchFilterQueryParams.cityCode);
  const stateCode = searchParams.get(searchFilterQueryParams.stateCode);
  const countryCode = searchParams.get(searchFilterQueryParams.countryCode);
  const qSalaryPeriod = searchParams.get(searchFilterQueryParams.salaryPeriod);
  const categoryIds = searchParams.getAll(searchFilterQueryParams.categoryIds);
  const searchedQuery = decodeURIComponent(searchParams.get('query') || '');

  const jobTitles = dynamicFilters?.titles || [];
  const creators = dynamicFilters?.creators || [];
  const mentions = dynamicFilters?.mentions || [];
  const pages = dynamicFilters?.pages || [];
  const relatedPages = dynamicFilters?.relatedPages || [];
  const cities = dynamicFilters?.cities || [];
  const industries = dynamicFilters?.industries || [];
  const skills = dynamicFilters?.skills || [];
  const languages = dynamicFilters?.languages || [];
  const categories = useMemo(() => {
    const _categories = dynamicFilters?.categories || [];
    const popularCategories =
      queryClient
        .getQueryData<any>([QueryKeys.jobPopularCategories])
        ?.content?.map((item: any) => ({
          value: item?.id,
          label: item?.title,
          image: item?.imageUrl,
        })) || [];
    const newCategories: any[] = [];
    (categoryIds as string[])?.forEach((id: string) => {
      if (_categories?.includes((_item: any) => _item?.value === id)) return;
      const popular = popularCategories?.find(
        (_item: any) => _item?.value === id
      );
      if (popular) newCategories.push(popular);
    });

    return uniqBy([..._categories, ...newCategories], 'value');
  }, [dynamicFilters, categoryIds, queryClient]);
  const salaryRange = dynamicFilters?.salaryRange || [];
  const benefits = dynamicFilters?.benefits || [];
  const employmentTypes = dynamicFilters?.employmentTypes || [];
  const experienceLevels = dynamicFilters?.experienceLevels || [];
  const workPlaceTypes = dynamicFilters?.workPlaceTypes || [];
  const datePosted = dynamicFilters?.datePosted || [];
  const hashtags = dynamicFilters?.hashtags || [];
  const visibleCategory = categories?.length || categoryIds?.length;
  const companySizes = dynamicFilters?.companySizes;
  const pageTypes = dynamicFilters?.pageTypes;
  const postedByOptions = dynamicFilters?.postedBy;
  const postTypes = dynamicFilters?.postTypes;
  const sortByOptions = dynamicFilters?.sortBy;
  const memberSinceOptions = dynamicFilters?.memberSince;
  const establishmentDateOptions = dynamicFilters?.establishmentDate;
  const isAllSearchGroupType = searchGroupType === searchGroupTypes.ALL;
  const refresh = searchParams.get(searchFilterQueryParams.refresh) || 'false';
  const onlyClients =
    getQueryValue(searchFilterQueryParams.onlyClients) === 'true';

  const isPosts = useMemo(
    () => [routeNames.searchPosts].some((path) => pathname.includes(path)),
    [pathname]
  );

  function getQueryValue(
    key: (typeof searchFilterQueryParams)[keyof typeof searchFilterQueryParams],
    type: 'string' | 'array' = 'string'
  ) {
    return type === 'string'
      ? searchParams.get(key)
      : getNormalizedArrayQuery(searchParams, key);
  }

  if (searchEntity === 'companies') {
    return {
      pageTypes,
      categories,
      establishmentDateOptions,
      currentEntityId,
      searchedQuery,
      locationTitle,
      companySizes,
      languages,
      hashtags,
      getQueryValue,
      visibleCategory,
      onlyClients,
    };
  }

  return {
    searchGroupType,
    occupationId,
    currentEntityId,
    isLocationTitleFiltered,
    isAllFilter,
    isSavedFilter,
    jobStatus,
    cityCode,
    locationTitle,
    stateCode,
    countryCode,
    qSalaryPeriod,
    categoryIds,
    jobTitles,
    creators,
    pages,
    relatedPages,
    cities,
    industries,
    skills,
    languages,
    categories,
    salaryRange,
    benefits,
    employmentTypes,
    experienceLevels,
    workPlaceTypes,
    datePosted,
    hashtags,
    visibleCategory,
    companySizes,
    pageTypes,
    isAllSearchGroupType,
    searchedQuery,
    isPosts,
    postedByOptions,
    postTypes,
    sortByOptions,
    memberSinceOptions,
    establishmentDateOptions,
    mentions,
    refresh,
    getQueryValue,
  };
};

export default useSearchQueries;

function getNormalizedArrayQuery(query: any, queryName: string) {
  return (
    query
      .getAll(queryName)
      ?.map((item: any) => item?.split(/,(?!\s)/))
      // split by "," but not when it's ", "
      ?.flat()
      ?.filter((item: any) => Boolean(item))
  );
}
