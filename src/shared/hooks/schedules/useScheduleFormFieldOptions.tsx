import {
  MeetingAttendeePermission,
  TaskStatus,
} from 'shared/types/schedules/schedules';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useMemo } from 'react';

export const useScheduleFormFieldOptions = () => {
  const { t } = useTranslation();
  const taskStatusOptions = useMemo(
    () =>
      rawTaskStatusOptions.map((RTSO) => ({ ...RTSO, label: t(RTSO.label) })),
    [t]
  );

  const attendeePermissionsOptions = useMemo(
    () =>
      rawAttendeePermissionsOptions.map((RAPO) => ({
        ...RAPO,
        label: t(RAPO.label),
      })),
    [t]
  );

  const assigneePermissionOption = useMemo(
    () =>
      rawAssigneePermissionOption.map((RAPO) => ({
        ...RAPO,
        label: t(RAPO.label),
      })),
    [t]
  );

  return {
    taskStatusOptions,
    attendeePermissionsOptions,
    assigneePermissionOption,
  };
};

export const rawTaskStatusOptions = [
  {
    label: 'open',
    value: TaskStatus.OPEN,
  },
  {
    label: 'in_progress',
    value: TaskStatus.IN_PROGRESS,
  },
  {
    label: 'done',
    value: TaskStatus.DONE,
  },
] as const;

export const rawAttendeePermissionsOptions = [
  {
    label: 'modify_event',
    value: MeetingAttendeePermission.MODIFY_MEETING,
  },
  {
    label: 'invite_others',
    value: MeetingAttendeePermission.INVITE_OTHERS,
  },
  {
    label: 'see_guests_list',
    value: MeetingAttendeePermission.SEE_OTHER_ATTENDEES,
  },
] as const;

export const rawAssigneePermissionOption = [
  {
    label: 'modify_event',
    value: true,
  },
] as const;
