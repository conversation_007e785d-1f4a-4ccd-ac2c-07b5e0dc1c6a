import { useCallback, useMemo } from 'react';
import dayjs from 'dayjs';
import { schedulesEventTypes } from 'shared/utils/constants/enums';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';

import useHistory from 'shared/utils/hooks/useHistory';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useLocation from 'shared/utils/hooks/useLocation';
import type { MouseEvent } from 'react';
import {
  setDate,
  setSavedEvents,
  setTemporaryEvent,
  useSchedulesState,
} from '@shared/stores/schedulesStore';
import type { SchedulesStoreState } from '@shared/types/schedules/store';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { useSchedulesUrlState } from './useSchedulesUrlState';
import { Time } from '../utils/Time';
import {
  type CalendarBoardType,
  type DateType,
  type DayUnit,
  type EventCreationFromCalendarInitialData,
  type SavedEventType,
} from '../types/schedules/schedules';

export function useSchedulesCalendar() {
  const { t } = useTranslation();
  const scheduleEventsData = useSchedulesState();

  const { pathname } = useLocation();
  const history = useHistory();
  const globalDispatch = useGlobalDispatch();
  const { setScheduleEventsPanelData: setScheduleCreationModalData } =
    useSchedulesUrlState();
  const calendarBoardType: CalendarBoardType = useMemo(
    () =>
      (['year', 'month', 'day', 'week'].find((value) =>
        pathname.includes(value)
      ) ?? 'day') as CalendarBoardType,
    [pathname]
  );

  const setViewDateToToday = useCallback(() => {
    setDate(Time.getToday());
  }, []);

  const setViewDate = useCallback((date: DateType) => {
    setDate(date);
  }, []);
  const decrementViewDateBy = useCallback(
    (unit: DayUnit) => {
      setDate(Time.decrementDateBy(scheduleEventsData.viewDate.start, unit));
    },
    [scheduleEventsData.viewDate.start]
  );
  const incrementViewDateBy = useCallback(
    (unit: DayUnit) => {
      setDate(Time.incrementDateBy(scheduleEventsData.viewDate.start, unit));
    },
    [scheduleEventsData.viewDate.start]
  );

  const handleIncrement = useCallback(() => {
    incrementViewDateBy(calendarBoardType);
  }, [calendarBoardType, incrementViewDateBy]);

  const handleDecrement = useCallback(() => {
    decrementViewDateBy(calendarBoardType);
  }, [calendarBoardType, decrementViewDateBy]);

  const setCalendarBoardType = useCallback(
    (boardType: CalendarBoardType) => {
      history.push(boardType);
    },
    [history]
  );

  const openCreateEventWithDate = useCallback(
    (
      date: DateType = Time.getNextSelectableTime(dayjs()),
      initialData: EventCreationFromCalendarInitialData = {},
      isInCandidateManager: boolean = false
    ) => {
      const payload: EventCreationFromCalendarInitialData = {
        startDate: Time.getFormDate(date),
        startTime: Time.getFormTime(date),
        ...initialData,
      };

      setTemporaryEvent({
        id: `${Number.MAX_SAFE_INTEGER}`,
        title: t('no_title'),
        type: ScheduleEventTypes.TEMPORARY,
        start: '',
        startTime: date,
        end: '',
        endTime: date.clone().add(1, 'h'),
        isAllDay: false,
        uniqueEventId: `${Number.MAX_SAFE_INTEGER}`,
      });
      setScheduleCreationModalData({
        isInCrEdit: true,
        isInCandidateManager,
        schedulesEventType:
          payload?.schedulesEventType || schedulesEventTypes.MEETING,
        creationInitialData: payload,
      });
    },
    [setScheduleCreationModalData, t]
  );

  const handleEventClick = useCallback(
    (
      e: MouseEvent<HTMLDivElement, MouseEvent> | undefined, // Srsly?
      event: SavedEventType & { isFromNotification?: boolean }
    ) => {
      e?.stopPropagation();
      if (event?.type === 'TEMPORARY') return;
      globalDispatch({
        type: 'SET_SHOW_RIGHT_PANEL',
        payload: true,
      });
      setScheduleCreationModalData({
        isInCrEdit: false,
        schedulesEventType: event?.type,
        eventId: event?.id,
        isFromNotification: event?.isFromNotification,
      });
    },
    [setScheduleCreationModalData, globalDispatch]
  );

  const setEvents = useCallback(
    (events: SchedulesStoreState['savedEvents']) => {
      setSavedEvents(events);
    },
    []
  );
  return {
    data: scheduleEventsData,
    setViewDate,
    setViewDateToToday,
    decrementViewDateBy,
    incrementViewDateBy,
    calendarBoardType,
    handleDecrement,
    handleIncrement,
    setCalendarBoardType,
    openCreateEventWithDate,
    handleEventClick,
    setEvents,
  };
}
