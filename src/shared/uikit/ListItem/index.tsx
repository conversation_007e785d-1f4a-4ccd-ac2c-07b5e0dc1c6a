import React, { useRef } from 'react';
import classes from './index.module.scss';

import cnj from '../utils/cnj';
import Typography from '../Typography';
import Icon from '../Icon';
import BaseButton from '../Button/BaseButton';
import Flex from '../Flex';
import type { IconFontType, IconName } from '../Icon/types';
import type { colorsKeys } from '../helpers/theme';
import type { TypographyProps } from '../Typography';
import OverflowTip from '../Typography/OverflowTip';

export interface ListItemProps {
  onClick?: (event: React.MouseEvent<HTMLElement>) => any;
  link?: string;
  className?: string;
  popperItem?: boolean;
  narrow?: boolean;
  autoWide?: boolean;
  leftSvg?: React.ReactNode;
  leftIcon?: IconName;
  leftType?: IconFontType;
  leftSize?: number;
  leftColor?: colorsKeys;
  leftClassName?: { wrapper?: string; icon?: string };
  rightSvg?: React.ReactNode;
  rightIcon?: IconName;
  rightType?: IconFontType;
  rightSize?: number;
  rightColor?: colorsKeys;
  renderContent?: React.ReactNode;
  action?: React.ReactNode | (() => React.ReactNode);
  label?: string;
  secondaryLabel?: string;
  labelSize?: number;
  secondaryLabelSize?: number;
  secondaryLabelClassName?: string;
  labelColor?: colorsKeys;
  labelFont?: any;
  secondaryLabelColor?: colorsKeys;
  labelClassName?: string;
  labelsContainerClassName?: string;
  labelTruncated?: boolean;
  secondaryLabelTruncated?: boolean;
  hover?: boolean;
  hoverBgColor?: colorsKeys;
  hoverColor?: colorsKeys;
  labelProps?: Omit<TypographyProps, 'children'>;
  secondaryLabelProps?: Omit<TypographyProps, 'children'> & {
    showTooltip?: boolean;
  };
  hasNarrowLabel?: boolean;
  prefetch?: boolean;
  disabled?: boolean;
}

const ListItem = ({
  onClick,
  link,
  className,
  popperItem,
  narrow,
  autoWide,
  leftSvg,
  leftIcon,
  leftType,
  leftSize,
  leftColor,
  leftClassName,
  rightSvg,
  rightIcon,
  rightType,
  rightSize,
  rightColor,
  renderContent,
  action,
  label,
  secondaryLabel,
  labelSize = 16,
  labelFont,
  secondaryLabelSize = 16,
  labelColor,
  secondaryLabelColor,
  secondaryLabelClassName,
  labelClassName,
  labelsContainerClassName,
  labelTruncated,
  secondaryLabelTruncated,
  hover,
  hoverBgColor,
  hoverColor,
  labelProps: lP,
  secondaryLabelProps: sLP,
  hasNarrowLabel,
  prefetch,
  disabled,
}: ListItemProps): JSX.Element => {
  const ref = useRef<any>(null);
  const { className: lClass, ...labelProps } = lP || {};
  const { className: slClass, ...secondaryLabelProps } = sLP || {};

  return (
    <BaseButton
      prefetch={prefetch}
      to={link}
      onClick={(e) => {
        if (disabled) {
          e.stopPropagation();
          e.preventDefault();
          return;
        }
        onClick?.(e);
      }}
      className={cnj(
        classes.listItemRoot,
        popperItem && classes.listItemRootPopper,
        hover && !disabled && classes.hover,
        disabled && classes.disabled,
        classes[`hoverBg-${hoverBgColor}`],
        classes[`hoverColor-${hoverColor}`],
        className
      )}
    >
      {leftIcon && (
        <Flex
          className={cnj(classes.leftIconContainer, leftClassName?.wrapper)}
        >
          <Icon
            className={cnj(classes.leftIcon, leftClassName?.icon)}
            type={leftType}
            name={leftIcon}
            color={leftColor}
            size={leftSize}
          />
        </Flex>
      )}
      {leftSvg}
      {hasNarrowLabel && (
        <Typography
          ref={ref}
          className={cnj(
            classes.label,
            labelClassName,
            lClass,
            'listItemLabel'
          )}
          size={labelSize}
          color={labelColor}
          font={labelFont}
          isTruncated={labelTruncated}
          {...labelProps}
        >
          {label}
        </Typography>
      )}
      {!narrow && (
        <>
          <Flex className={cnj(classes.container, labelsContainerClassName)}>
            {renderContent || (
              <>
                <OverflowTip
                  className={cnj(
                    classes.narrowSituation,
                    autoWide && classes.narrowSituationWide,
                    classes.label,
                    labelClassName,
                    lClass,
                    'listItemLabel'
                  )}
                  size={labelSize}
                  color={labelColor}
                  font={labelFont}
                  {...labelProps}
                >
                  {label}
                </OverflowTip>

                {secondaryLabel && (
                  <OverflowTip
                    className={cnj(
                      classes.narrowSituation,
                      autoWide && classes.narrowSituationWide,
                      classes.secondaryLabel,
                      labelClassName,
                      secondaryLabelClassName,
                      slClass
                    )}
                    size={secondaryLabelSize}
                    color={secondaryLabelColor}
                    isTruncated={secondaryLabelTruncated}
                    {...secondaryLabelProps}
                  >
                    {secondaryLabel}
                  </OverflowTip>
                )}
              </>
            )}
          </Flex>
          {rightIcon && (
            <Icon
              type={rightType}
              name={rightIcon}
              className={cnj(
                classes.narrowSituation,
                autoWide && classes.narrowSituationWide,
                classes.rightIcon,
                'rightIcon'
              )}
              color={rightColor}
              size={rightSize}
            />
          )}
          {action
            ? typeof action === 'function'
              ? action()
              : action
            : rightSvg}
        </>
      )}
    </BaseButton>
  );
};

export default ListItem;
