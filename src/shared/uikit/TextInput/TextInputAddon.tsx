import type { PropsWithChildren } from 'react';
import React from 'react';
import Flex from '../Flex/Flex.component';
import type { IconName, IconProps } from '../Icon/types';
import type { TypographyProps } from '../Typography';
import Typography from '../Typography';
import Icon from '../Icon';
import classes from './TextInputAddon.module.scss';
import cnj from '../utils/cnj';

interface InputAddonProps {
  left?: true;
  right?: true;
  label?: string;
  labelProps?: TypographyProps;
  icon?: IconName;
  iconProps?: IconProps;
  iconSize?: number;
  className?: string;
  iconClassName?: string;
  labelClassName?: string;
}

const TextInputAddon: React.FC<PropsWithChildren<InputAddonProps>> = ({
  left,
  right,
  icon,
  iconProps,
  iconSize,
  label,
  labelProps,
  children,
  className,
  iconClassName,
  labelClassName,
}) => (
  <Flex
    flexDir="row"
    className={cnj(
      classes.wrapper,
      left && classes.left,
      right && classes.right,
      className
    )}
  >
    {icon && (
      <Icon
        {...iconProps}
        name={icon}
        type="far"
        size={iconSize}
        className={iconClassName}
      />
    )}
    <Typography {...labelProps} className={labelClassName}>
      {label}
    </Typography>
    {children}
  </Flex>
);

export default TextInputAddon;
