import type { FC } from 'react';
import { jobsDb } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { DBStaticItemProps } from 'shared/types/generalProps';
import { priorityIconDetails } from '@shared/constants/priorityDetails';
import type { PriorityType } from '@shared/types/pipelineTypes';
import AutoComplete from '../AutoComplete';
import useMedia from '../utils/useMedia';
import Icon from '../Icon';
import Flex from '../Flex';
import type { AutoCompleteProps } from '../types';

interface PriorityDropdownProps extends AutoCompleteProps {
  onChange?: Function;
  value?: DBStaticItemProps<PriorityType>;
}

const PriorityDropdown: FC<PriorityDropdownProps> = ({
  onChange: parentOnChange,
  value,
  visibleRightIcon = true,
  optionsVariant,
  ...rest
}) => {
  const onSelect = (item: DBStaticItemProps<PriorityType>) =>
    parentOnChange?.(item);
  const { isMoreThanTablet } = useMedia();
  const { t } = useTranslation();

  return (
    <AutoComplete
      {...rest}
      onSelect={onSelect}
      editable={false}
      displayName={value?.label}
      value={value}
      visibleRightIcon={visibleRightIcon}
      optionsVariant={
        optionsVariant || isMoreThanTablet ? 'dropdown' : 'bottomsheet'
      }
      leftIcon={
        !!value && (
          <Flex style={{ justifyContent: 'center', alignItems: 'center' }}>
            <Icon
              name={priorityIconDetails[value.value].name}
              type="far"
              color={priorityIconDetails[value.value].color}
            />
          </Flex>
        )
      }
      options={jobsDb.JOB_PRIORITY_MODEL.map((item) => ({
        ...item,
        label: t(item.label),
      }))}
      hasPriorityData
    />
  );
};

export default PriorityDropdown;
