import React, { useEffect, useState } from 'react';
import { getHashtagsByText } from 'shared/utils/api/post';
import QueryKeys from 'shared/utils/constants/queryKeys';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import cnj from 'shared/uikit/utils/cnj';
import uniq from 'lodash/uniq';
import Skeleton from 'shared/uikit/Skeleton';
import AlertMessage from '../AlertMessage';
import IconButton from '../Button/IconButton';
import type { IconProps } from '../Icon/types';
import Icon from '../Icon';
import Flex from '../Flex';
import useMedia from '../utils/useMedia';
import HashtagInput from './HashtagInput';
import HashtagModal from './Hashtag.Modal';
import classes from './HashtagPicker.component.module.scss';
import Tooltip from '../Tooltip';
import Typography from '../Typography';

const maxLength = 30;

export interface HashTagPickerProps {
  onChange: (value: string[]) => void;
  value: string[];
  className?: string;
  onFocus?: (e: React.ChangeEvent<HTMLInputElement> | boolean | number) => void;
  isFocus?: boolean | number;
  tooltip: string;
  text?: string;
  isEditMode?: boolean;
  toolTipProps?: IconProps;
}

const MAXIMUM_LIMIT = 10;

const HashtagPicker = ({
  value: propValue = [],
  onChange,
  className,
  onFocus,
  isFocus,
  tooltip,
  text,
  isEditMode,
  toolTipProps,
}: HashTagPickerProps) => {
  const { isMoreThanTablet } = useMedia();
  const { t } = useTranslation();
  const [isOpen, toggleOpen] = useState(false);
  const value = propValue.map((str) => str.slice(0, maxLength));

  const { isLoading, data = [] } = useReactQuery({
    action: {
      apiFunc: getHashtagsByText,
      key: [QueryKeys.getHashtagsByText, text],
      params: {
        text,
      },
    },
    config: {
      enabled: !!text && !isEditMode,
      onSuccess: (res) => {
        if (Array.isArray(res)) {
          const hashtags = uniq([...res, ...data]);
          onChange(hashtags);
        }
      },
    },
  });

  const onFocusHandler = (index: number | boolean) => onFocus?.(index);

  const onClose = () => {
    toggleOpen(false);
  };

  const onSuccess = ({ hashtag }: any) => {
    const { label } = hashtag;
    onChange([...value, label]);
    toggleOpen(false);
  };

  const deleteHashtagInput = (id: number, item: string = '') => {
    const newValue = (value || []).filter(
      (val: any, index: number) => val !== item && id !== index
    );

    onChange(newValue);
  };

  const addHashtag = () => {
    if (isMoreThanTablet) {
      onChange([...(value || []), '']);
      onFocusHandler(value?.length || 0);
    } else {
      toggleOpen(true);
    }
  };

  const handleChange = (id: number, item: string) => {
    const newValue = value?.map((val: string, index: number) => {
      if (index === id) val = item;

      return val;
    });

    onChange(newValue);
  };

  const handleBlur = (id: number) => deleteHashtagInput(id);

  return (
    <Flex className={className}>
      <Flex flexDir="row">
        <Typography color="colorIconForth2" font="500" size={14} height={16}>
          {t('hashtags')}
        </Typography>
        <Tooltip
          triggerWrapperClassName={classes.toolTip}
          placement="top"
          trigger={
            <Icon
              {...toolTipProps}
              name="info-circle"
              type="far"
              size={16}
              color={toolTipProps?.color ?? 'fifthText'}
            />
          }
        >
          <Typography
            className={classes.toolTipContent}
            size={14}
            font="400"
            height={18}
            color="tooltipText"
            textAlign="center"
          >
            {tooltip}
          </Typography>
        </Tooltip>
      </Flex>
      <Flex className={cnj(classes.hashtagsContainer, 'responsive-margin-top')}>
        {isLoading ? (
          <Skeleton className={classes.skeleton} />
        ) : (
          value?.map((item: string, index: number) => (
            <HashtagInput
              key={index}
              value={item}
              onChange={handleChange}
              id={index}
              isFocus={isFocus === index}
              onFocus={(e: any) => onFocusHandler(e ? index : false)}
              onBlur={handleBlur}
              readOnly={!isMoreThanTablet}
              rightIcon={
                <Icon
                  name="times"
                  size={13}
                  onClick={() => deleteHashtagInput(index, item)}
                  color="fifthText"
                  className={classes.removeIcon}
                />
              }
              onKeyDown={addHashtag}
            />
          ))
        )}
        <IconButton
          type="fas"
          size="md15"
          name="plus"
          colorSchema="backgroundIconSecondary"
          onClick={addHashtag}
          className={classes.addAnotherBtn}
          disabled={value?.includes('') || value?.length === MAXIMUM_LIMIT}
        />
      </Flex>
      {value?.length === MAXIMUM_LIMIT && !value?.includes('') && (
        <AlertMessage
          title={t('reach_max_error')}
          type="error"
          closeAble={false}
          className={classes.alert}
        />
      )}
      {isOpen && <HashtagModal onClose={onClose} onSuccess={onSuccess} />}
    </Flex>
  );
};

export default HashtagPicker;
