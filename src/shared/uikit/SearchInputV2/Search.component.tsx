import React, { useRef } from 'react';
import isFunction from 'lodash/isFunction';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type { FocusEvent, HtmlHTMLAttributes } from 'react';
import debounce from 'lodash/debounce';
import IconButton from '@shared/uikit/Button/IconButton';
import classes from './Search.component.module.scss';
import cnj from '../utils/cnj';
import type { IconProps } from '../Icon/types';
import Flex from '../Flex';

export interface SearchInputV2Props {
  className?: any;
  inputStyle?: any;
  placeholder?: string;
  inputId?: string;
  onChange: (val: string) => void;
  value?: string;
  onClearSearch?: () => void;
  onFocus?: (e: FocusEvent<HTMLInputElement>) => void;
  variant?: 'circle' | 'square';
  size?: 'large' | 'small';
  inputProps?: any;
  trashIconId?: string;
  inputRootClassName?: string;
  trashIconProps?: IconProps;
  searchIconProps?: IconProps;
  debounceTime?: number;
  ref?: React.Ref<HTMLImageElement>;
  additionalRightIcon?: React.ReactElement;
}

const SearchInputV2 = ({
  className,
  inputStyle,
  onChange,
  value,
  onClearSearch,
  onFocus,
  variant = 'circle',
  placeholder,
  inputId,
  size = 'large',
  inputProps,
  trashIconId,
  inputRootClassName,
  trashIconProps = {},
  searchIconProps = {},
  debounceTime,
  ref,
  additionalRightIcon,
}: SearchInputV2Props) => {
  const { t } = useTranslation();
  const wrapperRef = useRef<HtmlHTMLAttributes<HTMLDivElement>>();
  const inputRef = useRef<HTMLInputElement>();

  const handleFocus = (e: any) => {
    if (isFunction(onFocus)) {
      onFocus(e);
    }
  };
  const handleChanged = (e: any) => {
    const val = e.target.value;
    onChange(val);
  };

  const debouncedHandleChange = debounceTime
    ? debounce(handleChanged, debounceTime)
    : handleChanged;

  const clear = () => {
    onClearSearch?.();
    if (onChange) {
      onChange('');
    }
  };

  const focus = (e: React.MouseEvent<HTMLInputElement, MouseEvent>) =>
    e?.currentTarget?.focus?.();

  return (
    <Flex ref={wrapperRef} className={cnj(classes.wrapper, className)}>
      <Flex
        className={cnj(
          classes.inputRoot,
          size === 'large' && classes.inputRootLarge,
          inputRootClassName
        )}
      >
        <input
          {...inputProps}
          id={inputId}
          ref={inputRef}
          value={value}
          placeholder={placeholder || t('search')}
          className={cnj(
            classes.input,
            variant === 'square' && classes.inputSquare,
            !!value && classes.inputActive,
            inputStyle
          )}
          onChange={debouncedHandleChange}
          onFocus={handleFocus}
          onClick={focus}
        />
        <Flex
          className={cnj(
            classes.rightIconsWrapper,
            size === 'large' && classes.searchIconLarge
          )}
        >
          {value ? (
            <Flex
              ref={ref}
              id={trashIconId}
              onClick={clear}
              onKeyDown={clear}
              role="button"
              tabIndex="-1"
            >
              <IconButton
                name="trash"
                type="fal"
                color="primaryText"
                size="md18"
                noHover
                {...trashIconProps}
                className={cnj(classes.clean, trashIconProps?.className)}
              />
            </Flex>
          ) : (
            <Flex>
              <IconButton
                name="search"
                type="fal"
                iconProps={{ color: 'inputPlaceholder' }}
                size="md18"
                noHover
                {...searchIconProps}
                className={cnj(classes.searchIcon, searchIconProps?.className)}
              />
            </Flex>
          )}
          {additionalRightIcon}
        </Flex>
      </Flex>
    </Flex>
  );
};

export default SearchInputV2;
