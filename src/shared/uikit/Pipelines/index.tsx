import type { FC, PropsWithChildren } from 'react';
import React, { useState } from 'react';
import type { PipelineProps } from 'shared/types/pipelineTypes';
import type {
  DraggableProvided,
  OnDragEndResponder,
  OnDragUpdateResponder,
} from '@hello-pangea/dnd';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import useTranslation from 'shared/utils/hooks/useTranslation';
import debounce from 'lodash/debounce';
import PipelineItem from './partials/PipelineItem';
import Flex from '../Flex';
import classes from './index.module.scss';
import type { BaseButtonProps } from '../Button/BaseButton';
import PipelineAddNewButton from './partials/PipelineAddNewButton';
import useOpenConfirm from '../Confirmation/useOpenConfirm';
import cnj from '../utils/cnj';

export interface PipelineFeatureProps {
  canDrag?: boolean;
  canEdit?: boolean;
  canDelete?: boolean;
  canChangeSwitch?: boolean;
  canChangeColor?: boolean;
  hideSwitch?: boolean;
}

export type PipelineItemProps = PipelineProps & PipelineFeatureProps;

export interface PipelinesProps {
  value: PipelineItemProps[];
  onChange: (value: PipelineItemProps[]) => void;
  inModal?: boolean;
  name: string;
  addButtonProps?: BaseButtonProps;
}

const Pipelines: FC<PipelinesProps> = (props) => {
  const { value = [], onChange, inModal = false, name, addButtonProps } = props;
  const { t } = useTranslation();

  const onDargEnd = (list: PipelineItemProps[]) => onChange(list);

  if (!value.length) return null;

  return (
    <Flex className={classes.root}>
      <List
        data={value}
        onDragEnd={(list) => onDargEnd(list)}
        contextId={name}
        addButtonProps={addButtonProps}
        inModal={inModal}
      />
    </Flex>
  );
};

interface ListProps {
  data: PipelineItemProps[];
  onDragEnd: (list: PipelineItemProps[]) => void;
  addButtonProps?: BaseButtonProps;
  contextId: string;
  inModal?: boolean;
  isAddButtonOnTop?: boolean;
}

const List: FC<ListProps> = ({
  onDragEnd,
  data,
  addButtonProps,
  contextId,
  inModal,
  isAddButtonOnTop = false,
}) => {
  const [list, setList] = useState(data);
  const [selectedItem, setSelectedItem] = useState<{
    item: PipelineItemProps;
    index: number;
  }>();
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });
  const { t } = useTranslation();
  const [invalidDropId, setInvalidDropID] = useState<number>();
  const handleChange = (_list: PipelineItemProps[]) => {
    setList(_list);
    handleDebounce(_list);
  };

  const handleOnDragEnd: OnDragEndResponder = (result) => {
    setInvalidDropID(undefined);
    if (!result.destination || !list[result.destination.index]?.canDrag) return;
    const items = Array.from(list);
    items[result.destination.index].order = result.source.index;
    items[result.source.index].order = result.destination.index;
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);
    handleChange(items);
  };

  const handleDebounce = React.useCallback(
    debounce((_list) => onDragEnd(_list), 500),
    []
  );

  const onAddToContext = (newItem: PipelineItemProps) => {
    const btnIndex = list.findIndex((_item) => !_item.title);
    const newList = Array.from(list);
    newList.splice(btnIndex, 0, {
      ...newItem,
      order: list.length,
    });
    handleChange(newList);
  };

  const onEditItem = (item: PipelineItemProps, index: number) => {
    const items = Array.from(list);
    items[index] = item;
    handleChange(items);
  };

  const onDeleteItem = (index: number) => {
    const items = list
      .filter((_, i) => i !== index)
      .map((item, i) => ({ ...item, order: i }));
    handleChange(items);
  };

  const onShowDeleteConfirm = (index: number) => {
    openConfirmDialog({
      title: t('delete_stage'),
      message: t('delete_stage_desc'),
      confirmButtonText: t('delete'),
      cancelButtonText: t('confirm_cancel'),
      confirmCallback: () => onDeleteItem(index),
    });
  };

  const onSwitchChange = (
    checked: boolean,
    item: PipelineItemProps,
    index: number
  ) => {
    onEditItem(
      {
        ...item,
        applicantTrack: checked,
      },
      index
    );
  };

  const onDragUpdate: OnDragUpdateResponder<string> = (update) => {
    const notAllowDrop = list.some(
      (item, i) =>
        i === update.destination?.index && !item.canDrag && item.title
    );
    if (notAllowDrop) {
      setInvalidDropID(update.source.index);
    } else setInvalidDropID(undefined);
  };

  return (
    <>
      {!!addButtonProps && isAddButtonOnTop && (
        <PipelineAddNewButton
          onAddNew={onAddToContext}
          addButtonProps={addButtonProps}
          classNames={{ addButtonRoot: '!mt-0', addInputRoot: '!mt-0' }}
        />
      )}
      <DragDropContext onDragEnd={handleOnDragEnd} onDragUpdate={onDragUpdate}>
        <Droppable droppableId={`droppable_${contextId}`}>
          {(provided) => (
            <ul
              className={classes.contextRoot}
              {...provided.droppableProps}
              ref={provided.innerRef}
            >
              {list.map((item, index) => (
                <Draggable
                  key={`item_${index}_context_${contextId}`}
                  draggableId={`item_${index}_context_${contextId}`}
                  index={index}
                  isDragDisabled={!item.canDrag || !item.title}
                  shouldRespectForcePress={false}
                  disableInteractiveElementBlocking
                >
                  {(draggableProvided, snapshot) => (
                    <Wrapper
                      draggableProvided={draggableProvided}
                      canDrag={item.canDrag}
                      inModal={inModal}
                    >
                      {item.title ? (
                        <PipelineItem
                          item={item}
                          moreOptions={{
                            onEditClick: () => setSelectedItem({ item, index }),
                            onDeleteClick: () => onShowDeleteConfirm(index),
                          }}
                          selectedItem={
                            selectedItem?.index === index
                              ? selectedItem.item
                              : undefined
                          }
                          onEditItem={(_item) => onEditItem(_item, index)}
                          onRemoveSelectedItem={() =>
                            setSelectedItem(undefined)
                          }
                          switchProps={{
                            onChange: (checked: boolean) =>
                              onSwitchChange(checked, item, index),
                            value: item.applicantTrack ?? false,
                          }}
                          classNames={{
                            root: cnj(
                              invalidDropId === index ? '!border-error' : '',
                              snapshot.isDragging
                                ? 'rotate-[3deg] border-success'
                                : ''
                            ),
                          }}
                        />
                      ) : (
                        <PipelineAddNewButton
                          onAddNew={onAddToContext}
                          addButtonProps={addButtonProps}
                          classNames={{
                            addButtonRoot: '!mb-12',
                            addInputRoot: '!mb-12',
                          }}
                        />
                      )}
                    </Wrapper>
                  )}
                </Draggable>
              ))}
              {provided.placeholder}
            </ul>
          )}
        </Droppable>
      </DragDropContext>
    </>
  );
};

export default Pipelines;

const Wrapper = (
  props: {
    draggableProvided: DraggableProvided;
    canDrag?: boolean;
    inModal?: boolean;
  } & PropsWithChildren
) => {
  const { draggableProvided, children, canDrag } = props;

  if (!canDrag)
    return (
      <li
        ref={draggableProvided.innerRef}
        {...draggableProvided.draggableProps}
      >
        {children}
      </li>
    );

  return (
    <li
      ref={draggableProvided.innerRef}
      {...draggableProvided.draggableProps}
      style={draggableProvided.draggableProps.style}
      {...draggableProvided.dragHandleProps}
    >
      {children}
    </li>
  );
};
