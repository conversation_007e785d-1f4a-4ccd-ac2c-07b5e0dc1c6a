import React from 'react';
import IcomoonReact from 'icomoon-react';
import Flex from '../Flex';
import { makeTheme } from '../helpers/theme';
import iconSet from './selection.json';
import useTheme from '../utils/useTheme';
import type { IconProps } from './types';

const Icon = ({
  type = 'fas',
  name,
  size = 20,
  color = 'primaryText',
  className,
  onClick,
  style,
  ref,
}: IconProps) => {
  const icon = type === 'fas' ? `${name}-s` : name;
  const { isDark } = useTheme();

  const themObj = makeTheme(isDark);
  const iconColor = themObj?.colors?.[color] as string;

  return (
    <Flex as="span" className={className} onClick={onClick} ref={ref}>
      <IcomoonReact
        style={style}
        iconSet={iconSet}
        color={iconColor}
        size={size}
        icon={icon as string}
      />
    </Flex>
  );
};

Icon.displayName = 'Icon';

export default Icon;
