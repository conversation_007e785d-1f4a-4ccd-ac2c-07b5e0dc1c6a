import type { colorsKeys } from '../helpers/theme';
import type iconSetTS from './selection';

export type IconFontType = 'fas' | 'far' | 'fal';
export type IconName =
  (typeof iconSetTS)['icons'][number]['properties']['name'];

export interface IconProps {
  type?: IconFontType;
  name?: IconName;
  size?: number;
  color?: colorsKeys;
  className?: string;
  onClick?: any;
  style?: React.CSSProperties;
  ref?: React.Ref<any>;
}
