import type { ArgTypes, Meta, StoryObj } from '@storybook/react';
import type { IconProps } from './types';
import Icon from '.';
import iconsNames from './selection';
import { makeTheme } from '../helpers/theme';
import Flex from '../Flex';
import Typography from '../Typography';
import Divider from '../Divider';
import Tooltip from '../Tooltip';
import copyTextToClipboard from '../utils/copyToClipboard';

const meta: Meta<IconProps> = {
  title: 'Atoms/SVGs/Icon',
  component: Icon,
} satisfies Meta<IconProps>;

export default meta;
type Story = StoryObj<typeof meta>;

const icons = iconsNames.icons.map((icon) => icon.properties.name);
const iconsData = groupIconsByAlphabet(icons);
const colors = Object.keys(makeTheme(false).colors);

const defaultProps: IconProps = {
  name: 'Apple',
  color: 'primaryText',
  size: 40,
  type: 'far',
};

const defaultArgTypes: Partial<ArgTypes<IconProps>> = {
  name: {
    control: {
      type: 'select',
    },
    options: icons,
  },
  color: {
    control: {
      type: 'select',
    },
    options: colors,
  },
  type: {
    control: false,
  },
};

export const Default: Story = {
  args: defaultProps,
  argTypes: defaultArgTypes,
  parameters: {
    controls: {
      expanded: true,
    },
  },
};

export const All: Story = {
  render: () => (
    <Flex className="overflow-auto gap-24">
      {iconsData.map((iconData) => (
        <Flex key={`icon_key_${iconData.key}`} className="gap-12">
          <Typography>{iconData.key.toUpperCase()}</Typography>
          <Divider />
          <Flex className="!flex-row gap-8 flex-wrap items-center">
            {iconData.icons.map((icon) => (
              <Tooltip
                key={`icon_${icon}`}
                placement="top"
                trigger={
                  <Icon
                    name={icon as any}
                    size={32}
                    color="primaryText"
                    type="far"
                    onClick={() => copyTextToClipboard(icon)}
                  />
                }
              >
                {icon}
              </Tooltip>
            ))}
          </Flex>
        </Flex>
      ))}
    </Flex>
  ),
};

function groupIconsByAlphabet(iconsList: string[]) {
  const sortedIcons = iconsList.sort((a, b) => a.localeCompare(b));
  const groupedIcons = sortedIcons.reduce(
    (acc, icon) => {
      const firstLetter = icon[0].toLowerCase();
      let group = acc.find((item) => item.key === firstLetter);

      if (!group) {
        group = { key: firstLetter, icons: [] };
        acc.push(group);
      }
      group.icons.push(icon);
      return acc;
    },
    [] as { key: string; icons: string[] }[]
  );
  return groupedIcons;
}
