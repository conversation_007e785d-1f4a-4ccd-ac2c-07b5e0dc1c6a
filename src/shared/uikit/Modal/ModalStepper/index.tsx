import CircularProgressWithIcon from '@shared/components/Organism/MultiStepForm/CircularProgressWithIcon';
import Flex from '@shared/uikit/Flex';
import type Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import type { ComponentProps, FC } from 'react';
import classes from './index.module.scss';

interface ModalStepperProps {
  title: string;
  progressProps?: {
    percentage: number;
    iconProps: ComponentProps<typeof Icon>;
    pathColor?: string;
  };
  actions?: React.ReactNode;
}

const ModalStepper: FC<ModalStepperProps> = (props) => {
  const { title, progressProps, actions } = props;
  return (
    <Flex className={classes.root}>
      <Typography size={16} height={18} font="700" color="primaryText">
        {title}
      </Typography>
      <Flex className={classes.rightSection}>
        {actions}
        {!!progressProps && (
          <CircularProgressWithIcon
            iconProps={progressProps.iconProps}
            percentage={progressProps.percentage}
            pathColor={progressProps?.pathColor}
          />
        )}
      </Flex>
    </Flex>
  );
};

export default ModalStepper;
