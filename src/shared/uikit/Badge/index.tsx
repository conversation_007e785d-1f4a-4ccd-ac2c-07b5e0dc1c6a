import React, { type FC } from 'react';
import cnj from '../utils/cnj';
import type { TextProps } from '../Typography';
import Icon from '../Icon';
import type { IconProps } from '../Icon/types';
import Typography from '../Typography';
import Flex from '../Flex';
import type { colorsKeys } from '../helpers/theme';
import classes from './index.module.scss';

interface BadgeProps {
  text?: string | number;
  background?: colorsKeys;
  borderColor?: colorsKeys;
  disabled?: boolean;
  disabledColor?: colorsKeys;
  hoverColor?: colorsKeys;
  className?: string;
  textProps?: TextProps;
  startIconProps?: IconProps;
  endIconProps?: IconProps;
  startComponent?: ReactNode;
  endComponent?: ReactNode;
}
const Badge: FC<BadgeProps> = ({
  text,
  background = 'error',
  borderColor = 'background',
  disabled,
  disabledColor = 'disabledError',
  hoverColor,
  className,
  textProps,
  startIconProps,
  endIconProps,
  startComponent,
  endComponent,
}) => (
  <Flex
    as="span"
    className={cnj(
      classes.badgeRoot,
      classes[`backgroundColor-${background}`],
      classes[`borderColor-${borderColor}`],
      className,
      disabled && classes.disabledStyle,
      disabled && classes[`backgroundColor-${disabledColor}`]
    )}
  >
    {startIconProps ? (
      <Icon {...startIconProps} />
    ) : startComponent ? (
      startComponent
    ) : null}
    <Typography
      textAlign="center"
      font="700"
      color="white"
      size={12}
      height={14}
      {...textProps}
    >
      {text}
    </Typography>
    {endIconProps ? (
      <Icon {...endIconProps} />
    ) : endComponent ? (
      endComponent
    ) : null}
  </Flex>
);

export default Badge;
