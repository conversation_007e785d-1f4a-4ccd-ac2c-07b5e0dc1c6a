import React from 'react';
import type { FlexProps } from 'shared/uikit/Flex';
import Flex from 'shared/uikit/Flex';
import cnj from '../utils/cnj';
import type { IconFontType, IconName, IconProps } from '../Icon/types';
import Icon from '../Icon';
import type { colorsKeys } from '../helpers/theme';
import Badge from '../Badge';
import type { LinkProps } from '../Link';
import Link from '../Link';
import classes from './IconButton.module.scss';

const sizeMapperTable = {
  stiny: { iconSize: 12, wrapperSize: 12 },
  tiny: { iconSize: 13, wrapperSize: 16 },
  sm11: { iconSize: 11, wrapperSize: 24 },
  sm: { iconSize: 15, wrapperSize: 24 },
  sm16: { iconSize: 16, wrapperSize: 24 },
  sm18: { iconSize: 18, wrapperSize: 24 },
  sm20: { iconSize: 20, wrapperSize: 24 },
  sm22: { iconSize: 22, wrapperSize: 24 },
  md: { iconSize: 20, wrapperSize: 32 },
  md15: { iconSize: 15, wrapperSize: 32 },
  md18: { iconSize: 18, wrapperSize: 32 },
  md20: { iconSize: 20, wrapperSize: 32 },
  md24: { iconSize: 24, wrapperSize: 32 },
  lg: { iconSize: 20, wrapperSize: 36 },
  mlg: { iconSize: 20, wrapperSize: 38 },
  xl40: { iconSize: 22, wrapperSize: 40 },
  xl: { iconSize: 24, wrapperSize: 48 },
};

const variantBorderRadius = {
  circle: 100,
  rectangle: 4,
};

const colorSchemaMapperTable = {
  primary: {
    background: 'background',
    hover: 'hoverPrimary',
    iconColor: 'primaryText',
    focus: 'smoke2',
  },
  disabledGray: {
    background: 'background',
    hover: 'hoverPrimary',
    iconColor: 'disabledGrayDark_disabledGray',
    focus: 'smoke2',
  },
  'primary-light': {
    background: 'lightGray',
    hover: 'hover_2',
    iconColor: 'graphene',
  },
  'primary-blue': {
    background: 'brand',
    hover: 'trench',
    iconColor: 'white',
  },
  secondary: {
    background: 'background',
    hover: 'hoverPrimary',
    iconColor: 'thirdText',
    focus: 'smoke2',
  },
  secondary2: {
    background: 'transparent',
    hover: 'hoverPrimary',
    iconColor: 'secondaryText',
    focus: 'smoke2',
  },
  'secondary-transparent': {
    background: 'transparent',
    hover: 'hoverPrimary',
    iconColor: 'primaryText',
    focus: 'smoke2',
  },
  tertiary: {
    background: 'background',
    hover: 'hoverPrimary',
    iconColor: 'brand',
    focus: 'smoke2',
  },
  'tertiary-transparent': {
    background: 'transparent',
    hover: 'hoverPrimary',
    iconColor: 'brand',
    focus: 'smoke2',
  },

  transparent: {
    background: 'transparent',
    hover: 'hoverPrimary',
    iconColor: 'primaryText',
    focus: 'smoke2',
  },
  transparent1: {
    background: 'transparent',
    hover: 'hover75_techGray_20',
    iconColor: 'primaryText',
    focus: 'darkSecondary_hoverGray15',
  },
  transparent2: {
    background: 'transparent',
    hover: 'hoverPrimary',
    iconColor: 'disabledGrayDark_gray',
    focus: 'smoke2',
  },
  transparentSmokeCoal: {
    background: 'transparent',
    hover: 'hoverPrimary',
    iconColor: 'smoke_coal',
    focus: 'smoke2',
  },
  'transparent-white': {
    background: 'transparent',
    hover: 'transparent',
    iconColor: 'white',
    focus: 'transparent',
  },
  dark: {
    background: 'popOverBg',
    hover: 'hover_75',
    iconColor: 'disabledGray',
    focus: 'muteMidGray',
  },
  error: {
    background: 'lightError',
    hover: 'borderError',
    iconColor: 'error',
    focus: 'borderError',
  },
  transParentError: {
    background: 'transparent',
    hover: 'borderError',
    iconColor: 'error',
    focus: 'borderError',
  },
  warning: {
    background: 'transparent',
    hover: 'warning_5',
    iconColor: 'warning',
    focus: 'warning_5',
  },
  success: {
    background: 'transparent',
    hover: 'success_5',
    iconColor: 'success',
    focus: 'warning_5',
  },
  tag: {
    background: 'transparent',
    hover: 'tagHover',
    iconColor: 'colorIconSeventh',
    focus: 'tagHover',
  },
  modalHeaderIcon: {
    background: 'transparent',
    hover: 'hoverThird',
    iconColor: 'thirdText',
    focus: 'colorIconThird',
  },
  gray: {
    background: 'background',
    hover: 'hoverPrimary',
    iconColor: 'primaryDisabledText',
    focus: 'smoke2',
  },
  graySecondary: {
    background: 'darkSecondary_hover',
    hover: 'hoverPrimary',
    iconColor: 'primaryText',
    focus: 'smoke2',
  },
  'semi-transparent': {
    background: 'brand_10',
    hover: 'brand_20',
    iconColor: 'brand',
    focus: 'smoke2',
  },
  'orange-semi-transparent': {
    background: 'pendingOrange_10',
    hover: 'pendingOrange_50',
    iconColor: 'pendingOrange',
    focus: 'pendingOrange',
  },
  backgroundIconSecondary: {
    background: 'darkSecondary_hover',
    hover: 'hoverThird',
    iconColor: 'primaryText',
    focus: 'smoke2',
  },
  bottomBarIcon: {
    background: 'background',
    hover: 'hoverPrimary',
    iconColor: 'ninthText',
    focus: 'smoke2',
  },
  smoke_coal: {
    background: 'darkSecondary_hover',
    hover: 'hoverThird',
    iconColor: 'smoke_coal',
    focus: 'smoke2',
  },
  white: {
    background: 'darkSecondary_hover',
    hover: 'hoverThird',
    iconColor: 'white',
    focus: 'smoke2',
  },
  darkLight: {
    background: 'popOverBg_white',
    hover: 'popOverBg_white',
    iconColor: 'smoke_coal',
    focus: 'popOverBg_white',
  },
  transparentNoHover: {
    background: 'transparent',
    hover: 'transparent',
    iconColor: 'primaryText',
    focus: 'smoke2',
  },
  transparentMoreHover: {
    background: 'transparent',
    hover: 'darkSecondary_hover',
    iconColor: 'primaryText',
    focus: 'smoke2',
  },
  secondaryDisabledText: {
    background: 'transparent',
    hover: 'darkSecondary_hover',
    iconColor: 'secondaryDisabledText',
    focus: 'brand',
  },
  'ghost-success': {
    background: 'ghost',
    hover: 'success_10',
    iconColor: 'success',
    focus: 'success_10',
  },
};

const defaultSchema = 'primary';

type SchemaItem = {
  background: colorsKeys;
  hover: colorsKeys;
  color: colorsKeys;
  iconColor: colorsKeys;
  focus: colorsKeys;
};
export type IconButtonColorSchema = keyof typeof colorSchemaMapperTable;

export type IconButtonSize = keyof typeof sizeMapperTable;

export interface IconButtonProps {
  onClick?: (event: React.MouseEvent<HTMLElement>) => any;
  htmlId?: string;
  to?: LinkProps['to'];
  href?: string;
  name: IconName;
  size?: IconButtonSize;
  colorSchema?: IconButtonColorSchema;
  type?: IconFontType;
  className?: string;
  iconClassName?: string;
  disabled?: boolean;
  variant?: 'circle' | 'rectangle';
  badgeNumber?: number | string;
  tooltip?: string;
  tPlacement?: string;
  icon?: React.ReactNode;
  iconProps?: IconProps;
  noHover?: boolean;
  target?: string;
  badgeClassName?: string;
  ref?: React.Ref<HTMLDivElement>;
}

const IconButton = ({
  onClick,
  htmlId,
  to,
  href,
  name,
  size = 'md',
  colorSchema = defaultSchema,
  type,
  className,
  iconClassName,
  disabled,
  variant = 'circle',
  badgeNumber,
  tooltip,
  tPlacement,
  iconProps,
  noHover,
  target,
  badgeClassName,
  ref,
  ...rest
}: IconButtonProps & FlexProps) => {
  const { iconColor, background, hover, focus } =
    (colorSchemaMapperTable[colorSchema] as SchemaItem) || {};
  const { iconSize, wrapperSize } = sizeMapperTable[size] || {};

  const handeClick: React.MouseEventHandler<HTMLDivElement> = (e) => {
    if (disabled) return;
    onClick?.(e);
  };

  const Wrapper = to ? Link : Flex;
  const wrapperProps = to
    ? { to }
    : href
      ? { href, as: 'a', target }
      : { onClick: handeClick };

  return (
    <Wrapper
      data-tip={tooltip}
      data-place={tPlacement}
      ref={ref}
      {...wrapperProps}
      {...rest}
      className={cnj(
        classes.iconButtonWrap,
        classes[`backgroundColor-${background}`],
        classes[`backgroundColorHover-${hover}`],
        classes[`backgroundColorFocus-${focus}`],
        variant === 'rectangle' && classes.rectangle,
        (noHover || disabled) && classes[`noHover-${background}`],
        'select-none',
        className
      )}
      style={{
        width: wrapperSize,
        height: wrapperSize,
        minWidth: wrapperSize,
        minHeight: wrapperSize,
      }}
      id={htmlId}
    >
      <Icon
        name={name}
        size={iconSize}
        type={type}
        color={iconProps?.color ?? iconColor}
        className={cnj(disabled && classes.disabledStyle, iconClassName)}
        {...(iconProps || {})}
      />
      {!!badgeNumber && (
        <Badge
          className={cnj(classes.badge, badgeClassName)}
          disabled={disabled}
          text={badgeNumber}
        />
      )}
    </Wrapper>
  );
};

export default IconButton;
