import React from 'react';
import preventClickHandler from 'shared/utils/toolkit/preventClickHandler';
import type { FontType, TypographyProps } from '../Typography';
import Typography from '../Typography';
import type { IconFontType, IconName } from '../Icon/types';
import Icon from '../Icon';
import Link from '../Link';
import cnj from '../utils/cnj';
import type { colorsKeys } from '../helpers/theme';
import Spinner from '../Spinner';
import classes from './Button.component.module.scss';
import BaseButton from './BaseButton';

export const schemaProperties = {
  'primary-light': {
    backgroundColor: 'lightGray',
    hoverBackgroundColor: 'hover_2',
    color: 'graphene',
  },
  'secondary-light': {
    backgroundColor: 'transparent',
    hoverBackgroundColor: 'hover_2',
    color: 'graphene',
  },
  'primary-blue': {
    backgroundColor: 'brand',
    hoverBackgroundColor: 'trench',
    color: 'white',
  },
  'semi-transparent': {
    backgroundColor: 'brand_10',
    hoverBackgroundColor: 'brand_20',
    color: 'brand',
  },
  'semi-transparent2': {
    backgroundColor: 'brand_20',
    hoverBackgroundColor: 'brand_30',
    color: 'brand',
  },
  ghost: {
    backgroundColor: 'transparent',
    hoverBackgroundColor: 'techGray_20',
    color: 'primaryText',
  },
  'ghost-black': {
    backgroundColor: 'transparent',
    hoverBackgroundColor: 'hover_75',
    color: 'disabledGray',
  },
  'light-gray': {
    backgroundColor: 'primaryText',
    hoverBackgroundColor: 'hover',
    color: 'darkSecondary_hover',
  },
  'secondary-dark': {
    backgroundColor: 'darkSecondary',
    hoverBackgroundColor: 'muteMidGray',
    color: 'disabledGray',
  },
  'dark-primary': {
    backgroundColor: 'disabledGray',
    hoverBackgroundColor: 'hover_2',
    color: 'popOverBg',
  },
  black: {
    backgroundColor: 'popOverBg',
    hoverBackgroundColor: 'hover_75',
    color: 'disabledGray',
  },
  'dark-gray': {
    backgroundColor: 'darkSecondary',
    hoverBackgroundColor: 'hover_75',
    color: 'disabledGray',
  },
  transparent: {
    backgroundColor: 'transparent',
    hoverBackgroundColor: 'transparent',
    color: 'disabledGray',
  },
  'transparent-brand': {
    backgroundColor: 'transparent',
    hoverBackgroundColor: 'transparent',
    color: 'brand',
  },
  'transparent-mute': {
    backgroundColor: 'transparent',
    hoverBackgroundColor: 'transparent',
    color: 'primaryDisabledText',
  },
  'ghost-brand': {
    backgroundColor: 'ghost',
    hoverBackgroundColor: 'brand_10',
    color: 'brand',
  },
  'ghost-success': {
    backgroundColor: 'ghost',
    hoverBackgroundColor: 'success_10',
    color: 'success',
  },
  orange: {
    backgroundColor: 'pendingOrange',
    hoverBackgroundColor: 'pendingOrange',
    color: 'white',
  },
  'ghost-orange': {
    backgroundColor: 'ghost',
    hoverBackgroundColor: 'pendingOrange_10',
    color: 'pendingOrange',
  },
  'orange-semi-transparent': {
    backgroundColor: 'pendingOrange_10',
    hoverBackgroundColor: 'pendingOrange_50',
    color: 'pendingOrange',
  },
  'success-semi-transparent': {
    backgroundColor: 'success_10',
    hoverBackgroundColor: 'success_50',
    color: 'success',
  },
  success: {
    backgroundColor: 'success',
    hoverBackgroundColor: 'success',
    color: 'white',
  },
  'brown-semi-transparent': {
    backgroundColor: 'brown_4',
    hoverBackgroundColor: 'brown_4',
    color: 'brown',
  },
  error: {
    backgroundColor: 'error',
    hoverBackgroundColor: 'error',
    color: 'white',
  },
  'error-semi-transparent': {
    backgroundColor: 'error_10',
    hoverBackgroundColor: 'error_20',
    color: 'error',
  },
  'gray-semi-transparent': {
    backgroundColor: 'gray_10',
    hoverBackgroundColor: 'hoverPrimary',
    color: 'primaryText',
  },
  gray: {
    backgroundColor: 'backgroundIconSecondary',
    hoverBackgroundColor: 'hoverPrimary',
    color: 'primaryText',
  },
  background: {
    backgroundColor: 'background',
    hoverBackgroundColor: 'hoverPrimary',
    color: 'smoke_coal',
  },
  'semi-transparent3': {
    backgroundColor: 'darkSecondary_hover', // darkSeconday / hover: #F1F4F9
    hoverBackgroundColor: 'hoverPrimary',
    color: 'smoke_coal',
  },
};

export type ButtonSchemaType = keyof typeof schemaProperties;
type SchemaItem = {
  backgroundColor: colorsKeys;
  hoverBackgroundColor: colorsKeys;
  color: colorsKeys;
};
const variantHeight = {
  default: 32,
  thin: 24,
  large: 42,
  xLarge: 42,
} as const;
export interface ButtonProps {
  onClick?: (event?: React.MouseEvent<HTMLElement>) => any;
  className?: string;
  href?: string;
  to?: string;
  leftSvg?: React.ReactNode;
  leftIcon?: IconName;
  leftType?: IconFontType;
  leftSize?: number;
  leftColor?: colorsKeys;
  leftIconClassName?: string;
  rightSvg?: React.ReactNode;
  rightIcon?: IconName;
  rightType?: IconFontType;
  rightSize?: number;
  rightColor?: colorsKeys;
  rightIconClassName?: string;
  label?: React.ReactNode;
  labelSize?: number;
  labelFont?: FontType;
  labelColor?: colorsKeys;
  labelClassName?: string;
  labelProps?: Partial<TypographyProps>;
  children?: React.ReactNode;
  schema?: ButtonSchemaType;
  variant?: 'default' | 'large' | 'thin' | 'text' | 'xLarge';
  shape?: 'rectangle' | 'capsule';
  disabled?: boolean;
  fullWidth?: boolean;
  tabIndex?: number;
  isLoading?: boolean;
  tooltip?: string;
  tPlacement?: string;
  target?: string;
  dataTestId?: string;
  ref?: React.Ref<HTMLImageElement>;
}

const Button = ({
  onClick,
  className,
  href,
  to,
  leftSvg,
  leftIcon,
  leftType,
  leftSize = 16,
  leftColor,
  leftIconClassName,
  rightSvg,
  rightIcon,
  rightType,
  rightSize = 16,
  rightColor,
  rightIconClassName,
  label,
  labelSize,
  labelFont = '700',
  labelColor,
  labelClassName,
  labelProps,
  children,
  schema = 'primary-blue',
  variant = 'default',
  shape = 'rectangle',
  disabled,
  fullWidth,
  tabIndex,
  isLoading,
  tooltip,
  tPlacement,
  target,
  dataTestId,
  ref,
}: ButtonProps) => {
  const buttonSchema = schemaProperties[schema] as SchemaItem;
  const notClickAble = isLoading || disabled;
  const handleClick = (e: any) => {
    if (notClickAble) {
      preventClickHandler(e);
    } else {
      onClick?.(e);
    }
  };
  const spinnerSize = (variantHeight[variant] || 42) / 2;
  const Wrapper = notClickAble ? 'span' : href ? 'a' : to ? Link : 'span';
  const wrapperProps = href ? { target, href } : to ? { to } : {};

  if (variant === 'text') {
    return (
      <BaseButton onClick={handleClick} disabled={notClickAble}>
        <Typography
          font={labelFont}
          color={labelColor || buttonSchema.color}
          className={cnj(classes.label, labelClassName)}
          size={labelSize}
          {...labelProps}
        >
          {label}
        </Typography>
      </BaseButton>
    );
  }

  return (
    <Wrapper
      data-tip={tooltip}
      data-place={tPlacement}
      ref={ref}
      {...wrapperProps}
      className={cnj(
        classes.buttonRoot,
        fullWidth && classes.fullWidth,
        classes[`backgroundColor-${buttonSchema.backgroundColor}`],
        variant === 'thin' && classes.heightThin,
        variant === 'large' && classes.heightLarge,
        variant === 'xLarge' && classes.heightXLarge,
        shape === 'rectangle' && classes.rectangle,
        classes[`backgroundColorHover-${buttonSchema.hoverBackgroundColor}`],
        className,
        disabled && classes.disabledStyle,
        disabled &&
          classes[`backgroundColorHover-${buttonSchema.backgroundColor}`]
      )}
      tabIndex={tabIndex}
      disabled={notClickAble}
      onClick={handleClick}
      data-test-id={dataTestId}
    >
      {isLoading ? (
        <Spinner size={spinnerSize} color={buttonSchema.color} />
      ) : (
        <>
          {leftIcon && (
            <Icon
              name={leftIcon}
              type={leftType}
              className={cnj(classes.leftIcon, leftIconClassName)}
              color={leftColor || buttonSchema.color}
              size={leftSize}
            />
          )}
          {leftSvg}
          {!!label && (
            <Typography
              font={labelFont}
              color={labelColor || buttonSchema.color}
              size={labelSize}
              {...labelProps}
              className={cnj(classes.label, labelClassName)}
            >
              {label}
            </Typography>
          )}
          {!!children && children}
          {rightSvg}
          {rightIcon && (
            <Icon
              type={rightType}
              name={rightIcon}
              className={cnj(classes.rightIcon, rightIconClassName)}
              color={rightColor || buttonSchema.color}
              size={rightSize}
            />
          )}
        </>
      )}
    </Wrapper>
  );
};

export default Button;
