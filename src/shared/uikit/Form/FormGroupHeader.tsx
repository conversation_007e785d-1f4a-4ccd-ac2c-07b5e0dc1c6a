import React from 'react';
import ParseTextStringCP from 'shared/components/molecules/TranslateReplacer';
import type { colorsKeys } from '../utils/makeStyle';
import cnj from '../utils/cnj';
import type { TypographyProps } from '../Typography';
import Typography from '../Typography';
import type { IconName } from '../Icon/types';
import Icon from '../Icon';
import Flex from '../Flex';
import classes from './FormGroupHeader.module.scss';
import Tooltip from '../Tooltip';

interface FormGroupHeaderProps {
  icon?: IconName;
  title?: string;
  className?: string;
  rightComponent?: React.ReactNode;
  color?: colorsKeys;
  formSection?: boolean;
  helper?: string | string[];
  classNames?: {
    title?: string;
  };
  titleProps?: TypographyProps;
}

const FormGroupHeader: React.FC<FormGroupHeaderProps> = ({
  title,
  icon,
  className,
  rightComponent,
  color = 'primaryText',
  formSection = false,
  helper,
  classNames,
  titleProps,
}) => (
  <Flex
    className={cnj(
      classes.formGroupRoot,
      formSection && classes.formSection,
      className
    )}
  >
    <Flex className={classes.leftSection}>
      {!!icon && (
        <Flex className={classes.iconWrap}>
          <Icon color={color} type="far" size={16} name={icon} />
        </Flex>
      )}
      <Typography
        color={color}
        font={titleProps?.font ?? 'bold'}
        height={titleProps?.height ?? 18}
        size={titleProps?.size ?? 16}
        className={cnj(formSection ? classes.title : '', classNames?.title)}
      >
        {title}
      </Typography>
      {helper && (
        <Tooltip
          trigger={
            <Icon
              name="info-circle"
              type="fal"
              size={14}
              color="secondaryDisabledText"
              className="ml-4"
            />
          }
          placement="top"
        >
          {typeof helper === 'string' ? (
            helper
          ) : (
            <Flex>
              {helper.map((hText) => (
                <ParseTextStringCP
                  key={`helper_${hText}`}
                  textProps={{ size: 13, height: 17, color: 'skeletonBg' }}
                  textString={hText}
                  tagComponentMap={{
                    // eslint-disable-next-line react/no-unstable-nested-components
                    0: (text) => (
                      <Typography
                        color="skeletonBg"
                        size={13}
                        height={17}
                        font="bold"
                      >
                        {text}
                      </Typography>
                    ),
                  }}
                />
              ))}
            </Flex>
          )}
        </Tooltip>
      )}
    </Flex>
    {rightComponent}
  </Flex>
);

export default FormGroupHeader;
