export const searchFilterQueryParams = {
  query: 'query',
  searchEntity: 'searchEntity',
  searchGroupType: 'searchGroupType',
  dateRangeType: 'dateRangeType',
  workPlaceTypes: 'workPlaceTypes',
  experienceLevels: 'experienceLevels',
  titles: 'titles',
  categoryIds: 'categoryIds',
  employmentTypes: 'employmentTypes',
  currentEntityId: 'currentEntityId',
  similarEntityId: 'similarEntityId',
  travelRequirements: 'travelRequirements',
  pageIds: 'pageIds',
  sortBy: 'sortBy',
  source: 'source',
  cities: 'cities',
  skills: 'skills',
  languages: 'languages',
  occupations: 'occupations',
  languageIds: 'languageIds',
  jobBenefitIds: 'jobBenefitIds',
  salaryPeriod: 'salaryPeriod',
  salaryCurrencyId: 'salaryCurrencyId',
  minSalary: 'minSalary',
  maxSalary: 'maxSalary',
  salaryRange: 'salaryRange',
  applyAtLobox: 'applyAtLobox',
  onlyApplicants: 'onlyApplicants',
  countryId: 'countryId',
  jobGroupStatus: 'jobGroupStatus',
  creatorIds: 'creatorIds',
  status: 'status',
  hostedBy: 'hostedBy',
  occupationId: 'occupationId',
  isLocationTitleFiltered: 'isLocationTitleFiltered',
  experiencesAtCompanyIds: 'experiencesAtCompanyIds',
  educatedAtSchoolIds: 'educatedAtSchoolIds',
  memberSince: 'memberSince',
  occupationNames: 'occupationNames',
  educationDegree: 'educationDegree',
  industryIds: 'industryIds',
  skillIds: 'skillIds',
  datePosted: 'datePosted',
  postType: 'type',
  postedByUserIds: 'postedByUserIds',
  categories: 'categories',
  companySizes: 'companySizes',
  scope: 'scope',
  page: 'page',
  hashtags: 'hashtags',
  postedBy: 'postedBy',
  establishmentDate: 'establishmentDate',
  relatedPageIds: 'relatedPageIds',
  mentionedUserIds: 'mentionedUserIds',
  priorities: 'priorities',
  projectIds: 'projectIds',
  jobIds: 'jobIds',
  collaboratorUserIds: 'collaboratorUserIds',
  pointOfContactUserIds: 'pointOfContactUserIds',
  creatorUserIds: 'creatorUserIds',
  responseTimes: 'responseTimes',
  workAuthorizationIds: 'workAuthorizationIds',
  tags: 'tags',
  ownerIds: 'ownerIds',
  vendorIds: 'vendorIds',
  clientIds: 'clientIds',
  locationTitle: 'locationTitle',
  cityCode: 'cityCode',
  stateCode: 'stateCode',
  countryCode: 'countryCode',
  refresh: 'refresh',
  preferredCities: 'preferredCities',
  schoolIds: 'schoolIds',
  majorIds: 'majorIds',
  currentCompanyIds: 'currentCompanyIds',
  previousCompanyIds: 'previousCompanyIds',
  referralUserIds: 'referralUserIds',
  referralCompanyIds: 'referralCompanyIds',
  onlyInteracted: 'onlyInteracted',
  openToWork: 'openToWork',
  size: 'size',
  createByIds: 'createByIds',
  degreeIds: 'degreeIds',
  preferredLocationIds: 'preferredCountries',
  relocationStatuses: 'relocationStatuses',
  noticePeriods: 'noticePeriods',
  genders: 'genders',
  ageRanges: 'ageRanges',
  races: 'races',
  veteranStatuses: 'veteranStatuses',
  disabilityStatuses: 'disabilityStatuses',
  lastActivity: 'lastActivity',
  candidateSearchType: 'candidateSearchType',
  onlyClients: 'onlyClients',
} as const;

export const searchGroupTypes = {
  ALL: 'ALL',
  APPLIED: 'APPLIED',
  SAVED: 'SAVED',
  POPULAR: 'POPULAR',
  TOP_SUGGESTION: 'TOP_SUGGESTION',
  FOLLOWERS: 'FOLLOWERS',
  FOLLOWINGS: 'FOLLOWINGS',
  INCOMING_REQUESTS: 'INCOMING_REQUESTS',
  PENDING_REQUESTS: 'PENDING_REQUESTS',
  CREATED_BY: 'CREATED_BY',
} as const;
