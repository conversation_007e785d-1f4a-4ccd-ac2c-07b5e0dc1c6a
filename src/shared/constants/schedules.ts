import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { WeekDay } from 'shared/types/preferences';
import { MeetingAttendeePermission } from 'shared/types/schedules/schedules';

export const detailsLabels = {
  [ScheduleEventTypes.MEETING]: 'meeting_details',
  [ScheduleEventTypes.REMINDER]: 'reminder_details',
  [ScheduleEventTypes.TASK]: 'task_details',
  [ScheduleEventTypes.BIRTHDAY]: 'birthday_details',
  [ScheduleEventTypes.HOLIDAY]: 'public_holiday_details',
};
export const shareLabels = {
  [ScheduleEventTypes.MEETING]: 'share_meeting',
  [ScheduleEventTypes.REMINDER]: 'share_reminder',
  [ScheduleEventTypes.TASK]: 'share_task',
  [ScheduleEventTypes.BIRTHDAY]: 'share_birthday',
  [ScheduleEventTypes.HOLIDAY]: 'share_public_holiday',
};

export const removeLabels = {
  [ScheduleEventTypes.MEETING]: 'remove_meeting',
  [ScheduleEventTypes.REMINDER]: 'remove_reminder',
  [ScheduleEventTypes.TASK]: 'remove_task',
  [ScheduleEventTypes.BIRTHDAY]: 'remove_birthday',
  [ScheduleEventTypes.HOLIDAY]: 'remove_public_holiday',
};

export const WeekDays = [
  WeekDay.MONDAY,
  WeekDay.TUESDAY,
  WeekDay.WEDNESDAY,
  WeekDay.THURSDAY,
  WeekDay.FRIDAY,
  WeekDay.SATURDAY,
  WeekDay.SUNDAY,
];

export const meetingAttendeePermissions = {
  IS_CREATOR: false,
  [MeetingAttendeePermission.INVITE_OTHERS]: false,
  [MeetingAttendeePermission.SEE_OTHER_ATTENDEES]: false,
  [MeetingAttendeePermission.MODIFY_MEETING]: false,
};

export const meetingCreatorAttendeePermissions = {
  IS_CREATOR: true,
  [MeetingAttendeePermission.INVITE_OTHERS]: true,
  [MeetingAttendeePermission.SEE_OTHER_ATTENDEES]: true,
  [MeetingAttendeePermission.MODIFY_MEETING]: true,
};
