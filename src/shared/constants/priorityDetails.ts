import type { PriorityType } from '@shared/types/pipelineTypes';
import type { IconName } from '@shared/uikit/Icon/types';
import type { colorsKeys } from '@shared/uikit/helpers/theme';

export const priorityOptions: Array<{
  value: PriorityType;
  label: PriorityType;
}> = [
  {
    value: 'CRITICAL',
    label: 'CRITICAL',
  },
  {
    value: 'HIGH',
    label: 'HIGH',
  },
  {
    value: 'MEDIUM',
    label: 'MEDIUM',
  },
  {
    value: 'LOW',
    label: 'LOW',
  },
];

export const priorityIconDetails: {
  [key in PriorityType]: { name: IconName; color: colorsKeys };
} = {
  CRITICAL: {
    color: 'error',
    name: 'priority-critical',
  },
  HIGH: {
    color: 'darkError',
    name: 'priority-high',
  },
  MEDIUM: {
    color: 'pendingOrange',
    name: 'priority-medium',
  },
  LOW: {
    color: 'brand',
    name: 'priority-low',
  },
};
