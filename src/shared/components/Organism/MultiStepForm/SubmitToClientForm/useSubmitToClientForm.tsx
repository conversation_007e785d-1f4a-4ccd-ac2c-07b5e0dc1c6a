import { useCallback } from 'react';
import {
  closeMultiStepForm,
  openMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import Icon from '@shared/uikit/Icon';
import Tooltip from '@shared/uikit/Tooltip';
import Typography from '@shared/uikit/Typography';
import {
  modifySelectedJobsByVendor,
  suggestSubmittedJob,
} from '@shared/utils/api/jobs';
import { getSuggestCompany } from '@shared/utils/api/search';
import { QueryKeys } from '@shared/utils/constants';
import useReactMutation from '@shared/utils/hooks/useReactMutation';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import useToast from '@shared/uikit/Toast/useToast';
import StepsList from './SubmitToClientStepsList';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';

const stepTitles = {
  client: 'submit_to_client',
  job: 'choose_job',
};

const placeholderText = {
  client: 'search_client',
  job: 'search_job',
};

export default function useSubmitToClientForm() {
  const { t } = useTranslation();
  const toast = useToast();

  const { openConfirmDialog, closeConfirm } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });
  const { data: submitToClientFormState, stepKey } =
    useMultiStepFormState<'submitToClientForm'>('submitToClientForm');

  const candidateId = submitToClientFormState?.candidateId;

  const onAlert = (
    title: string,
    message: string,
    type: 'success' | 'error'
  ) => {
    toast({
      type,
      icon: `${type === 'success' ? 'check' : 'times'}-circle`,
      title,
      message,
    });
  };

  const onError = (error: any) => {
    let message = t('error_moving_candidates');
    if (error?.response?.data?.message) {
      message = error.response.data.message;
    }
    onAlert(t('error'), message, 'error');
  };

  const {
    mutate: modifySelectedJobsByVendorMutation,
    isLoading: isLoadingModifySelectedJobsByVendor,
  } = useReactMutation({
    apiFunc: modifySelectedJobsByVendor,
  });

  const getHeaderProps = () => ({
    visibleHeaderDivider: true,
    title: stepKey === 'client' ? t(stepTitles.client) : t(stepTitles.job),
    backButtonProps: {
      onClick: () => {
        openMultiStepForm({
          formName: 'submitToClientForm',
          data: {
            ...submitToClientFormState,
          },
          stepKey: 'client',
        });
      },
    },
    noCloseButton: stepKey !== 'client',
    hideBack: stepKey === 'client',
    rightContent: () =>
      stepKey === 'client' ? (
        <Tooltip
          triggerWrapperClassName="ml-auto mr-[35px]"
          trigger={
            <Icon color="smoke" type="far" name="info-circle" size={18} />
          }
        >
          <Typography size={14} font="400" height={18} color="tooltipText">
            {t('submitting_candidates_to_clients_jobs_helps_find_talent')}
          </Typography>
        </Tooltip>
      ) : (
        <></>
      ),
  });

  const getStepHeaderProps = useCallback<
    MultiStepFormStepProps['getStepHeaderProps']
  >(() => ({}), []);

  const renderFooter = () => {
    if (stepKey !== 'job') return null;
    return (
      <TwoButtonFooter
        submitLabel={t('submit')}
        secondaryButtonLabel={t('discard')}
        disabledSubmit={isLoadingModifySelectedJobsByVendor}
        onSubmitClick={handleSubmit}
        secondaryButtonOnClick={onCancel()}
      />
    );
  };

  const renderBody = () => <StepsList />;

  const onCancel = () => () => {
    openConfirmDialog({
      title: t('confirm_title'),
      message: t('confirm_desc'),
      cancelButtonText: t('confirm_cancel'),
      confirmButtonText: t('continue'),
      cancelCallback: () => {
        closeMultiStepForm('submitToClientForm');
      },
      confirmCallback: () => {
        closeConfirm();
      },
      isReverse: true,
    });
  };

  const handleSubmit = () => {
    modifySelectedJobsByVendorMutation(
      {
        jobIds: submitToClientFormState?.jobIds,
        candidateId: candidateId,
      },
      {
        onSuccess: () => {
          onAlert(
            t('jobSubmittedSuccesssTitle'),
            t('jobSubmittedSuccesssSubtitle'),
            'success'
          );
          closeMultiStepForm('submitToClientForm');
        },
        onError,
      }
    );
  };

  const apiFunc =
    stepKey === 'client' ? getSuggestCompany : suggestSubmittedJob;

  const apiParams =
    stepKey === 'client'
      ? {
          companyFilter: 'CLIENT',
        }
      : {
          clientId: submitToClientFormState?.clientId,
        };

  const placeHolder = t(
    placeholderText[stepKey === 'client' ? 'client' : 'job']
  );

  const queryName =
    stepKey === 'client'
      ? QueryKeys.getSuggestCompany
      : QueryKeys.suggestSubmittedJob;

  const onClickJob = (item: any) => {
    const index = submitToClientFormState.jobIds?.findIndex(
      (id: number) => id === item.id
    );
    if (index === -1) {
      openMultiStepForm({
        formName: 'submitToClientForm',
        data: {
          ...submitToClientFormState,
          jobIds: [...(submitToClientFormState.jobIds || []), item.id],
        },
        stepKey: 'job',
      });
    } else {
      openMultiStepForm({
        formName: 'submitToClientForm',
        data: {
          ...submitToClientFormState,
          jobIds:
            submitToClientFormState.jobIds?.filter(
              (id: number) => id !== item.id
            ) || [],
        },
        stepKey: 'job',
      });
    }
  };

  const onClickSuggestCompany = (item: any) => {
    openMultiStepForm({
      formName: 'submitToClientForm',
      data: {
        ...submitToClientFormState,
        clientId: item.id,
      },
      stepKey: 'job',
    });
  };

  return {
    handleSubmit,
    renderBody,
    getHeaderProps,
    getStepHeaderProps,
    renderFooter,
    onCancel,
    submitToClientFormState,
    stepKey,
    apiFunc,
    apiParams,
    placeHolder,
    queryName,
    onClickJob,
    onClickSuggestCompany,
  };
}
