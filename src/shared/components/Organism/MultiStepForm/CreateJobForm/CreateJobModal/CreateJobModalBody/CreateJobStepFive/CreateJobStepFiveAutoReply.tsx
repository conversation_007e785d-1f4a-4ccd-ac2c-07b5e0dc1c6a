import Info from 'shared/components/molecules/Info/Info';
import removeHtmlTagsInstring from 'shared/utils/toolkit/removeHtmlTagsInstring';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useFormikContext } from 'formik';
import type { CreateJobFormDataProps } from 'shared/types/jobsProps';
import Flex from 'shared/uikit/Flex';
import type { FC } from 'react';
import { use } from 'react';
import Typography from 'shared/uikit/Typography';
import FormGroupHeader from 'shared/uikit/Form/FormGroupHeader';
import type { AutoReplyProps } from '@shared/types/pipelineTypes';
import SubFormSectionWrapper from './SubFormSectionWrapper';
import classes from './CreateJobModalStepFiveStyles.module.scss';
import { useCreateJobModal } from '../../../CreateJobModalProvider';
import { handlePipeLines } from '../../useCreateJobSubForm';

const CreateJobStepFiveAutoReply = () => {
  const { t } = useTranslation();
  const { values } = useFormikContext() as { values: CreateJobFormDataProps };
  const { setSubForm } = useCreateJobModal();

  const pipelines = handlePipeLines(values);

  return (
    <SubFormSectionWrapper>
      <Info text={t('auto_reply_desc')} color="secondaryDisabledText" />
      <Flex>
        <FormGroupHeader title="Job stages" formSection />
        <Flex className={classes.autoReplyRoot}>
          {pipelines.map((pipeline) => (
            <AutoReplyItem
              title={pipeline.title}
              key={`pipeline_${pipeline.type}`}
              onClick={() => setSubForm(pipeline.title)}
              autoReply={(values as any)[`auto_reply_${pipeline.title}`]}
            />
          ))}
        </Flex>
      </Flex>
    </SubFormSectionWrapper>
  );
};

export default CreateJobStepFiveAutoReply;

interface AutoReplyItemProps {
  title: string;
  onClick: VoidFunction;
  autoReply: AutoReplyProps;
}

const AutoReplyItem: FC<AutoReplyItemProps> = ({
  title,
  autoReply,
  ...rest
}) => {
  const { t } = useTranslation();
  const text = removeHtmlTagsInstring(autoReply?.autoReplyMessage);
  return (
    <Flex className={classes.autoReplyItemRoot} {...rest}>
      <Flex className={classes.titleBox}>
        <Typography className={classes.arTitle}>{title}</Typography>
        {!!autoReply && (
          <Typography color="success">{t('modified')}</Typography>
        )}
      </Flex>
      <Typography color="secondaryDisabledText">{t('message')}:</Typography>
      <Typography
        lineNumber={1}
        className={!autoReply?.autoReplyMessage ? classes.notSet : ''}
      >
        {text.length ? text : t('not_set_yet')}
      </Typography>
    </Flex>
  );
};
