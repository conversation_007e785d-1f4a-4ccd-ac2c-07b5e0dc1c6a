import Flex from 'shared/uikit/Flex';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import useTranslation from 'shared/utils/hooks/useTranslation';
import translateReplacer from 'shared/utils/toolkit/translateReplacer';
import { useFormikContext } from 'formik';
import type { FC } from 'react';
import { useMemo } from 'react';
import type {
  PipelineFollowUpPeriodType,
  PipelineProps,
  TemplateDynamicValueType,
} from '@shared/types/pipelineTypes';
import classes from './CreateJobModalStepFiveStyles.module.scss';

interface AutoReplayTemplateProps {
  pipeline: PipelineProps;
}

const AutoReplayTemplate: FC<AutoReplayTemplateProps> = ({ pipeline }) => {
  const { t } = useTranslation();
  const { values } = useFormikContext() as any;

  const groups = useMemo(() => {
    let _groups = [
      {
        formGroup: {
          title: translateReplacer(t('template_num'), '1'),
          formSection: true,
        },
        cp: 'input',
        name: `_auto_reply_${pipeline.title}.autoReplyTitle`,
        label: t('message_title'),
      },
      {
        cp: 'richtext',
        name: `_auto_reply_${pipeline.title}.autoReplyMessage`,
        label: t('message'),
        showEmoji: false,
        className: classes.textarea,
        templateTexts: dynamiValues,
      },
      {
        cp: 'checkBox',
        name: `_auto_reply_${pipeline.title}.hasFollowUpMessage`,
        label: t('have_fu_msg'),
      },
    ] as any[];
    if (values[`_auto_reply_${pipeline.title}`]?.hasFollowUpMessage) {
      _groups = [
        ..._groups,
        {
          name: `_auto_reply_${pipeline.title}.followupMessagePeriod`,
          cp: 'dropdownSelect',
          label: t('fu_after'),
          options: period,
        },
        {
          cp: 'input',
          name: `_auto_reply_${pipeline.title}.followUpTitle`,
          label: t('message_title'),
        },
        {
          cp: 'richtext',
          name: `_auto_reply_${pipeline.title}.followUpMessage`,
          label: t('message'),
          showEmoji: false,
          className: classes.textarea,
          templateTexts: dynamiValues,
        },
      ];
    }
    return _groups;
  }, [pipeline, values]);

  return (
    <Flex className={classes.followupTemplateRoot}>
      <DynamicFormBuilder className={classes.formRoot} groups={groups} />
    </Flex>
  );
};

export default AutoReplayTemplate;

const period: {
  value: PipelineFollowUpPeriodType;
  label: PipelineFollowUpPeriodType;
}[] = [
  {
    label: '_3_DAYS',
    value: '_3_DAYS',
  },
  {
    label: '_5_DAYS',
    value: '_5_DAYS',
  },
  {
    label: '_7_DAYS',
    value: '_7_DAYS',
  },
  {
    label: '_14_DAYS',
    value: '_14_DAYS',
  },
];

const dynamiValues: TemplateDynamicValueType[] = [
  'APPLICANT_NAME',
  'JOB_TITLE',
  'PAGE_NAME',
  'RECRUITER_NAME',
];
