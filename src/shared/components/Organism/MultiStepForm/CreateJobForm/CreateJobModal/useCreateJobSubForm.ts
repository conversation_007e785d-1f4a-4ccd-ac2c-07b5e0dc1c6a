import { use, useMemo } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import translateReplacer from 'shared/utils/toolkit/translateReplacer';
import { useFormikContext } from 'formik';
import type {
  CreateJobAPIDataProps,
  CreateJobFormDataProps,
  LanguageLevelType,
  SkillLevelType,
} from 'shared/types/jobsProps';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import type { PipelineItemProps } from '@shared/uikit/Pipelines';
import { priorityItems } from '@shared/utils/constants';
import type { PipelineProps } from '@shared/types/pipelineTypes';
import { useCreateJobModal } from '../CreateJobModalProvider';
import type { StepFiveItemType } from '../CreateJobModalProvider';
import { mapPipeline } from '../useCreateJobForm';

export default function useCreateJobSubForm() {
  const { subForm, setSubForm } = useCreateJobModal();
  const { t } = useTranslation();
  const formikContext = useFormikContext() as any;
  const { values, setFieldValue, setValues } = formikContext || {};
  const onClickDiscardFormApplication = () => {
    setSubForm(undefined);
    setFieldValue('_phoneRequired', values.phoneRequired);
    setFieldValue('_resumeRequired', values.resumeRequired);
    setFieldValue('_coverLetterRequired', values.coverLetterRequired);
  };
  const onClickSaveFormApplication = () => {
    setSubForm(undefined);
    setFieldValue('phoneRequired', values._phoneRequired ?? false);
    setFieldValue('resumeRequired', values._resumeRequired ?? false);
    setFieldValue('coverLetterRequired', values._coverLetterRequired ?? false);
  };
  const onClickDiscardRequirements = () => {
    setSubForm(undefined);
    setValues({
      ...values,
      _experienceLevel: values.experienceLevel,
      _educationDegree: values.educationDegree,
      _contractDuration: values.contractDuration,
      _travelRequirement: values?.travelRequirement,
      _workDays: values.workDays,
      _hoursPerDay: values.hoursPerDay,
      _hoursPerWeek: values.hoursPerWeek,
      _timezone: values.timezone,
      _workAuthorizations: values.workAuthorizations,
    });
  };
  const onClickSaveRequirements = () => {
    const authorizations = values._workAuthorizations.filter(
      (item: any) =>
        !!item.country?.value?.length && !!item.authorization?.value?.length
    );

    setSubForm(undefined);
    setValues({
      ...values,
      experienceLevel: values._experienceLevel,
      educationDegree: values._educationDegree,
      contractDuration: values._contractDuration,
      workDays: values._workDays,
      hoursPerDay: values._hoursPerDay,
      hoursPerWeek: values._hoursPerWeek,
      timezone:
        values._timezone?.value != null ? values._timezone : values.timezone,
      _timezone:
        values._timezone?.value != null ? values._timezone : values.timezone,
      workAuthorizations: authorizations?.length ? authorizations : [{}],
    });
  };
  const onClickDiscardBenefits = () => {
    setSubForm(undefined);
    setValues({
      ...values,
      _currency: values.currency,
      _salaryPeriod: values.salaryPeriod,
      _min_salary: values.min_salary,
      _max_salary: values.max_salary,
      _tax_term: values.tax_term,
      _markup: values.markup,
      _benefits: values.benefits,
    });
  };
  const onClickSaveBenefits = () => {
    setSubForm(undefined);
    setValues({
      ...values,
      salaryPeriod: values._salaryPeriod,
      currency:
        values._currency?.value != null ? values._currency : values.currency,
      _currency:
        values._currency?.value != null ? values._currency : values.currency,
      min_salary: values._min_salary,
      max_salary: values._max_salary,
      tax_term: values._tax_term,
      markup: values._markup,
      benefits: values._benefits,
    });
  };
  const onClickDiscardPipelines = () => {
    setSubForm(undefined);
    setValues({
      ...values,
      _stagesPipelines: values.stagesPipelines,
      _onboardingPipelines: values.onboardingPipelines,
    });
  };
  const onClickSavePipelines = () => {
    setSubForm(undefined);
    setValues({
      ...values,
      stagesPipelines: values._stagesPipelines,
      onboardingPipelines: values._onboardingPipelines,
      pipelineDirty: true,
    });
  };
  const onClickDiscardAutoplay = () => {
    setSubForm(undefined);
    setValues({
      ...values,
    });
  };
  const onClickSaveAutoplay = () => {
    setSubForm(undefined);
    setValues({
      ...values,
    });
  };
  const onClickDiscardAutoplayTemplate = () => {
    setSubForm('auto_reply');
    setValues({
      ...values,
      [`_auto_reply_${subForm}`]: values[`auto_reply_${subForm}`],
    });
  };
  const onClickSaveAutoplayTemplate = () => {
    setSubForm('auto_reply');
    setValues({
      ...values,
      [`auto_reply_${subForm}`]: values[`_auto_reply_${subForm}`],
    });
  };
  const onClickDiscardQuestions = () => {
    setSubForm('application_form');
    setValues({
      ...values,
      _questionsData: values.questionsData,
    });
  };
  const onClickSaveQuestions = () => {
    setSubForm('questions_final');
    setValues({
      ...values,
      questionsData: values._questionsData,
    });
  };

  const handlers = useMemo(() => {
    let onClickSave = () => setSubForm(undefined);
    let onClickDiscard = () => setSubForm(undefined);

    switch (subForm) {
      case 'application_form': {
        onClickSave = onClickSaveFormApplication;
        onClickDiscard = onClickDiscardFormApplication;
        break;
      }
      case 'requirements': {
        onClickSave = onClickSaveRequirements;
        onClickDiscard = onClickDiscardRequirements;
        break;
      }
      case 'benefits': {
        onClickSave = onClickSaveBenefits;
        onClickDiscard = onClickDiscardBenefits;
        break;
      }
      case 'pipelines_and_automation': {
        onClickSave = onClickSavePipelines;
        onClickDiscard = onClickDiscardPipelines;
        break;
      }
      case 'auto_reply': {
        onClickSave = onClickSaveAutoplay;
        onClickDiscard = onClickDiscardAutoplay;
        break;
      }
      case 'questions': {
        onClickSave = onClickSaveQuestions;
        onClickDiscard = onClickDiscardQuestions;
        break;
      }
      case 'questions_final': {
        onClickSave = () => setSubForm(undefined);
        onClickDiscard = () => setSubForm('questions');
        break;
      }
      default: {
        if (!subForm) {
          onClickSave = () => setSubForm(undefined);
          onClickDiscard = () => setSubForm(undefined);
        } else {
          onClickSave = onClickSaveAutoplayTemplate;
          onClickDiscard = onClickDiscardAutoplayTemplate;
        }
        break;
      }
    }
    return {
      onClickSave,
      onClickDiscard,
    };
  }, [subForm, values]);

  const getHeaderProps = () => {
    const title = mainSteps.includes(subForm as StepFiveItemType)
      ? subForm
      : translateReplacer(t('message_template'), subForm as string);
    return {
      title: t(title as string),
      hideBack: false,
      noCloseButton: true,
      backButtonProps: {
        onClick: handlers.onClickDiscard,
      },
      hideHeader: subForm === 'questions',
    };
  };

  return { getHeaderProps, ...handlers };
}

const mainSteps: StepFiveItemType[] = [
  'application_form',
  'auto_reply',
  'benefits',
  'pipelines_and_automation',
  'requirements',
];

export const handlePipeLines = (values: CreateJobFormDataProps) => {
  let pipelines = [] as PipelineProps[];
  if (values._stagesPipelines?.length) {
    let i = 0;
    const orderedStagePipelines = values._stagesPipelines.reduce((acc, cur) => {
      if (cur.title) {
        let newPP = cur;
        if (!cur.stageType) {
          newPP.customizeStageType = 'HIRING';
        }
        newPP = { ...cur, order: i, stageType: 'HIRING' };
        i += 1;
        return [...acc, newPP];
      }
      return acc;
    }, [] as PipelineItemProps[]);
    pipelines = [...pipelines, ...orderedStagePipelines];
  }
  if (values._onboardingPipelines?.length) {
    let i = pipelines.length;
    const orderedStagePipelines = values._onboardingPipelines.reduce(
      (acc, cur) => {
        if (cur.title) {
          let newPP = cur;
          if (!cur.stageType) {
            newPP.customizeStageType = 'ONBOARDING';
          }
          newPP = { ...cur, order: i, stageType: 'ONBOARDING' };
          i += 1;
          return [...acc, newPP];
        }
        return acc;
      },
      [] as PipelineItemProps[]
    );
    pipelines = [...pipelines, ...orderedStagePipelines];
  }
  return pipelines;
};

export const useMapJobData = () => {
  const { t } = useTranslation();
  const { authUser } = useGetAppObject();
  const handleMapJobData = (
    job?: CreateJobAPIDataProps
  ): CreateJobFormDataProps | null => {
    if (!job) return null;
    const { onboardingPipelines, stagesPipelines } = mapPipeline(job.pipelines);
    return {
      ...job,
      locationWithExtraParams: {
        value: job.location?.id,
        label: job.location?.title,
        id: job.location?.id,
      },
      title: {
        label: job.title,
      },
      category: {
        label: job.categoryName,
        id: job.categoryId,
        value: job.categoryId,
      },
      workPlaceType: {
        label: job.workPlaceType,
        value: job.workPlaceType,
      },
      employmentType: {
        label: job.employmentType,
        value: job.employmentType,
      },
      responseTime: {
        label: job.responseTime?.toLowerCase(),
        value: job.responseTime,
      },
      isAddExternalJobLink: !!job.websiteUrl,
      skills: job.skills.map((_skill) => ({
        id: _skill?.id,
        label: _skill?.name,
        level: _skill?.level,
        skillLevel: _skill.level,
        progress: skillLevelProgress[_skill.level as SkillLevelType],
      })),
      languages: job.languages?.map((_lang) => ({
        id: _lang?.id?.toString(),
        label: _lang?.name,
        languageLevel: _lang?.level,
        level: _lang?.level,
        skillLevel: _lang?.level,
        progress: languageLevelProgress[_lang?.level],
      })),
      hashtags: job.hashtags ?? [],
      collaboratorUsers: job.collaborators.reduce((acc, cur) => {
        const user = {
          user: cur,
          userId: cur.userId,
        };
        if (cur.userId === authUser?.id) {
          return [user, ...acc];
        }
        return [...acc, user];
      }, [] as any),
      primary_collab: job.collaborators?.find(
        (user: any) => user?.pointOfContact
      ),
      priority: job?.priority
        ? {
            label: t(job?.priority?.toLowerCase()),
            value: job?.priority,
          }
        : priorityItems.MEDIUM,
      phoneRequired: job.phoneRequired,
      coverLetterRequired: job.coverLetterRequired,
      experienceLevel: {
        label: t(job.experienceLevel ?? ''),
        value: job.experienceLevel,
      },
      educationDegree: {
        label: t(job.educationDegree ?? ''),
        value: job.educationDegree,
      },
      contractDuration: {
        label: t(job.contractDuration ?? ''),
        value: job.contractDuration,
      },
      travelRequirement: {
        label: t(job.travelRequirement ?? ''),
        value: job.travelRequirement,
      },
      workAuthorizations: job.workAuthorizations?.length
        ? job.workAuthorizations?.map((wa) => ({
            authorization: {
              label: t(wa.title),
              value: wa.id,
            },
            country: {
              label: wa.country,
              value: wa.countryCode,
            },
          }))
        : [
            {
              country: { value: '', label: '' },
              authorization: { value: '', label: '' },
            },
          ],
      workDays: job.workDays?.map((wd) => ({
        label: t(wd),
        value: wd,
      })),
      hoursPerDay: job.hoursPerDay
        ? hoursPerDayData(job.hoursPerDay)
        : undefined,
      hoursPerWeek: job.hoursPerDay
        ? {
            label: calculateHour(job.hoursPerDay * job.workDays.length),
            value: job.hoursPerDay * job.workDays.length,
          }
        : undefined,
      timezone: job.timezoneId
        ? {
            value: job.timezoneId,
            code: job.timezoneCode,
            label: job.timezoneLabel,
            offset: job.timezoneOffset,
          }
        : undefined,
      currency: job.currencyId
        ? {
            label: `${job.currencyName} (${job.currencySymbol})`,
            code: job.currencyCode,
            name: job.currencyName,
            symbol: job.currencySymbol,
            value: job.currencyId,
          }
        : undefined,
      salaryPeriod: job.period
        ? {
            label: t(job.period),
            value: job.period,
          }
        : undefined,
      min_salary: String(job.rangeMin ?? ''),
      max_salary: String(job.rangeMax ?? ''),
      taxTermData: job.taxTermId
        ? {
            label: t(job.taxTermName ?? ''),
            value: job.taxTermId,
            name: job.taxTermName,
          }
        : undefined,
      markup: job.markup
        ? {
            label: t(job.markup),
            value: job.markup,
          }
        : undefined,
      benefits: job.benefits?.map((ben) => ({
        label: ben.title,
        value: ben.id,
      })),
      stagesPipelines,
      onboardingPipelines,
      _stagesPipelines: stagesPipelines,
      _onboardingPipelines: onboardingPipelines,
    };
  };

  return { handleMapJobData };
};

const skillLevelProgress: { [key in SkillLevelType]: number } = {
  BEGINNER: 1,
  INTERMEDIATE: 2,
  ADVANCED: 3,
  EXPERT: 4,
};

const languageLevelProgress: { [key in LanguageLevelType]: number } = {
  A1: 1,
  A2: 2,
  B1: 3,
  B2: 4,
  C1: 5,
  C2: 6,
  Native: 7,
};

const calculateHour = (time: number) => {
  const minutes = time % 60;
  const hour = Math.floor(time / 60);
  return `${hour > 9 ? hour : `0${hour}`}:${minutes > 9 ? minutes : `0${minutes}`}`;
};

const hoursPerDayData = (time: number) => ({
  value: time,
  label: calculateHour(time),
});
