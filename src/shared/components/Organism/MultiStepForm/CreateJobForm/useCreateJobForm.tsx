import React, { use, useCallback, type ComponentProps } from 'react';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from 'shared/hooks/useMultiStepForm';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import type Icon from 'shared/uikit/Icon';
import { QueryKeys } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import { getProjectsList } from 'shared/utils/api/project';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import type { ProjectProps } from 'shared/types/project';
import type { PipelineItemProps } from '@shared/uikit/Pipelines';
import formValidator from '@shared/utils/form/formValidator';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import removeHtmlTagsInstring from '@shared/utils/toolkit/removeHtmlTagsInstring';
import CreateJobModalFooter from './CreateJobModal/CreateJobModalFooter';
import CreateJobModalBody from './CreateJobModal/CreateJobModalBody';
import EmptyCreateJob from './CreateJobModal/EmptyCreateJob';
import ProjectsSkeleton from './CreateJobModal/ProjectsSkeleton';
import { useCreateJobModal } from './CreateJobModalProvider';
import useCreateJobSubForm from './CreateJobModal/useCreateJobSubForm';

const DESC_LIMIT = 8000;

export function useCreateJobForm({ jobId = '' }): MultiStepFormStepProps[] {
  const { getHeaderProps: subFormHeaderProps } = useCreateJobSubForm();
  const { t } = useTranslation();
  const { businessPage } = useGetAppObject();
  const { subForm, job } = useCreateJobModal();
  const { options } = useMultiStepFormState('createJobForm') as {
    options: {
      subForm: string;
      step: number;
    };
  };

  const {
    data: projectsData,
    fetchNextPage,
    isLoading,
    hasNextPage,
  } = useInfiniteQuery<ProjectProps>(
    [QueryKeys.getProjectsList, businessPage?.username],
    {
      func: getProjectsList,
      size: 10,
    }
  );

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    setStep,
    step,
  }) => {
    if (subForm) return subFormHeaderProps();
    return {
      title: t(jobId || job?.id ? 'edit_job' : 'create_job'),
      hideBack: step === 0,
      backButtonProps: {
        onClick: () => {
          if (step === 0) return closeMultiStepForm('createProjectForm');
          setStep((prev) => prev - 1);
          return null;
        },
      },
      noCloseButton: step !== 0,
    };
  };
  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] = ({
    step,
  }) => {
    if (subForm) return {};
    const data = headerNavigationData[step];
    return {
      ...data,
      title: t(data.title),
    };
  };
  const renderFooter: MultiStepFormStepProps['renderFooter'] = (props) => {
    if (isLoading || !projectsData?.length) return undefined;
    if (subForm === 'questions') return undefined;
    return <CreateJobModalFooter {...props} jobId={jobId} />;
  };
  const renderBody: MultiStepFormStepProps['renderBody'] = ({ step }) => {
    if (isLoading) return <ProjectsSkeleton />;
    if (!projectsData?.length) return <EmptyCreateJob />;
    return (
      <CreateJobModalBody
        step={step || options?.step || 0}
        list={projectsData}
        loadMore={() => fetchNextPage()}
        hasNextPage={hasNextPage}
      />
    );
  };

  const getValidationSchema: MultiStepFormStepProps['getValidationSchema'] =
    useCallback(
      ({ step }: { step: number }) => {
        if (subForm === 'benefits') {
          return formValidator.object().shape(
            {
              min_salary: formValidator.number().when('max_salary', {
                is: (val: number) => !!val,
                then: (schema) =>
                  schema.test('test', t('s_b_l_t_m_s'), (_, ctx) => {
                    const { parent } = ctx;
                    const { min_salary: minSalary, max_salary: maxSalary } =
                      parent;
                    return !minSalary || maxSalary > minSalary;
                  }),
                otherwise: (schema) => schema.nullable(),
              }),
              max_salary: formValidator.number().when('min_salary', {
                is: (val: number) => !!val,
                then: (schema) =>
                  schema.test('test', t('s_b_g_t_m_s'), (_, ctx) => {
                    const { parent } = ctx;
                    const { min_salary: minSalary, max_salary: maxSalary } =
                      parent;
                    return !maxSalary || maxSalary > minSalary;
                  }),
                otherwise: (schema) => schema.nullable(),
              }),
            },
            // See: https://medium.com/@sanjoyofficialp/article-cyclic-dependency-inyup-de2e8290110e
            [['max_salary', 'min_salary']]
          );
        }
        const validationSchema = {
          0: {
            projects: formValidator.array().min(1),
          },
          1: {
            title: formValidator.object().required(),
          },
          2: {
            skills: formValidator.array().min(3),
            description: formValidator
              .string()
              .required()
              .test(
                'isValidLength',
                translateReplacer(t('must_be_less_n_char'), [
                  String(DESC_LIMIT),
                ]),
                (value) => {
                  if (!value) return false; // Ensure value is not empty
                  const plainText = removeHtmlTagsInstring(value); // Remove HTML tags
                  const textLength = plainText.length; // Get text length
                  return textLength <= DESC_LIMIT; // Validate length
                }
              ),
          },
        } as any;

        return formValidator.object().shape(validationSchema[step]);
      },
      [subForm]
    );

  const stepKeyData = (index: number) => headerNavigationData[index].title;
  const data: Array<MultiStepFormStepProps> = new Array(
    headerNavigationData.length
  )
    .fill(1)
    .map((_, i) => ({
      stepKey: stepKeyData(i),
      getHeaderProps,
      getStepHeaderProps,
      renderFooter,
      renderBody,
      getValidationSchema,
    }));

  return data;
}

const headerNavigationData: {
  title: string;
  iconProps: ComponentProps<typeof Icon>;
  pathColor?: string;
  helper?: string;
}[] = [
  {
    title: 'choose_projects',
    iconProps: {
      name: 'projects-light',
      type: 'fal',
    },
    helper: 'choose_projects_description',
  },
  {
    title: 'overview',
    iconProps: {
      name: 'page-information',
      type: 'fal',
    },
  },
  {
    title: 'details',
    iconProps: {
      name: 'revision',
      type: 'fal',
    },
  },
  {
    title: 'management',
    iconProps: {
      name: 'user-cog',
      type: 'fal',
    },
  },
  {
    title: 'advance_feature',
    iconProps: {
      name: 'legal',
      type: 'fal',
    },
    helper: 'advance_feature_tootip',
  },
  {
    title: 'preview',
    iconProps: {
      name: 'page-overview',
      type: 'fal',
    },
    pathColor: 'pendingOrange',
  },
];

export const mapPipeline = (pipelines?: PipelineItemProps[]) => {
  const stagesPipelines = [] as PipelineItemProps[];
  const onboardingPipelines = [] as PipelineItemProps[];
  if (pipelines?.length) {
    const sortedPipelines = pipelines.sort(
      (a, b) => (a.order ?? 0) - (b.order ?? 0)
    );
    sortedPipelines.forEach((stage) => {
      if (stage.stageType === 'HIRING') {
        if (stage.type === 'HIRED') {
          stagesPipelines.push({ canDrag: true });
        }
        stagesPipelines.push({
          ...stage,
          ...pipelineFeatures[stage.type ?? 'CUSTOMIZE'],
        });
      } else {
        if (stage.type === 'ON_BOARDED') {
          onboardingPipelines.push({ canDrag: true });
        }
        onboardingPipelines.push({
          ...stage,
          ...pipelineFeatures[stage.type ?? 'CUSTOMIZE'],
        });
      }
    });
  }
  return { stagesPipelines, onboardingPipelines };
};

const pipelineFeatures = {
  REVIEW: {
    canDrag: false,
    canDelete: false,
    canChangeColor: true,
    canChangeSwitch: false,
    canEdit: false,
  },
  INTERVIEW: {
    canDrag: true,
    canDelete: true,
    canChangeColor: true,
    canChangeSwitch: true,
    canEdit: true,
  },
  OFFERED: {
    canDrag: true,
    canDelete: false,
    canChangeColor: true,
    canChangeSwitch: true,
    canEdit: false,
  },
  HIRED: {
    canDrag: false,
    canDelete: false,
    canChangeColor: true,
    canChangeSwitch: true,
    canEdit: false,
  },
  ON_BOARDED: {
    canDrag: false,
    canDelete: false,
    canChangeColor: true,
    canChangeSwitch: true,
    canEdit: false,
  },
  CUSTOMIZE: {
    canDrag: true,
    canDelete: true,
    canChangeColor: true,
    canChangeSwitch: true,
    canEdit: true,
  },
};
