import useTranslation from 'shared/utils/hooks/useTranslation';
import { closeMultiStepForm } from '@shared/hooks/useMultiStepForm';
import MenuItem from '@shared/uikit/MenuItem';
import type { IconName } from '@shared/uikit/Icon';
import Icon from '@shared/uikit/Icon';
import InfoCard from '@shared/uikit/InfoCard';
import Flex from '@shared/uikit/Flex';
import BaseButton from '@shared/uikit/Button/BaseButton';
import IconButton from '@shared/uikit/Button/IconButton';
import Typography from '@shared/uikit/Typography';
import type { MultiStepFormProps } from '../MultiStepForm';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
};

type CandidateBulkActionStepOneProps = {
  setCurrentStep: (value: number) => void;
};

export function useCandidateBulkActionStepOne({
  setCurrentStep,
}: CandidateBulkActionStepOneProps): SingleDataItem[] {
  const { t } = useTranslation();
  // const { hasBackModal, backToModal } = useBackToModal('createEntityPanel');

  const pipelineBulkActions = [
    {
      step: 1,
      icon: 'link' as IconName,
      title: t('link_job'),
    },
    {
      step: 2,
      icon: 'envelope' as IconName,
      title: t('email'),
    },
    {
      step: 3,
      icon: 'message' as IconName,
      title: t('message'),
    },
    {
      step: 4,
      icon: 'note' as IconName,
      title: t('note'),
    },
    {
      step: 5,
      icon: 'checklist' as IconName,
      title: t('todo'),
    },
  ];

  const getHeaderProps: SingleDataItem['getHeaderProps'] = () => ({
    title: t('set_bulk_actions'),
    // hideBack: !hasBackModal,
    // noCloseButton: hasBackModal,
    closeButtonProps: {
      onClick: () => {
        // backToModal();
        closeMultiStepForm('candidateBulkAction');
        // event.trigger(eventKeys.closeModal);
      },
    },
  });

  const data: Array<SingleDataItem> = [
    {
      stepKey: '1',
      getHeaderProps,

      renderBody: ({ setStep }) => (
        <Flex className="gap-12">
          <InfoCard
            label={t('candidate_bulk_action_info_message')}
            labelProps={{
              color: 'smoke_coal',
              className: '!text-smoke_coal',
            }}
          />
          <Flex className="gap-2">
            {pipelineBulkActions.map((item, index) => (
              <BaseButton
                onClick={() => {
                  setStep(item?.step);
                  setCurrentStep(item?.step);
                }}
                key={index}
              >
                <Flex
                  flexDir="row"
                  className="items-center justify-between hover:bg-hoverPrimary rounded p-8"
                >
                  <Flex flexDir="row" className="gap-8 items-center">
                    <IconButton
                      name={item.icon}
                      type="far"
                      size="xl40"
                      colorSchema="backgroundIconSecondary"
                      variant="rectangle"
                    />
                    <Typography
                      fontSize={15}
                      fontWeight={700}
                      color="smoke_coal"
                    >
                      {item.title}
                    </Typography>
                  </Flex>
                  <Icon
                    color="white"
                    name="chevron-right"
                    type="fas"
                    size={20}
                  />
                </Flex>
              </BaseButton>
            ))}
          </Flex>
        </Flex>
      ),
    },
  ];

  return data;
}
