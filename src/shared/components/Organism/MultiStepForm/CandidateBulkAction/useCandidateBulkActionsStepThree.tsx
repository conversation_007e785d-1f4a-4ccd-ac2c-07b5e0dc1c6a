import { useState } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import DynamicFormBuilder from '@shared/uikit/Form/DynamicFormBuilder';
import { getAllTemplates } from '@shared/utils/api/template';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import type { MultiStepFormProps } from '../MultiStepForm';
import Flex from '@shared/uikit/Flex';
import MenuItem from '@shared/uikit/MenuItem';
import Link from 'next/link';
import Typography from '@shared/uikit/Typography';
import Icon from '@shared/uikit/Icon';
import { routeNames } from '@shared/utils/constants';
import { CANDIDATE_NOTE_MAX_LENGTH } from '@shared/constants/enums';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

export function useCandidateBulkActionsStepThree(): SingleDataItem[] {
  const { t } = useTranslation();
  const [formData, setFormData] = useState<{
    item: any;
    subject: string;
    message: string;
    fieIds: number[];
    followupFileIds: number[];
    followupMessage: string;
    followupPeriod: string;
    followupTitle: string;
    hasFollowup: boolean;
  } | null>(null);

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: t('email'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep(0),
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <TwoButtonFooter
      submitLabel={t('send')}
      secondaryButtonLabel={t('discard')}
      // onSubmitClick={() => setStep((prev) => prev + 1)}
      secondaryButtonOnClick={() => setStep(0)}
    />
  );

  const data: Array<SingleDataItem> = [
    {
      stepKey: '3',
      getHeaderProps,

      renderBody: ({ setFieldValue }) => (
        <Flex className="gap-20">
          {/* <Icon name='info-circle'  /> */}
          <MenuItem
            title={t('candidate_bulk_email_message_info')}
            iconType="fal"
            iconSize={20}
            iconName="info-circle"
            className="!bg-gray_5"
            rightElement={
              <Link href={routeNames.settingsTextTemplates.email}>
                <Typography color="brand" fontSize={15} fontWeight={700}>
                  {t('modify_template')}
                </Typography>
              </Link>
            }
          />

          <DynamicFormBuilder
            className="gap-12 bg-gray_5 p-20 rounded-xl "
            groups={[
              {
                label: t('template'),
                required: true,
                name: 'template',
                cp: 'asyncAutoComplete',
                maxLength: 100,
                apiFunc: getAllTemplates,
                normalizer: (data: any) =>
                  data?.content?.map((item: any) => ({
                    value: item.id,
                    label: item.title,
                    ...item,
                  })),
                onChange: (item: any) => {
                  console.log('itemmmm', item);
                  setFormData({
                    item: { value: item.id, label: item.title },
                    subject: item?.subject,
                    message: item?.message,
                    fieIds: item?.fileIds,

                    followupFileIds: item?.followupFileIds,
                    followupMessage: item?.followupMessage,
                    followupPeriod: item?.followupMessage,
                    followupTitle: item?.followupTitle,
                    hasFollowup: item?.hasFollowup,
                  });
                  setFieldValue('template', item?.id);
                  // setFieldValue('message', item?.message);

                  // const language = item?.language;
                  // setTimeout(() => setFieldTouched('language', true), 0);
                },
                value: formData?.item,
                visibleOptionalLabel: false,

                // normalizer: lookupResponseNormalizer,
                // wrapStyle: parentClasses.formItem,
              },
              {
                name: 'subject',
                cp: 'input',
                label: t('subject'),
                required: false,
                disabled: true,
                value: formData?.subject,
                visibleOptionalLabel: false,
              },
              {
                name: 'message',
                cp: 'richtext',
                label: t(`message`),
                showEmoji: false,
                className: '!h-[252px] overflow-auto',
                helperText: t('dynamic_template_helper_text'),
                disabled: true,
                required: false,
                readonly: true,
                visibleOptionalLabel: false,
                value: formData?.message,
                defaultValue: formData?.message,
                maxLength: CANDIDATE_NOTE_MAX_LENGTH,
              },
              {
                name: 'attachmentFileIds',
                cp: 'attachmentPicker',
                value: formData?.fieIds,
                wrapStyle: 'pointer-events-none',
                label: t('attachment'),
                required: false,
                visibleOptionalLabel: false,
              },
              {
                name: 'hasFollowUp',
                cp: 'checkBox',
                label: t('has_followup_message'),
                hint: t('followup_checkbox_hint'),
                wrapStyle: 'responsive-margin-top',
                disabled: true,

                visibleOptionalLabel: false,
                value: formData?.hasFollowup,
              },
              {
                name: 'followupPeriod',
                cp: 'dropdownSelect',
                label: t('follow_up_after'),
                wrapStyle: 'responsive-margin-top',
                visibleOptionalLabel: false,
                options: [
                  {
                    label: translateReplacer(t('n_days'), ['3']),
                    value: '_3_DAYS',
                  },
                  {
                    label: t('1_week'),
                    value: '_1_WEEK',
                  },
                  {
                    label: translateReplacer(t('n_weeks'), ['2']),
                    value: '_2_WEEK',
                  },
                  {
                    label: translateReplacer(t('n_weeks'), ['3']),
                    value: '_3_WEEK',
                  },
                  {
                    label: t('1_month'),
                    value: '_1_month',
                  },
                ],
                disabled: true,
                value: formData?.followupPeriod,
              },
              {
                name: 'followupTitle',
                cp: 'richtext',
                label: t('message_title'),
                wrapStyle: 'responsive-margin-top',
                singleLine: true,
                showEmoji: false,
                disabled: true,
                value: formData?.followupTitle,
                visibleOptionalLabel: false,
              },
              {
                name: 'followupMessage',
                cp: 'richtext',
                variant: 'form-input',
                label: t('message'),
                wrapStyle: 'responsive-margin-top',
                magicUrl: true,
                disabled: true,
                helperText: t('dynamic_template_helper_text'),
                value: formData?.followupMessage,
                visibleOptionalLabel: false,
                showEmoji: false,

                required: false,
                readonly: true,
                className: '!h-[252px] overflow-auto',
              },
              {
                name: 'followupFileIds',
                cp: 'attachmentPicker',
                value: formData?.followupFileIds,
                wrapStyle: 'pointer-events-none',
                label: t('attachment'),
                required: false,
                visibleOptionalLabel: false,
              },
            ].filter(Boolean)}
          />
        </Flex>
      ),

      renderFooter,
    },
  ];

  return data;
}
