import React from 'react';
import { type FormikProps } from 'formik';
import Flex from '@shared/uikit/Flex';
import Typography, { type TypographyProps } from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import Tooltip, { type TooltipProps } from '@shared/uikit/Tooltip/Tooltip';
import Icon from '@shared/uikit/Icon';
import { type IconProps } from '@shared/uikit/Icon/types';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { type PaymentFormFields } from './useCheckoutModal';
import { type CheckoutChildModalData } from '.';
import classes from './index.module.scss';

export default function InvoiceSummary({
  formikProps,
  price,
  taxAmount,
  subtotal,
  priceUnit,
  quantity,
  requestType,
  isLoading,
}: CheckoutChildModalData['invoice'] & {
  formikProps?: FormikProps<PaymentFormFields>;
  isLoading?: boolean;
}) {
  const { t } = useTranslation();

  return (
    <Flex className={classes.invoiceSummaryWrapper}>
      {quantity ? (
        <InvoiceSummaryItem
          label={t('subtotal')}
          value={`${quantity} x ${priceUnit} = $${subtotal}`}
        />
      ) : subtotal ? (
        <InvoiceSummaryItem
          label={t('subtotal')}
          value={`$${Number(subtotal).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
        />
      ) : (
        <InvoiceSummaryItem
          label={t('subtotal')}
          value={`$${Number(priceUnit).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
        />
      )}
      {taxAmount && (
        <InvoiceSummaryItem
          label={t('tax_amount')}
          value={`$${Number(taxAmount).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
          hint={t('tax_amount_label_hint')}
          tooltipProps={{
            tooltipWrapperProps: {
              className: classes.hintTooltip,
            },
          }}
        />
      )}
      {price && (
        <InvoiceSummaryItem
          label={t('total')}
          value={`$${Number(price).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`}
        />
      )}
    </Flex>
  );
}

type InvoiceSummaryItemProps = {
  label?: string;
  labelProps?: Partial<TypographyProps>;
  value?: string;
  valueProps?: Partial<TypographyProps>;
  wrapperClassName?: string;
  hint?: string;
  hintIconProps?: IconProps;
  tooltipProps?: Omit<TooltipProps, 'trigger' | 'children'>;
};

export function InvoiceSummaryItem({
  wrapperClassName,
  label,
  labelProps = {},
  value,
  valueProps = {},
  hint,
  hintIconProps = {},
  tooltipProps = {},
}: InvoiceSummaryItemProps) {
  return (
    <Flex className={cnj(classes.invoiceSummaryItemWrapper, wrapperClassName)}>
      <Typography
        size={16}
        font="500"
        height={22}
        {...labelProps}
        className={cnj(classes.labelWithHint, labelProps?.className)}
      >
        {label}
        {hint && (
          <Tooltip
            {...tooltipProps}
            trigger={
              <Icon
                name="info-circle"
                type="fal"
                size={16}
                {...hintIconProps}
              />
            }
          >
            {hint}
          </Tooltip>
        )}
      </Typography>
      <Typography
        size={15}
        font="500"
        height={22}
        color="secondaryDisabledText"
        {...valueProps}
      >
        {value}
      </Typography>
    </Flex>
  );
}
