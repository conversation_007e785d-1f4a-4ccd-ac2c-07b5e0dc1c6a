import type {
  CallbackParams,
  SingleStepItem,
} from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import { useDiscardConfirm } from '@shared/components/Organism/MultiStepForm/ProfileSections/sections/utils/useDiscardConfirm';
import type {
  CandidateSocialInfoAPIRequestBody,
  CandidateSocialInfoFormData,
} from '@shared/types/candidates';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import linkValidation, {
  usernameValidation,
} from '@shared/utils/form/formValidator/customValidations/linkValidation';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import formValidator, {
  phoneNumberValidation,
} from 'shared/utils/form/formValidator';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { socialMediaOrigins } from '@shared/constants/socialmedia';
import Observer from '@shared/components/atoms/Observer';
import {
  CandidateFormStep2SubKeys,
  CandidateFormStepKeys,
} from '../../../constants';
import classes from '../../../CreateCandidate.module.scss';
import { useCandidateModalContext } from '../../../CreateCandidateModalProvider';
import { useAdditionalInfoStore } from '../AdditionalInfo.store';
import CandidateSocialBody from './CandidateSocial.body';

export default function useCandidateSocialStep(): SingleStepItem {
  const { t } = useTranslation();
  const { resetState, activeState } = useAdditionalInfoStore();
  const { candidate, Candidate } = useCandidateModalContext();
  const { openConfirm } = useDiscardConfirm();

  const handleClickDiscard = ({
    dirty,
    resetForm,
  }: Pick<CallbackParams, 'dirty' | 'resetForm'>) => {
    if (dirty) {
      openConfirm(() => {
        resetForm?.();
        resetState();
      });
    } else {
      resetState();
    }
  };

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    setStep,
    dirty,
    resetForm,
  }) => ({
    title: t('social_information'),
    hideBack: false,
    backButtonProps: {
      onClick: () => {
        if (activeState === 'root') {
          setStep((prev) => prev - 1);
        } else {
          handleClickDiscard({ dirty, resetForm });
        }
      },
    },
    noCloseButton: true,
  });

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] = (
    props
  ) => ({
    stepKey: CandidateFormStep2SubKeys.EXPERIENCE,
    iconProps: {
      name: 'projects-light',
      type: 'fal',
    },
  });

  const renderBody: MultiStepFormStepProps['renderBody'] = (props) => (
    <CandidateSocialBody {...props} />
  );

  const renderFooter: MultiStepFormStepProps['renderFooter'] = ({
    validateForm,
    step,
    dirty,
    resetForm,
  }) => (
    <Flex className={classes.footer}>
      <Observer validateForm={validateForm} step={step} />
      <Button
        fullWidth
        label={t('discard')}
        schema="gray-semi-transparent"
        onClick={() => handleClickDiscard({ dirty, resetForm })}
      />
      {dirty ? (
        <SubmitButton fullWidth label={t('save')} schema="primary-blue" />
      ) : (
        <Button
          onClick={resetState}
          fullWidth
          label={t('save')}
          schema="primary-blue"
        />
      )}
    </Flex>
  );

  const getValidationSchema: MultiStepFormStepProps['getValidationSchema'] = (
    props
  ) =>
    formValidator.object().shape({
      cellNumber: phoneNumberValidation.optional(),
      workNumber: phoneNumberValidation.optional(),
      homeNumber: phoneNumberValidation.optional(),
      skypeId: usernameValidation.optional(),
      linkedinUsername: usernameValidation.optional(),
      facebookUsername: usernameValidation.optional(),
      twitterUsername: usernameValidation.optional(),
      otherUrl: linkValidation.optional(),
      fullAddress: formValidator.string().optional(),
    });

  function apiFunc(data: any, ...rest: any[]) {
    return Candidate.EditSocial({
      candidateId: candidate?.id,
      body: data,
    });
  }

  return {
    apiFunc,
    initialValues: candidate,
    onSuccess: resetState,
    transform: candidateSocialStepTransform,
    stepKey: CandidateFormStepKeys.ADDITIONAL,
    getHeaderProps,
    getStepHeaderProps,
    renderBody,
    renderFooter,
    getValidationSchema,
    enableReinitialize: true,
  };
}

function candidateSocialStepTransform(
  c: CandidateSocialInfoFormData
): CandidateSocialInfoAPIRequestBody {
  return {
    cellNumber: c.cellNumber?.replace(/\s/g, ''),
    workNumber: c.workNumber?.replace(/\s/g, ''),
    homeNumber: c.homeNumber?.replace(/\s/g, ''),
    skypeId: c.skypeId,
    linkedinUrl: c.linkedinUsername
      ? socialMediaOrigins.linkedin + c.linkedinUsername
      : undefined,
    facebookUrl: c.facebookUsername
      ? socialMediaOrigins.facebook + c.facebookUsername
      : undefined,
    twitterUrl: c.twitterUsername
      ? socialMediaOrigins.x + c.twitterUsername
      : undefined,
    otherUrls: c.otherUrls,
    fullAddress: c.fullAddress,
  };
}
