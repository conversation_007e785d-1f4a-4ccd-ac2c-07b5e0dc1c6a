import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import type { FC } from 'react';
import type { MultiStepFormFooterProps } from '@shared/types/formTypes';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { CandidateFormStepKeys } from '../../constants';
import classes from '../../CreateCandidate.module.scss';

const AdditionalInfoFooter: FC<MultiStepFormFooterProps> = (props) => {
  const { setStep } = props;
  const { t } = useTranslation();

  const handleClickDiscard = () => {
    openMultiStepForm({
      formName: 'createCandidateForm',
      keepPreviousData: false,
      stepKey: CandidateFormStepKeys.GENERAL,
    });
    setStep((prev) => prev - 1);
  };

  const handleClickNext = () => {
    openMultiStepForm({
      formName: 'createCandidateForm',
      keepPreviousData: false,
      stepKey: CandidateFormStepKeys.STATUS,
    });
    setStep((prev) => prev + 1);
  };

  return (
    <Flex className={classes.footer}>
      <Button
        fullWidth
        label={t('discard')}
        schema="gray-semi-transparent"
        onClick={handleClickDiscard}
      />
      <Button
        fullWidth
        label={t('next')}
        schema="primary-blue"
        onClick={handleClickNext}
      />
    </Flex>
  );
};

export default AdditionalInfoFooter;
