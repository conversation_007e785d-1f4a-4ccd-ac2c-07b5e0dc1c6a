import type {
  CallbackParams,
  SingleStepItem,
} from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import type {
  CandidateDemographicFormData,
  CandidateDemographicInfoAPIRequestBody,
} from '@shared/types/candidates';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import formValidator, { linkValidation } from 'shared/utils/form/formValidator';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useDiscardConfirm } from '@shared/components/Organism/MultiStepForm/ProfileSections/sections/utils/useDiscardConfirm';
import Observer from '@shared/components/atoms/Observer';
import {
  CandidateFormStep2SubKeys,
  CandidateFormStepKeys,
} from '../../../constants';
import classes from '../../../CreateCandidate.module.scss';
import { useCandidateModalContext } from '../../../CreateCandidateModalProvider';
import { useAdditionalInfoStore } from '../AdditionalInfo.store';
import CandidateBackgroundBody from './CandidateBackground.body';

export default function useCandidateBackgroundStep(): SingleStepItem {
  const { t } = useTranslation();
  const { resetState, activeState } = useAdditionalInfoStore();
  const { candidate, Candidate } = useCandidateModalContext();
  const { openConfirm } = useDiscardConfirm();

  const handleClickDiscard = ({
    dirty,
    resetForm,
  }: Pick<CallbackParams, 'dirty' | 'resetForm'>) => {
    if (dirty) {
      openConfirm(() => {
        resetForm?.();
        resetState();
      });
    } else {
      resetState();
    }
  };

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    setStep,
    dirty,
    resetForm,
  }) => ({
    title: t('background_n_referrals'),
    hideBack: false,
    backButtonProps: {
      onClick: () => {
        if (activeState === 'root') {
          setStep((prev) => prev - 1);
        } else {
          handleClickDiscard({ dirty, resetForm });
        }
      },
    },
    noCloseButton: true,
  });

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] = (
    props
  ) => ({
    stepKey: CandidateFormStep2SubKeys.EXPERIENCE,
    iconProps: {
      name: 'projects-light',
      type: 'fal',
    },
  });

  const renderBody: MultiStepFormStepProps['renderBody'] = (props) => (
    <CandidateBackgroundBody {...props} />
  );

  const renderFooter: MultiStepFormStepProps['renderFooter'] = ({
    validateForm,
    step,
    dirty,
    resetForm,
  }) => (
    <Flex className={classes.footer}>
      <Observer validateForm={validateForm} step={step} />
      <Button
        fullWidth
        label={t('discard')}
        schema="gray-semi-transparent"
        onClick={() => handleClickDiscard({ dirty, resetForm })}
      />
      {dirty ? (
        <SubmitButton fullWidth label={t('save')} schema="primary-blue" />
      ) : (
        <Button
          onClick={resetState}
          fullWidth
          label={t('save')}
          schema="primary-blue"
        />
      )}
    </Flex>
  );

  const getValidationSchema: MultiStepFormStepProps['getValidationSchema'] = (
    props
  ) =>
    formValidator.object().shape({
      ssn: formValidator.number().nullable().optional(),
      referralUrl: linkValidation.optional(),
    });

  function apiFunc(data: any) {
    return Candidate.EditDemographic({
      candidateId: candidate?.id!,
      body: data,
    });
  }

  return {
    apiFunc,
    onSuccess: resetState,
    initialValues: candidate,
    transform: candidateBackgroundTransform,
    stepKey: CandidateFormStepKeys.ADDITIONAL,
    getHeaderProps,
    getStepHeaderProps,
    renderBody,
    renderFooter,
    getValidationSchema,
    enableReinitialize: true,
  };
}

function candidateBackgroundTransform(
  c: CandidateDemographicFormData
): CandidateDemographicInfoAPIRequestBody {
  return {
    gender: c.gender?.value,
    ageRange: c.ageRange?.value,
    race: c.race?.value,
    veteranStatus: c.veteranStatus?.value,
    disabilityStatus: c.disabilityStatus?.value,
    birthdate: c.birthdate ? new Date(c.birthdate) : undefined,

    referralUserId: c.referralUser?.value,
    referralUserName: c.referralUser?.name,
    referralUserSurname: c.referralUser?.surname,
    referralUserCroppedImageUrl: c.referralUser?.image,

    referralCompanyId: c.referralCompany?.value,
    referralCompanyName: c.referralCompany?.label,
    referralCompanyCroppedImageUrl: c.referralCompany?.image,
    referralEmail: c.referralEmiil,
    referralPhone: c.referralPhone?.replace(/\s/g, ''),
    referralUrl: c.referralUrl,
  };
}
