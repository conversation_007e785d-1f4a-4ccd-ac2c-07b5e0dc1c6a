import type {
  CallbackParams,
  SingleStepItem,
} from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import { useDiscardConfirm } from '@shared/components/Organism/MultiStepForm/ProfileSections/sections/utils/useDiscardConfirm';
import type {
  CandidatePreferenceInfoAPIRequestBody,
  CandidatePreferenceInfoFormData,
} from '@shared/types/candidates';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import formValidator from 'shared/utils/form/formValidator';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Observer from '@shared/components/atoms/Observer';
import classes from '../../../CreateCandidate.module.scss';
import { useCandidateModalContext } from '../../../CreateCandidateModalProvider';
import {
  CandidateFormStep2SubKeys,
  CandidateFormStepKeys,
} from '../../../constants';
import { useAdditionalInfoStore } from '../AdditionalInfo.store';
import CandidatePreferenceBody from './CandidatePreference.body';

export default function useCandidatePreferenceStep(): SingleStepItem {
  const { t } = useTranslation();
  const { resetState, activeState } = useAdditionalInfoStore();
  const { candidate, Candidate } = useCandidateModalContext();
  const { openConfirm } = useDiscardConfirm();

  const handleClickDiscard = ({
    dirty,
    resetForm,
  }: Pick<CallbackParams, 'dirty' | 'resetForm'>) => {
    if (dirty) {
      openConfirm(() => {
        resetForm?.();
        resetState();
      });
    } else {
      resetState();
    }
  };

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    setStep,
    dirty,
    resetForm,
  }) => ({
    title: t('preferences_n_benefits'),
    hideBack: false,
    backButtonProps: {
      onClick: () => {
        if (activeState === 'root') {
          setStep((prev) => prev - 1);
        } else {
          handleClickDiscard({ dirty, resetForm });
        }
      },
    },
    noCloseButton: true,
  });

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] = (
    props
  ) => ({
    stepKey: CandidateFormStep2SubKeys.EXPERIENCE,
    iconProps: {
      name: 'projects-light',
      type: 'fal',
    },
  });

  const renderBody: MultiStepFormStepProps['renderBody'] = (props) => (
    <CandidatePreferenceBody {...props} />
  );

  const renderFooter: MultiStepFormStepProps['renderFooter'] = ({
    validateForm,
    step,
    dirty,
    resetForm,
  }) => (
    <Flex className={classes.footer}>
      <Observer validateForm={validateForm} step={step} />
      <Button
        fullWidth
        label={t('discard')}
        schema="gray-semi-transparent"
        onClick={() => handleClickDiscard({ dirty, resetForm })}
      />
      {dirty ? (
        <SubmitButton fullWidth label={t('save')} schema="primary-blue" />
      ) : (
        <Button
          onClick={resetState}
          fullWidth
          label={t('save')}
          schema="primary-blue"
        />
      )}
    </Flex>
  );

  const getValidationSchema: MultiStepFormStepProps['getValidationSchema'] = (
    props
  ) =>
    formValidator.object().shape({
      expectedMinimumSalary: formValidator.number().optional(),
      expectedMaximumSalary: formValidator.number().optional(),
      expectedMarkup: formValidator.number().optional(),
      preferredLocation: formValidator.object().nullable().optional(),
    });

  function apiFunc(data: any) {
    return Candidate.EditPreference({
      candidateId: candidate?.id!,
      body: data,
    });
  }

  return {
    apiFunc,
    onSuccess: resetState,
    initialValues: candidate,
    transform: candidatePreferenceStepTransform,
    stepKey: CandidateFormStepKeys.ADDITIONAL,
    getHeaderProps,
    getStepHeaderProps,
    renderBody,
    renderFooter,
    getValidationSchema,
    enableReinitialize: true,
  };
}

function candidatePreferenceStepTransform(
  c: CandidatePreferenceInfoFormData
): CandidatePreferenceInfoAPIRequestBody {
  return {
    preferredJobTitle: c.preferredJob?.label,
    preferredEmploymentType: c.preferredEmploymentType?.value,
    preferredWorkPlaceType: c.preferredWorkPlaceType?.value,
    preferredExperienceLevel: c.preferredExperienceLevel?.value,
    noticePeriod: c.noticePeriod?.value,
    preferredLocation: c.preferredLocation,
    relocation: c.relocation?.value,
    travelRequirement: c.travelRequirement?.value,
    expectedCurrencyId: c.expectedCurrency?.value,
    expectedCurrencyCode: c.expectedCurrency?.code,
    expectedCurrencyTitle: c.expectedCurrency?.label,
    expectedSalaryPeriod: c.expectedSalaryPeriod?.value,
    expectedMinimumSalary: c.expectedMinimumSalary
      ? +c.expectedMinimumSalary
      : undefined,
    expectedMaximumSalary: c.expectedMaximumSalary
      ? +c.expectedMaximumSalary
      : undefined,
  };
}
