import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import formValidator from 'shared/utils/form/formValidator';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import type { SingleStepItem } from '../../../MultiStepForm';
import {
  CandidateFormStepKeys,
  CandidateFormStep3SubKeys as SubKeys,
} from '../../constants';
import AdditionalInfoBody from './AdditionalInfo.body';
import AdditionalInfoFooter from './AdditionalInfo.footer';
import { useAdditionalInfoStore } from './AdditionalInfo.store';
import useCandidateLegalStep from './Step3-2/useCandidateLegalStep';
import useCandidatePreferenceStep from './Step3-3/useCandidatePreferenceStep';
import useCandidateSocialStep from './Step3-1/useCandidateSocialStep';
import useCandidateBackgroundStep from './Step3-4/useCandidateBackgroundStep';

export default function useAdditionalInfoStep(): SingleStepItem {
  const { t } = useTranslation();
  const socialStep = useCandidateSocialStep();
  const legalStep = useCandidateLegalStep();
  const preferenceStep = useCandidatePreferenceStep();
  const backgroundStep = useCandidateBackgroundStep();
  const { activeState, resetState } = useAdditionalInfoStore();

  /** early return on sub steps */
  if (activeState === SubKeys.SOCIAL) return socialStep;
  if (activeState === SubKeys.LEGAL) return legalStep;
  if (activeState === SubKeys.PREFERENCE) return preferenceStep;
  if (activeState === SubKeys.BACKGROUND) return backgroundStep;

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    setStep,
    step,
  }) => ({
    title: t('modify_candidate'),
    hideBack: false,
    backButtonProps: {
      onClick: () => {
        if (activeState === 'root') {
          openMultiStepForm({
            formName: 'createCandidateForm',
            keepPreviousData: false,
            stepKey: CandidateFormStepKeys.GENERAL,
          });
          setStep((prev) => prev - 1);
        } else {
          resetState();
        }
      },
    },
    noCloseButton: true,
  });

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] = (
    props
  ) => ({
    title: t('additional_information'),
    stepKey: CandidateFormStepKeys.ADDITIONAL,
    iconProps: {
      name: 'additional-info',
      type: 'fal',
    },
    // helper: '',
  });

  const renderBody: MultiStepFormStepProps['renderBody'] = (props) => (
    <AdditionalInfoBody {...props} />
  );

  const renderFooter: MultiStepFormStepProps['renderFooter'] = (props) => (
    <AdditionalInfoFooter {...props} />
  );

  const getValidationSchema: MultiStepFormStepProps['getValidationSchema'] = (
    props
  ) => formValidator.object().shape({});

  return {
    stepKey: CandidateFormStepKeys.ADDITIONAL,
    getHeaderProps,
    getStepHeaderProps,
    renderBody,
    renderFooter,
    getValidationSchema,
  };
}
