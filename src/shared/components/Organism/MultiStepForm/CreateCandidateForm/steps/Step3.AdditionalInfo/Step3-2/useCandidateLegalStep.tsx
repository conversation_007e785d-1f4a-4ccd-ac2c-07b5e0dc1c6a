import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import formValidator, {
  phoneNumberValidation,
} from 'shared/utils/form/formValidator';
import useTranslation from 'shared/utils/hooks/useTranslation';
import type {
  CallbackParams,
  SingleStepItem,
} from '@shared/components/Organism/MultiStepForm/MultiStepForm';
import type {
  CandidateLegalInfoAPIRequestBody,
  CandidateLegalInfoFormData,
} from '@shared/types/candidates';
import Flex from '@shared/uikit/Flex';
import Button from '@shared/uikit/Button';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import { useDiscardConfirm } from '@shared/components/Organism/MultiStepForm/ProfileSections/sections/utils/useDiscardConfirm';
import Observer from '@shared/components/atoms/Observer';
import { useAdditionalInfoStore } from '../AdditionalInfo.store';
import CandidateLegalBody from './CandidateLegal.body';
import classes from '../../../CreateCandidate.module.scss';
import {
  CandidateFormStep2SubKeys,
  CandidateFormStepKeys,
} from '../../../constants';
import { useCandidateModalContext } from '../../../CreateCandidateModalProvider';

export default function useCandidateLegalStep(): SingleStepItem {
  const { t } = useTranslation();
  const { resetState, activeState } = useAdditionalInfoStore();
  const { candidate, Candidate } = useCandidateModalContext();
  const { openConfirm } = useDiscardConfirm();

  const handleClickDiscard = ({
    dirty,
    resetForm,
  }: Pick<CallbackParams, 'dirty' | 'resetForm'>) => {
    if (dirty) {
      openConfirm(() => {
        resetForm?.();
        resetState();
      });
    } else {
      resetState();
    }
  };

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    setStep,
    dirty,
    resetForm,
  }) => ({
    title: t('residency_n_legal_information'),
    hideBack: false,
    backButtonProps: {
      onClick: () => {
        if (activeState === 'root') {
          setStep((prev) => prev - 1);
        } else {
          handleClickDiscard({ dirty, resetForm });
        }
      },
    },
    noCloseButton: true,
  });

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] = (
    props
  ) => ({
    stepKey: CandidateFormStep2SubKeys.EXPERIENCE,
    iconProps: {
      name: 'projects-light',
      type: 'fal',
    },
  });

  const renderBody: MultiStepFormStepProps['renderBody'] = (props) => (
    <CandidateLegalBody {...props} />
  );

  const renderFooter: MultiStepFormStepProps['renderFooter'] = ({
    validateForm,
    step,
    dirty,
    resetForm,
  }) => (
    <Flex className={classes.footer}>
      <Observer validateForm={validateForm} step={step} />
      <Button
        fullWidth
        label={t('discard')}
        schema="gray-semi-transparent"
        onClick={() => handleClickDiscard({ dirty, resetForm })}
      />
      {dirty ? (
        <SubmitButton fullWidth label={t('save')} schema="primary-blue" />
      ) : (
        <Button
          onClick={resetState}
          fullWidth
          label={t('save')}
          schema="primary-blue"
        />
      )}
    </Flex>
  );

  const getValidationSchema: MultiStepFormStepProps['getValidationSchema'] = (
    props
  ) =>
    formValidator.object().shape({
      referralEmail: formValidator.string().email().optional(),
      referralPhone: phoneNumberValidation.optional(),
      referralUrl: formValidator.string().optional(),
    });

  function apiFunc(data: any) {
    return Candidate.EditLegal({
      candidateId: candidate?.id!,
      body: data,
    });
  }
  return {
    apiFunc,
    initialValues: candidate,
    onSuccess: resetState,
    transform: candidateLegalStepTransform,
    stepKey: CandidateFormStepKeys.ADDITIONAL,
    getHeaderProps,
    getStepHeaderProps,
    renderBody,
    renderFooter,
    getValidationSchema,
    enableReinitialize: true,
  };
}

function candidateLegalStepTransform(
  c: CandidateLegalInfoFormData
): CandidateLegalInfoAPIRequestBody {
  return {
    country: c.country?.label,
    countryCode: c.country?.value,
    identificationDocumentType: c.identificationDocument?.value,
    ssn: c.ssn,
    workAuthorizationId: c.workAuthorization?.value
      ? c.workAuthorization?.value
      : undefined,
    workAuthorizationTitle: c.workAuthorization?.label,

    workAuthorizationExpiryDate: c.workAuthorizationExpiryDate,
    visaHeldByUserId: c.visaHeldByUser?.value,
    visaHeldByUserName: c.visaHeldByUser?.name,
    visaHeldByUserSurname: c.visaHeldByUser?.surname,
    visaHeldByUserCroppedImageUrl: c.visaHeldByUser?.image,
    expectedMarkup: c.expectedMarkup ? +c.expectedMarkup : undefined,
    expectedTaxTermId: c.expectedTaxTerm?.value
      ? c.expectedTaxTerm?.value
      : undefined,
    expectedTaxTermTitle: c.expectedTaxTerm?.label,
    criminalRecord: c?.criminalRecord?.value,
    fileIds: c.fileIds ?? [],
  };
}
