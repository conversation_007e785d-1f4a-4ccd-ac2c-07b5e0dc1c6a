import type {
  CandidateStatusInfoAPIRequestBody,
  CandidateStatusInfoFormData,
} from '@shared/types/candidates';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import {
  closeMultiStepForm,
  openMultiStepForm,
} from 'shared/hooks/useMultiStepForm';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import formValidator from 'shared/utils/form/formValidator';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useLinkJobsToCandidate } from '@shared/components/Organism/AsyncPickerModal/presets/job/hooks';
import Observer from '@shared/components/atoms/Observer';
import type { CallbackParams, SingleStepItem } from '../../../MultiStepForm';
import classes from '../../CreateCandidate.module.scss';
import { useCandidateModalContext } from '../../CreateCandidateModalProvider';
import { CandidateFormStepKeys } from '../../constants';
import StatusInfoBody from './StatusInfo.body';
import { useDiscardConfirm } from '../../../ProfileSections/sections/utils/useDiscardConfirm';

export default function useStatusInfoStep(): SingleStepItem {
  const { t } = useTranslation();
  const { candidate, Candidate } = useCandidateModalContext();
  const { openConfirm } = useDiscardConfirm();

  const linkJobsAsyncPicker = useLinkJobsToCandidate(candidate!);

  const handleClickDiscard = ({
    dirty,
    resetForm,
    setStep,
  }: Pick<CallbackParams, 'dirty' | 'resetForm' | 'setStep'>) => {
    const callback = () => {
      openMultiStepForm({
        formName: 'createCandidateForm',
        keepPreviousData: false,
        stepKey: CandidateFormStepKeys.ADDITIONAL,
      });
      setStep((prev) => prev - 1);
    };

    if (dirty) {
      openConfirm(() => {
        resetForm?.();
        callback();
      });
    } else {
      callback();
    }
  };

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    setStep,
    dirty,
    resetForm,
  }) => ({
    title: t('modify_candidate'),
    hideBack: false,
    backButtonProps: {
      onClick: () => {
        handleClickDiscard({ dirty, resetForm, setStep });
      },
    },
    noCloseButton: true,
  });

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] = ({
    step,
  }) => ({
    title: t('create'),
    stepKey: CandidateFormStepKeys.NEW,
    pathColor: 'pendingOrange',
    iconProps: {
      name: 'candidate-check',
      type: 'fal',
    },
  });

  const renderBody: MultiStepFormStepProps['renderBody'] = (props) => (
    <StatusInfoBody {...props} />
  );

  const renderFooter: MultiStepFormStepProps['renderFooter'] = ({
    validateForm,
    setStep,
    step,
    dirty,
    resetForm,
  }) => (
    <Flex className={classes.footer}>
      <Observer validateForm={validateForm} step={step} />
      <Button
        fullWidth
        label={t('discard')}
        schema="gray-semi-transparent"
        onClick={() => handleClickDiscard({ dirty, resetForm, setStep })}
      />
      <SubmitButton fullWidth label={t('update')} schema="primary-blue" />
    </Flex>
  );

  const getValidationSchema = () =>
    formValidator.object().shape({
      tags: formValidator.array().of(formValidator.string()).optional(),
      resumeUrl: formValidator.string().optional(),
      note: formValidator.string().optional(),
    });

  function apiFunc(data: any) {
    return Candidate.EditAdditional({
      candidateId: candidate?.id!,
      body: data,
    });
  }

  function onSuccess() {
    closeMultiStepForm('createCandidateForm');
    linkJobsAsyncPicker.open();
  }

  return {
    onSuccess,
    apiFunc,
    initialValues: candidate,
    stepKey: CandidateFormStepKeys.STATUS,
    transform: candidateStatusInfoStepTransform,
    getHeaderProps,
    getStepHeaderProps,
    renderBody,
    renderFooter,
    getValidationSchema,
    enableReinitialize: true,
  };
}

function getTags(tags?: string[]): string[] {
  return (tags ?? []).filter((t) => !!t) ?? [];
}

function candidateStatusInfoStepTransform(
  c: CandidateStatusInfoFormData
): CandidateStatusInfoAPIRequestBody {
  return {
    resumeUrl: c.resumeUrl,
    tags: getTags(c.tags),
    openToWork: c.openToWork?.value,
    note: c.note,
    jobSite: c.jobSite?.value,
  };
}
