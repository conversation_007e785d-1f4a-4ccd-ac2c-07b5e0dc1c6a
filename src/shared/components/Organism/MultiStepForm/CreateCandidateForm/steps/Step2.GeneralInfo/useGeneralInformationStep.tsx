import { closeMultiStepForm } from 'shared/hooks/useMultiStepForm';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import useTranslation from 'shared/utils/hooks/useTranslation';
import formValidator from 'shared/utils/form/formValidator';
import { useMemo } from 'react';
import { FormikHelpers } from 'formik';
import {
  CandidateFormStep2SubKeys as SubKeys,
  CandidateFormStepKeys,
  CandidateFormStep2SubKeys,
} from '../../constants';
import GeneralInfoBody from './GeneralInfo.body';
import GeneralInfoFooter from './GeneralInfo.footer';
import { useGeneralInfoStore } from './GeneralInfo.store';
import useCandidateExperienceStep from './CandidateExperience/useCandidateExperienceStep';
import useCandidateEducationStep from './CandidateEducation/useCandidateEducationStep';
import type { SingleStepItem } from '../../../MultiStepForm';
import { transformSkill } from '../../../ProfileSections/sections/useSkill';
import { transformLanguage } from '../../../ProfileSections/sections/useLanguage';
import { useCandidateModalContext } from '../../CreateCandidateModalProvider';

export default function useGeneralInfoStep(): SingleStepItem {
  const { t } = useTranslation();
  const experience = useCandidateExperienceStep();
  const education = useCandidateEducationStep();
  const { activeState, activeAction, resetState, skill, language } =
    useGeneralInfoStore();
  const { candidate, Skill, Language } = useCandidateModalContext();

  const initialValues = useMemo(() => {
    if (activeState === CandidateFormStep2SubKeys.SKILL) return skill;
    if (activeState === CandidateFormStep2SubKeys.LANGUAGE) return language;
    return {};
  }, [activeState, skill, language]);

  /** early return on sub steps */
  if (activeState === SubKeys.EXPERIENCE) return experience;
  if (activeState === SubKeys.EDUCATION) return education;

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    setStep,
    step,
  }) => ({
    title: t('modify_candidate'),
    hideBack: true,
    backButtonProps: {
      onClick: () => {
        if (activeState === 'root') {
          closeMultiStepForm('createCandidateForm');
        } else {
          resetState();
        }
      },
    },
    noCloseButton: false,
  });

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] = (
    props
  ) => {
    const rootHeaderProps = {
      title: t('professional_background'),
      stepKey: CandidateFormStepKeys.GENERAL,
      iconProps: {
        name: 'job',
        type: 'fal',
      },
      // helper: '',
    } as const;
    return rootHeaderProps;
  };

  const renderBody: MultiStepFormStepProps['renderBody'] = (props) => (
    <GeneralInfoBody {...props} values={undefined} />
  );

  const renderFooter: MultiStepFormStepProps['renderFooter'] = (props) => (
    <GeneralInfoFooter {...props} />
  );

  const getValidationSchema: MultiStepFormStepProps['getValidationSchema'] = (
    props
  ) =>
    formValidator.object().shape({
      experiences: formValidator.array(),
      educations: formValidator.array(),
      skills: formValidator.array(),
      languages: formValidator.array(),
    });

  function apiFunc(data: any) {
    if (activeState === CandidateFormStep2SubKeys.SKILL) {
      resetState();
      return activeAction === 'add'
        ? Skill.Add({ candidateId: candidate?.id, body: data })
        : Skill.Edit({ skillId: skill.id, body: data });
    }
    if (activeState === CandidateFormStep2SubKeys.LANGUAGE) {
      resetState();
      return activeAction === 'add'
        ? Language.Add({ candidateId: candidate?.id, body: data })
        : Language.Edit({ languageId: language.id, body: data });
    }
    return {};
  }

  function transform(data: any) {
    if (activeState === CandidateFormStep2SubKeys.SKILL)
      return transformSkill(data);
    if (activeState === CandidateFormStep2SubKeys.LANGUAGE)
      return transformLanguage(data);
    return data;
  }

  function onSuccess(d: unknown, formik: FormikHelpers<unknown>) {
    formik.resetForm();
  }

  return {
    apiFunc,
    initialValues,
    transform,
    onSuccess,
    stepKey: CandidateFormStepKeys.GENERAL,
    getHeaderProps,
    getStepHeaderProps,
    renderBody,
    renderFooter,
    getValidationSchema,
    enableReinitialize: true,
  };
}
