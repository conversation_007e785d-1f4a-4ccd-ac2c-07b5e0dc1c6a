import CandidateInfoCard from '@shared/components/molecules/CandidateCard/CandidateInfoCard';
import Info from '@shared/components/molecules/Info/Info';
import IconButton from '@shared/uikit/Button/IconButton';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import ModalTransitionWrapper from '@shared/uikit/Modal/TransitionWrapper/ModalTransitionWrapper';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import ProgressItem from '@shared/uikit/ProgressItem';
import { useFormikContext } from 'formik';
import { useCallback, useEffect, useMemo } from 'react';
import Flex from 'shared/uikit/Flex';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import FormGroupHeader from 'shared/uikit/Form/FormGroupHeader';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { candidateEducationNormalizer } from '@shared/utils/normalizers/beforeCacheCandidateInfo';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import type { CallbackParams } from '../../../MultiStepForm';
import { AddButtonFooter } from '../../../ProfileSections/Components/AddButtonFooter';
import { ViewCard } from '../../../ProfileSections/Components/ViewCard';
import { getLanguageGroups } from '../../../ProfileSections/sections/useLanguage';
import { getSkillGroups } from '../../../ProfileSections/sections/useSkill';
import { useDiscardConfirm } from '../../../ProfileSections/sections/utils/useDiscardConfirm';
import { CandidateFormStep2SubKeys } from '../../constants';
import classes from '../../CreateCandidate.module.scss';
import { useCandidateModalContext } from '../../CreateCandidateModalProvider';
import { useGeneralInfoStore } from './GeneralInfo.store';

const GeneralInfoBody: React.FC<CallbackParams> = () => {
  const { candidate, Skill, Language, isLoboxUser } =
    useCandidateModalContext();
  const { t } = useTranslation();
  const {
    activeAction,
    activeState,
    setState,
    resetState,
    skill,
    language,
    readonly,
  } = useGeneralInfoStore();
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });
  const { dirty, resetForm } = useFormikContext();
  const { openConfirm } = useDiscardConfirm();

  const discardAndCallNext = useCallback(
    (next: VoidFunction) => () => {
      if (dirty) {
        openConfirm(() => {
          resetForm?.();
          next();
        });
      } else {
        next();
      }
    },
    [resetForm, dirty, openConfirm]
  );

  useEffect(() => {
    resetForm();
  }, [activeState]);

  const deleteWithConfirm = (func: Function) => () => {
    openConfirmDialog({
      title: t('delete'),
      message: t('delete_item_confirmation'),
      confirmButtonText: t('delete'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func,
        onSuccess: resetState,
      },
    });
  };

  const flatExperiences = useMemo(
    () =>
      (candidate?._experiences ?? [])
        ?.map((item: any) => {
          if (!item?.steps) return item;
          return item?.steps?.map((step: any) => ({
            ...step,
            image: item?.image,
          }));
        })
        ?.flat(),
    [candidate?._experiences]
  );

  const skills = candidate?._skills ?? [];

  const languages = candidate?._languages ?? [];

  const educations = useMemo(
    () => candidateEducationNormalizer(candidate?._educations ?? [], t),
    [t, candidate?._educations]
  );

  const skillForm = (() => (
    <DynamicFormBuilder
      className={classes.formBuilder}
      groups={getSkillGroups({
        t,
        setActiveItem: () => {},
        setActiveState: discardAndCallNext(resetState),
        disabledReadOnly: readonly,
        skills,
      })}
    />
  ))();

  const languageForm = (() => (
    <DynamicFormBuilder
      className={classes.formBuilder}
      groups={getLanguageGroups({
        t,
        setActiveItem: () => {},
        setActiveState: discardAndCallNext(resetState),
        disabledReadOnly: readonly,
        languages,
      })}
    />
  ))();

  return (
    <ModalTransitionWrapper className={classes.stepWrapper}>
      {isLoboxUser ? (
        <Info
          text={t('candidate_exists__add_more')}
          textColor="secondaryText"
          color="warning"
          icon="exclamation-triangle"
          className="!bg-[#FF9A3E1A]"
        />
      ) : null}
      {candidate && (
        <CandidateInfoCard
          avatar={candidate?.profile?.croppedImageUrl}
          firstText={candidate?.profile?.fullName}
          secondText={candidate?.profile?.usernameAtSign}
          thirdText={candidate.profile?.occupation?.label}
          fourthText={cleanRepeatedWords(
            candidate?.profile?.location?.title || ''
          )}
        />
      )}
      <Flex className={classes.formSection}>
        <Flex className={classes.listContainer}>
          <FormGroupHeader
            formSection
            title={t('work_history')}
            className={classes.listGroupHeader}
          />
          {flatExperiences?.map((item: any) => (
            <ViewCard
              key={item.id}
              readonly={item.recruiterData}
              renderRight={() =>
                item.recruiterData ? (
                  <Flex flexDir="column" className="justify-between items-end">
                    <IconButton
                      colorSchema="transparent"
                      name="pen-light"
                      type="fal"
                      className={cnj(
                        classes.rightIcon,
                        readonly ? classes.readonly : undefined
                      )}
                      size="md20"
                    />
                    <Typography
                      font="700"
                      size={15}
                      height={23}
                      color="success"
                      className="text-nowrap"
                    >
                      {t('recruiter_data')}
                    </Typography>
                  </Flex>
                ) : null
              }
              item={{
                id: item?.id,
                image: item?.image,
                firstText: item?.realData?.job?.label,
                secondText: item?.realData?.company?.label,
                thirdText: item?.secondText,
                onClick: () =>
                  setState({
                    activeState: CandidateFormStep2SubKeys.EXPERIENCE,
                    readonly: item?.realData.originalId !== null,
                    activeAction: 'edit',
                    experience: item.realData,
                  }),
              }}
            />
          ))}
        </Flex>
        <AddButtonFooter
          label={t('add_experience')}
          onClick={() => {
            setState({
              activeState: CandidateFormStep2SubKeys.EXPERIENCE,
              activeAction: 'add',
              experience: {},
            });
          }}
        />
      </Flex>
      <Flex className={classes.formSection}>
        <Flex className={classes.listContainer}>
          <FormGroupHeader
            formSection
            title={t('educations')}
            className={classes.listGroupHeader}
          />
          {educations?.map((item: any) => (
            <ViewCard
              key={item.id}
              readonly={item?.realData.originalId !== null}
              renderRight={() =>
                item?.realData.originalId === null ? (
                  <Flex flexDir="column" className="justify-between items-end">
                    <IconButton
                      colorSchema="transparent"
                      name="pen-light"
                      type="fal"
                      className={cnj(
                        classes.rightIcon,
                        readonly ? classes.readonly : undefined
                      )}
                      size="md20"
                    />
                    <Typography
                      font="700"
                      size={15}
                      height={23}
                      color="success"
                      className="text-nowrap"
                    >
                      {t('recruiter_data')}
                    </Typography>
                  </Flex>
                ) : null
              }
              item={{
                id: item?.id,
                image: item?.image,
                firstText: item?.realData?.major?.label,
                secondText: item?.realData?.school?.label,
                thirdText: item?.secondText,
                onClick: () =>
                  setState({
                    activeState: CandidateFormStep2SubKeys.EDUCATION,
                    readonly: item?.realData.originalId !== null,
                    activeAction: 'edit',
                    education: item.realData,
                  }),
              }}
            />
          ))}
        </Flex>
        <AddButtonFooter
          label={t('add_education')}
          onClick={() => {
            setState({
              activeState: CandidateFormStep2SubKeys.EDUCATION,
              activeAction: 'add',
              education: {},
            });
          }}
        />
      </Flex>
      <Flex className={classes.formSection}>
        <FormGroupHeader
          formSection
          title={t('skills')}
          className={classes.listGroupHeader}
        />
        {skills.length > 0 ? (
          <Flex className={classes.formRoot}>
            {skills?.map((item, index) => {
              const isEditOpen =
                activeAction === 'edit' &&
                activeState === CandidateFormStep2SubKeys.SKILL &&
                item?.id === skill?.id;

              if (isEditOpen) return skillForm;
              return (
                <ProgressItem
                  key={item?.id}
                  title={item?.name}
                  badgeText={
                    item.recruiterData ? t('recruiter_data') : undefined
                  }
                  progressValue={item?.progress}
                  tooltipText={t(item?.level)}
                  progressSteps={4}
                  alwaysVisibleActionButton
                  actionButton={
                    !item?.realData.originalId ? (
                      <PopperMenu
                        placement="bottom-end"
                        buttonComponent={
                          <IconButton
                            type="far"
                            name="ellipsis-h"
                            size="sm"
                            colorSchema="transparent"
                          />
                        }
                      >
                        <PopperItem
                          onClick={discardAndCallNext(() => {
                            setState({
                              activeState: CandidateFormStep2SubKeys.SKILL,
                              readonly: item?.realData.originalId !== null,
                              activeAction: 'edit',
                              skill: item.realData,
                            });
                          })}
                          iconName="pen"
                          label={t('edit')}
                        />
                        <PopperItem
                          onClick={deleteWithConfirm(() =>
                            Skill.Remove({ skillId: item.realData.id })
                          )}
                          iconName="trash"
                          label={t('remove')}
                        />
                      </PopperMenu>
                    ) : null
                  }
                />
              );
            })}
          </Flex>
        ) : null}

        {activeAction === 'add' &&
        activeState === CandidateFormStep2SubKeys.SKILL ? (
          skillForm
        ) : (
          <IconButton
            className={classes.addBtn}
            type="far"
            size="md15"
            name="plus"
            colorSchema="backgroundIconSecondary"
            onClick={discardAndCallNext(() => {
              setState({
                activeState: CandidateFormStep2SubKeys.SKILL,
                activeAction: 'add',
                skill: undefined,
              });
            })}
          />
        )}
      </Flex>
      <Flex className={classes.formSection}>
        <FormGroupHeader
          formSection
          title={t('languages')}
          className={classes.listGroupHeader}
        />
        {languages.length > 0 ? (
          <Flex className={classes.formRoot}>
            {languages?.map((item, index) => {
              const isEditOpen =
                activeAction === 'edit' &&
                activeState === CandidateFormStep2SubKeys.LANGUAGE &&
                item?.id === language?.id;

              if (isEditOpen) return languageForm;
              return (
                <ProgressItem
                  key={item?.id}
                  title={item?.name}
                  badgeText={
                    item.recruiterData ? t('recruiter_data') : undefined
                  }
                  progressValue={item?.progress}
                  tooltipText={t(item?.level)}
                  progressSteps={7}
                  alwaysVisibleActionButton
                  actionButton={
                    !item?.realData.originalId ? (
                      <PopperMenu
                        placement="bottom-end"
                        buttonComponent={
                          <IconButton
                            type="far"
                            name="ellipsis-h"
                            size="sm"
                            colorSchema="transparent"
                          />
                        }
                      >
                        <PopperItem
                          onClick={discardAndCallNext(() => {
                            setState({
                              activeState: CandidateFormStep2SubKeys.LANGUAGE,
                              readonly: item?.realData.originalId !== null,
                              activeAction: 'edit',
                              language: item.realData,
                            });
                          })}
                          iconName="pen"
                          label={t('edit')}
                        />
                        <PopperItem
                          onClick={deleteWithConfirm(() =>
                            Language.Remove({
                              languageId: item.realData.id,
                            })
                          )}
                          iconName="trash"
                          label={t('remove')}
                        />
                      </PopperMenu>
                    ) : null
                  }
                />
              );
            })}
          </Flex>
        ) : null}

        {activeAction === 'add' &&
        activeState === CandidateFormStep2SubKeys.LANGUAGE ? (
          languageForm
        ) : (
          <IconButton
            className={classes.addBtn}
            type="far"
            size="md15"
            name="plus"
            colorSchema="backgroundIconSecondary"
            onClick={discardAndCallNext(() => {
              setState({
                activeState: CandidateFormStep2SubKeys.LANGUAGE,
                activeAction: 'add',
                language: undefined,
              });
            })}
          />
        )}
      </Flex>
    </ModalTransitionWrapper>
  );
};

export default GeneralInfoBody;
