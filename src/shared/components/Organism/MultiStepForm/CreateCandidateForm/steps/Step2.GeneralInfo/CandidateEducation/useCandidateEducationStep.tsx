import {
  getSchoolValidationSchema,
  transformSchool,
} from '@shared/components/Organism/MultiStepForm/ProfileSections/sections/useSchool';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import { useMemo } from 'react';
import type {
  CallbackParams,
  SingleStepItem,
} from 'shared/components/Organism/MultiStepForm/MultiStepForm';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import { useDiscardConfirm } from '@shared/components/Organism/MultiStepForm/ProfileSections/sections/utils/useDiscardConfirm';
import Observer from '@shared/components/atoms/Observer';
import {
  CandidateFormStep2SubKeys,
  CandidateFormStepKeys,
} from '../../../constants';
import { useCandidateModalContext } from '../../../CreateCandidateModalProvider';
import classes from '../../../CreateCandidate.module.scss';
import { useGeneralInfoStore } from '../GeneralInfo.store';
import CandidateEducationBody from './CandidateEducation.body';

export default function useCandidateEducationStep(): SingleStepItem {
  const { t } = useTranslation();
  const { candidate, Education } = useCandidateModalContext();
  const { resetState, activeState, education, activeAction, readonly } =
    useGeneralInfoStore();
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });
  const { openConfirm } = useDiscardConfirm();

  const handleClickDiscard = ({
    dirty,
    resetForm,
  }: Pick<CallbackParams, 'dirty' | 'resetForm'>) => {
    if (dirty) {
      openConfirm(() => {
        resetForm?.();
        resetState();
      });
    } else {
      resetState();
    }
  };

  const deleteWithConfirm = (func: Function) => () => {
    openConfirmDialog({
      title: t('delete'),
      message: t('delete_item_confirmation'),
      confirmButtonText: t('delete'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func,
        onSuccess: resetState,
      },
    });
  };

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    dirty,
    resetForm,
  }) => ({
    title: activeAction === 'edit' ? t('edit_education') : t('add_education'),
    hideBack: false,
    backButtonProps: {
      onClick: () => {
        handleClickDiscard({ dirty, resetForm });
      },
    },
    noCloseButton: true,
  });

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] = (
    props
  ) => ({
    stepKey: CandidateFormStep2SubKeys.EDUCATION,
    iconProps: {
      name: 'projects-light',
      type: 'fal',
    },
  });

  const getValidationSchema: MultiStepFormStepProps['getValidationSchema'] = (
    props
  ) => getSchoolValidationSchema();

  const renderBody: MultiStepFormStepProps['renderBody'] = (props) => (
    <CandidateEducationBody {...props} values={initialValues} />
  );

  const renderFooter: MultiStepFormStepProps['renderFooter'] = ({
    validateForm,
    step,
    dirty,
    resetForm,
  }) => (
    <Flex className={classes.footer}>
      <Observer validateForm={validateForm} step={step} />
      {activeAction === 'add' ? (
        <Button
          fullWidth
          label={t('discard')}
          schema="gray"
          onClick={() => handleClickDiscard({ dirty, resetForm })}
        />
      ) : (
        <Button
          fullWidth
          label={t('delete')}
          disabled={readonly}
          schema="gray"
          onClick={deleteWithConfirm(() =>
            Education.Remove({ educationId: education.id })
          )}
        />
      )}
      <SubmitButton
        fullWidth
        disabled={readonly}
        label={activeAction === 'add' ? t('add') : t('update')}
        schema="primary-blue"
      />
    </Flex>
  );

  function apiFunc(data: any) {
    return activeAction === 'add'
      ? Education.Add({ candidateId: candidate?.id, body: data })
      : Education.Edit({ educationId: education.id, body: data });
  }

  const initialValues = useMemo(() => {
    if (activeState === CandidateFormStep2SubKeys.EDUCATION) return education;
    return {};
  }, [activeState, education]);

  function onSuccess(d: unknown) {
    resetState();
  }

  return {
    apiFunc,
    onSuccess,
    initialValues,
    transform: transformSchool,
    stepKey: CandidateFormStepKeys.GENERAL,
    getHeaderProps,
    getStepHeaderProps,
    renderBody,
    renderFooter,
    getValidationSchema,
    enableReinitialize: true,
  };
}
