import {
  getExperienceValidationSchema,
  transformExperience,
} from '@shared/components/Organism/MultiStepForm/ProfileSections/sections/useExperience';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import type {
  CallbackParams,
  SingleStepItem,
} from 'shared/components/Organism/MultiStepForm/MultiStepForm';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useMemo } from 'react';
import useOpenConfirm from '@shared/uikit/Confirmation/useOpenConfirm';
import { useDiscardConfirm } from '@shared/components/Organism/MultiStepForm/ProfileSections/sections/utils/useDiscardConfirm';
import Observer from '@shared/components/atoms/Observer';
import {
  CandidateFormStep2SubKeys,
  CandidateFormStepKeys,
} from '../../../constants';
import { useCandidateModalContext } from '../../../CreateCandidateModalProvider';
import classes from '../../../CreateCandidate.module.scss';
import { useGeneralInfoStore } from '../GeneralInfo.store';
import CandidateExperienceBody from './CandidateExperience.body';

export default function useCandidateExperienceStep(): SingleStepItem {
  const { t } = useTranslation();
  const { candidate, Experience } = useCandidateModalContext();
  const { resetState, activeState, experience, activeAction, readonly } =
    useGeneralInfoStore();
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
  });
  const { openConfirm } = useDiscardConfirm();

  const handleClickDiscard = ({
    dirty,
    resetForm,
  }: Pick<CallbackParams, 'dirty' | 'resetForm'>) => {
    if (dirty) {
      openConfirm(() => {
        resetForm?.();
        resetState();
      });
    } else {
      resetState();
    }
  };

  const deleteWithConfirm = (func: Function) => () => {
    openConfirmDialog({
      title: t('delete'),
      message: t('delete_item_confirmation'),
      confirmButtonText: t('delete'),
      cancelButtonText: t('cancel'),
      isAjaxCall: true,
      apiProps: {
        func,
        onSuccess: resetState,
      },
    });
  };

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    dirty,
    resetForm,
  }) => ({
    title: activeAction === 'edit' ? t('edit_experience') : t('add_experience'),
    hideBack: false,
    backButtonProps: {
      onClick: () => {
        handleClickDiscard({ dirty, resetForm });
      },
    },
    noCloseButton: true,
  });

  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] = (
    props
  ) => ({
    stepKey: CandidateFormStep2SubKeys.EXPERIENCE,
    iconProps: {
      name: 'projects-light',
      type: 'fal',
    },
  });

  const getValidationSchema: MultiStepFormStepProps['getValidationSchema'] = (
    props
  ) => getExperienceValidationSchema();

  const renderBody: MultiStepFormStepProps['renderBody'] = (props) => (
    <CandidateExperienceBody {...props} />
  );

  const renderFooter: MultiStepFormStepProps['renderFooter'] = ({
    validateForm,
    step,
    dirty,
    resetForm,
  }) => (
    <Flex className={classes.footer}>
      <Observer validateForm={validateForm} step={step} />
      {activeAction === 'add' ? (
        <Button
          fullWidth
          label={t('discard')}
          schema="gray"
          onClick={() => handleClickDiscard({ dirty, resetForm })}
        />
      ) : (
        <Button
          fullWidth
          label={t('delete')}
          schema="gray"
          disabled={readonly}
          onClick={deleteWithConfirm(() =>
            Experience.Remove({ experienceId: experience.id })
          )}
        />
      )}
      <SubmitButton
        fullWidth
        disabled={readonly}
        label={activeAction === 'add' ? t('add') : t('update')}
        schema="primary-blue"
      />
    </Flex>
  );

  function apiFunc(data: any) {
    if (readonly) return null;
    return activeAction === 'add'
      ? Experience.Add({ candidateId: candidate?.id, body: data })
      : Experience.Edit({ experienceId: experience.id, body: data });
  }

  const initialValues = useMemo(() => {
    if (activeState === CandidateFormStep2SubKeys.EXPERIENCE) return experience;
    return {};
  }, [activeState, experience]);

  function onSuccess() {
    resetState();
  }

  return {
    apiFunc,
    onSuccess,
    initialValues,
    transform: transformExperience,
    stepKey: CandidateFormStepKeys.GENERAL,
    getHeaderProps,
    getStepHeaderProps,
    renderBody,
    renderFooter,
    getValidationSchema,
    enableReinitialize: true,
  };
}
