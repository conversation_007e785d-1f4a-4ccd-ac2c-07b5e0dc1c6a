import { closeMultiStepForm } from 'shared/hooks/useMultiStepForm';
import { QueryKeys } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useInfiniteQuery from 'shared/utils/hooks/useInfiniteQuery';
import type { Dispatch, SetStateAction } from 'react';
import { useState } from 'react';
import { getJobsForLinking } from 'shared/utils/api/jobs';
import type { IJobApi } from 'shared/types/job';
import { getCandidatesList } from '@shared/utils/api/candidates';
import LinkJobModalFooter from './LinkJobModal/LinkJobModalFooter';
import LinkJobModalBody from './LinkJobModal/LinkJobModalBody';
import EmptySearchResult from '../../EmptySearchResult';

export default function useLinkJobForm(
  target: 'project' | 'candidate' | 'job'
) {
  const { t } = useTranslation();

  const [text, setText] = useState('');

  const {
    data: listData,
    fetchNextPage,
    isFetchingNextPage,
    isLoading,
    hasNextPage,
  } = useInfiniteQuery<IJobApi>(
    target === 'job'
      ? [QueryKeys.suggestCandidates, text]
      : [QueryKeys.getJobsList, text],
    {
      func: (props: { page: number }) => {
        if (target === 'job')
          return getCandidatesList({
            params: { text, page: props.page ?? 0, onlyCandidates: true },
          });
        return getJobsForLinking({
          params: { text, page: props.page ?? 0, status: 'OPEN' },
        });
      },
      size: 20,
    }
  );

  const getHeaderProps = ({
    setStep,
    step,
  }: {
    setStep: Dispatch<SetStateAction<number>>;
    step: number;
  }) => ({
    title: t(titleText[target]),
    hideBack: step === 0,
    backButtonProps: {
      onClick: () => {
        if (step === 0) closeMultiStepForm('linkJobForm');
        setStep((prev) => prev - 1);
      },
    },
    noCloseButton: step !== 0,
    helper: helperText[target] ? t(helperText[target]) : undefined,
  });
  const renderFooter = (props: any) => {
    if (!isLoading && !listData?.length && !!text?.length) return undefined;
    return (
      <LinkJobModalFooter
        {...props}
        isLoading={isLoading}
        hasJobs={!!listData?.length}
        isSearching={!!text?.length}
      />
    );
  };
  const renderBody = ({ step }: { step: number }) => {
    if (!isLoading && !listData?.length && !text?.length)
      return (
        <EmptySearchResult
          title={t(emptyTitleText[target])}
          sectionMessage={t(emptyDescriptionText[target])}
          className="flex-1"
        />
      );

    return (
      <LinkJobModalBody
        step={step}
        listData={listData}
        fetchNextPage={fetchNextPage}
        isFetchingNextPage={isFetchingNextPage}
        isLoading={isLoading}
        onChangeSearch={setText}
        hasNextPage={hasNextPage}
      />
    );
  };
  const data: Array<any> = [
    {
      stepKey: 'link_job',
      getHeaderProps,
      renderFooter,
      renderBody,
    },
  ];

  return data;
}

const helperText = {
  project: 'link_job_description',
  candidate: 'candidate_link_job_desc',
  job: 'link_candidate_description',
};
const titleText = {
  project: 'link_job',
  candidate: 'link_job',
  job: 'link_candidate',
};
const emptyTitleText = {
  project: 'no_jobs_found',
  candidate: 'no_jobs_found',
  job: 'no_candidate_found',
};
const emptyDescriptionText = {
  project: 'no_job_to_link',
  candidate: 'no_job_to_link',
  job: 'no_candidate_to_link',
};
