import React, { useMemo, type ComponentProps } from 'react';
import { QueryKeys } from 'shared/utils/constants';
import useTranslation from 'shared/utils/hooks/useTranslation';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import type Icon from '@shared/uikit/Icon';
import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import type { IResume } from '@shared/types/job';
import jobsApi from 'shared/utils/api/jobs';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import JobApplicationFooter from './partials/JobApplicationFooter';
import JobApplicationBody from './partials/JobApplicationBody';

const useJobApplicationMultiStepForm = () => {
  const { t } = useTranslation();
  const { data: job } = useMultiStepFormState('jobApplication');
  const { authUser } = useGetAppObject();
  const resumeKey = [QueryKeys.getResume, authUser?.id];
  const { data: resumeData, isLoading } = useReactQuery<IResume>({
    action: {
      apiFunc: jobsApi.getResume,
      key: resumeKey,
      params: { userId: authUser?.id },
    },
    config: {
      enabled: !!authUser?.id,
    } as any,
  });

  const headerNavigationData = useMemo(() => {
    if (job?.questions.length) {
      return headerNavigationGeneralData.reduce(
        (acc, curr, i) => {
          if (i === 2) return [...acc, headerNavigationQuestionData, curr];
          return [...acc, curr];
        },
        [] as {
          title: string;
          iconProps?: ComponentProps<typeof Icon>;
          pathColor?: string;
          helper?: string;
        }[]
      );
    }
    return headerNavigationGeneralData;
  }, [job]);

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    setStep,
    step,
  }) => ({
    title: t('job_application'),
    hideBack: step === 0,
    backButtonProps: {
      onClick: () => {
        if (step === 0) return closeMultiStepForm('jobApplication');
        setStep((prev) => prev - 1);
        return null;
      },
    },
    noCloseButton: step !== 0,
  });
  const getStepHeaderProps: MultiStepFormStepProps['getStepHeaderProps'] = ({
    step,
  }) => {
    const data = headerNavigationData[step];
    return {
      ...data,
      title: t(data.title),
    };
  };
  const renderBody: MultiStepFormStepProps['renderBody'] = (props) => (
    <JobApplicationBody
      {...props}
      resumeData={resumeData}
      stepKey={headerNavigationData[props.step]?.title}
    />
  );
  const renderFooter: MultiStepFormStepProps['renderFooter'] = (props) => {
    if (isLoading) return null;
    return (
      <JobApplicationFooter
        {...props}
        stepKey={headerNavigationData[props.step]?.title}
      />
    );
  };

  const stepKeyData = (index: number) => headerNavigationData[index].title;
  const data: Array<MultiStepFormStepProps> = new Array(
    headerNavigationData.length
  )
    .fill(1)
    .map((_, i) => ({
      stepKey: stepKeyData(i),
      getHeaderProps,
      getStepHeaderProps,
      renderFooter,
      renderBody,
    }));

  return data;
};

export default useJobApplicationMultiStepForm;

const headerNavigationGeneralData: {
  title: string;
  iconProps?: ComponentProps<typeof Icon>;
  pathColor?: string;
  helper?: string;
}[] = [
  {
    title: 'personal_info',
    iconProps: {
      name: 'info-circle',
      type: 'fal',
    },
  },
  {
    title: 'resume',
    iconProps: {
      name: 'file-user',
      type: 'fal',
    },
  },
  {
    title: 'preview',
    iconProps: {
      name: 'contact',
      type: 'fal',
    },
  },
];

const headerNavigationQuestionData: {
  title: string;
  iconProps?: ComponentProps<typeof Icon>;
  pathColor?: string;
  helper?: string;
} = {
  title: 'questions',
  iconProps: {
    name: 'question',
    type: 'fal',
  },
};
