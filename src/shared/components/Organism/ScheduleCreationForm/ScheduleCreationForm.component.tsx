import { useMemo } from 'react';
import Form from 'shared/uikit/Form';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import useScheduleForm from './partials/useScheduleForm';
import ScheduleCreationFormContent from './ScheduleCreationForm.content';
import { AttendeePickerPanel } from './AttendeePickerPanel';

const ScheduleCreationForm = () => {
  const {
    initialValues,
    apiFunc,
    transform,
    onSuccessHandler,
    validationSchema,
  } = useScheduleForm();

  const { state } = useSchedulesUrlState();
  const { isInAttendeeSelection, schedulesEventType } = useMemo(
    () => ({
      isInAttendeeSelection:
        state?.scheduleEventsPanelData?.isInAttendeeSelection,
      schedulesEventType: state?.scheduleEventsPanelData?.schedulesEventType,
    }),
    [state]
  );
  if (!schedulesEventType) return null;
  return (
    <Form
      key={schedulesEventType}
      initialValues={initialValues}
      apiFunc={apiFunc}
      onSuccess={onSuccessHandler}
      transform={transform}
      enableReinitialize
      validationSchema={validationSchema}
    >
      <ScheduleCreationFormContent />
      {isInAttendeeSelection && (
        <AttendeePickerPanel modalProps={{ wide: isBusinessApp }} />
      )}
    </Form>
  );
};

export default ScheduleCreationForm;
