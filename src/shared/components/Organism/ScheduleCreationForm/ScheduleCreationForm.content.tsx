import { useEffect, useMemo } from 'react';
import dayjs from 'dayjs';
import { useFormikContext } from 'formik';
import { motion } from 'framer-motion';
import { useAuthState } from '@shared/contexts/Auth/auth.provider';
import Button from 'shared/uikit/Button';
import DynamicFormBuilder from 'shared/uikit/Form/DynamicFormBuilder';
import Flex from 'shared/uikit/Flex';
import InfoCard from 'shared/uikit/InfoCard';
import SubmitButton from 'shared/uikit/Form/SubmitButton';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalFooter from 'shared/uikit/Modal/ModalFooter';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog';
import { schedulesDb, schedulesEventTypes } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Tabs from 'shared/components/Organism/Tabs';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import useSchedulesEvent from 'shared/hooks/useSchedulesEvent';
import { Time } from 'shared/utils/Time';
import type { CreatableSchedulesEventTypes } from 'shared/types/schedules/schedules';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import useBackToModal from '@shared/hooks/useBackToModal';
import { isBusinessApp } from '@shared/utils/getAppEnv';
import { meetingCreatorAttendeePermissions } from '@shared/constants/schedules';
import {
  setTemporaryEvent,
  useSchedulesState,
} from '@shared/stores/schedulesStore';
import { selectTemporaryEvent } from '@shared/stores/schedulesStore/schedulesStore.selectors';
import useHistory from '@shared/utils/hooks/useHistory';
import Skeleton from '@shared/uikit/Skeleton';
import useScheduleFormFields from './partials/useScheduleFormFields';
import classes from './ScheduleCreationForm.content.module.scss';

const ScheduleCreationFormContent = () => {
  const { t } = useTranslation();
  const history = useHistory();
  const isLoggedIn = useAuthState('isLoggedIn');
  const {
    schedulesEventType,
    isCreationMode,
    queryResult,
    backHandler,
    creationInitialData,
    event,
  } = useSchedulesEvent<CreatableSchedulesEventTypes>();
  const temporaryEvent = useSchedulesState(selectTemporaryEvent);
  const formContext = useFormikContext<any>();
  const formFields = useScheduleFormFields(
    (event?.schedulesEventType || schedulesEventType) ===
      schedulesEventTypes.MEETING
      ? event?.permissions || { ...meetingCreatorAttendeePermissions }
      : undefined,
    creationInitialData?.targetAttendee
  );
  const { setScheduleEventsPanelData } = useSchedulesUrlState();
  const { backToModal, hasBackModal } = useBackToModal('createEntityPanel');
  const { openConfirmDialog } = useOpenConfirm({
    variant: 'wideRightSideModal',
    styles: {
      container: isBusinessApp
        ? classes.wideConfirmationModalContent
        : classes.confirmationModalContent,
      wrapper: isBusinessApp
        ? classes.wideConfirmationModalContent
        : classes.confirmationModalContent,
    },
  });

  const closeHandler = () => {
    if (formContext.dirty) {
      openConfirmDialog({
        title: t('confirm_title'),
        message: t('confirm_desc'),
        cancelButtonText: t('confirm_cancel'),
        confirmButtonText: t('confirm_ok'),
        cancelCallback: history.back,
        isReverse: true,
      });
    } else history.back();
  };
  const onBackHandler = () => {
    if (isCreationMode) {
      closeHandler();
    } else {
      backToModal();
      backHandler();
    }
  };

  const groups = useMemo(
    () => formFields[schedulesEventType],
    [formFields, schedulesEventType]
  );
  const startDate = formContext?.values?.startDate;
  const startTime = formContext?.values?.startTime;
  const endDate = formContext?.values?.endDate;
  const endTime = formContext?.values?.endTime;

  const tabs = useMemo(
    () => [
      {
        path: ScheduleEventTypes.MEETING,
        title: t('meeting'),
        headerTitle: isCreationMode ? t('create_meeting') : t('edit_meeting'),
      } as const,
      {
        path: ScheduleEventTypes.REMINDER,
        title: t('reminder'),
        headerTitle: isCreationMode ? t('create_reminder') : t('edit_reminder'),
      } as const,
      {
        path: ScheduleEventTypes.TASK,
        title: t('task'),
        headerTitle: isCreationMode ? t('create_task') : t('edit_task'),
      } as const,
    ],
    [t, isCreationMode]
  );
  const title = tabs?.find((i) => i.path === schedulesEventType)?.headerTitle;

  const setActiveTab = (tab: CreatableSchedulesEventTypes) => {
    if (schedulesEventType && schedulesEventType !== tab)
      setScheduleEventsPanelData(
        {
          isInCrEdit: true,
          schedulesEventType: tab,
          creationInitialData,
        },
        { replace: true }
      );
  };

  const isValidTime = (text: string) =>
    schedulesDb.timeOptions?.find((option) => option.label.includes(text));

  useEffect(() => {
    if (
      !startDate ||
      !startTime ||
      !isValidTime(startTime?.value) ||
      !isCreationMode ||
      !temporaryEvent
    )
      return;
    const start = Time.getUTCTimeByFormDateAndTime(startDate, startTime?.value);
    const end =
      endDate && endTime?.value && isValidTime(endTime?.value)
        ? Time.getUTCTimeByFormDateAndTime(endDate, endTime?.value)
        : start.add(1, 'hour');

    if (end && !dayjs(end).isAfter(start)) {
      return;
    }
    setTemporaryEvent({
      ...temporaryEvent,
      startTime: start,
      endTime: end,
    });
  }, [isCreationMode, startDate, startTime, endDate, endTime, t]);

  if (!schedulesEventType) return null;

  const isEventInThePast: boolean =
    startDate &&
    startTime?.value &&
    isValidTime(startTime?.value) &&
    Time.isBeforeNow(
      Time.getUTCTimeByFormDateAndTime(startDate, startTime?.value)
    );

  const warningBoxTitle = {
    [ScheduleEventTypes.MEETING]: t('this_meeting_set_past'),
    [ScheduleEventTypes.REMINDER]: t('this_reminder_set_past'),
    [ScheduleEventTypes.TASK]: t('this_task_set_past'),
  };

  return (
    <FixedRightSideModalDialog
      wide={isBusinessApp}
      onClose={closeHandler}
      onBack={onBackHandler}
      onClickOutside={closeHandler}
      fullBackdrop={!isLoggedIn}
    >
      <ModalHeaderSimple
        closeButtonProps={{
          className: !isCreationMode ? classes.hideBack : classes.showBack,
        }}
        backButtonProps={{
          onClick:
            isCreationMode && !hasBackModal ? closeHandler : onBackHandler,
          className:
            isCreationMode && !hasBackModal
              ? classes.hideBack
              : classes.showBack,
        }}
        hideBack={!hasBackModal}
        noCloseButton={hasBackModal}
        visibleHeaderDivider
        title={title}
        className={classes.largeModalHeader}
      />
      {isCreationMode && !isBusinessApp && !hasBackModal && (
        <Tabs
          onChangeTab={setActiveTab}
          activePath={schedulesEventType}
          tabs={tabs}
          confirmBeforeChange={formContext.dirty}
          openConfirmDialog={openConfirmDialog}
        />
      )}
      {queryResult.isLoading ? (
        <Skeleton />
      ) : (
        <>
          <ModalBody className={classes.modalBody}>
            <motion.div
              id="modalMotion"
              initial="visible"
              animate="hidden"
              variants={{
                visible: { originX: 0, opacity: 0, scale: 0.95 },
                hidden: { originX: 0.5, opacity: 1, scale: 1 },
              }}
              transition={{ duration: 0.2 }}
            >
              {isEventInThePast && schedulesEventType && (
                <InfoCard
                  classNames={{
                    wrapper: classes.warningBox,
                  }}
                >
                  {warningBoxTitle[schedulesEventType]}
                </InfoCard>
              )}
              <DynamicFormBuilder groups={groups} />
            </motion.div>
          </ModalBody>

          <ModalFooter className={classes.footer}>
            <Button
              onClick={closeHandler}
              fullWidth
              schema="semi-transparent3"
              label={t('discard')}
            />
            <Flex className={classes.divider} />
            <SubmitButton
              fullWidth
              label={isCreationMode ? t('create') : t('update')}
            />
          </ModalFooter>
        </>
      )}
    </FixedRightSideModalDialog>
  );
};

export default ScheduleCreationFormContent;
