import useDeleteScheduleEvent from 'shared/hooks/useDeleteScheduleEvent';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import type { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { useCallback } from 'react';

interface UserEventActionsProps {
  eventTitle: string;
  eventId: string;
  eventType: ScheduleEventTypes;
}
export const useEventActions = ({
  eventTitle,
  eventId,
  eventType,
}: UserEventActionsProps) => {
  const {
    state: { scheduleEventsPanelData: scheduleCreationModalData },
    setScheduleEventsPanelData: setScheduleCreationModalData,
  } = useSchedulesUrlState();

  const { deleteHandler: deleteScheduleEvent, onDeletionSuccessHandler } =
    useDeleteScheduleEvent(eventType, eventId || '');

  const editHandler = useCallback(() => {
    setScheduleCreationModalData({
      ...scheduleCreationModalData,
      isInCrEdit: true,
      schedulesEventType: eventType,
      eventId,
    });
  }, [
    eventId,
    scheduleCreationModalData,
    eventType,
    setScheduleCreationModalData,
  ]);

  const deleteHandler = useCallback(() => {
    if (eventId && eventTitle)
      deleteScheduleEvent({
        eventId,
        eventTitle,
        schedulesEventType: eventType,
        onSuccess: onDeletionSuccessHandler,
      });
  }, [
    deleteScheduleEvent,
    eventId,
    onDeletionSuccessHandler,
    eventType,
    eventTitle,
  ]);

  return { editHandler, deleteHandler };
};
