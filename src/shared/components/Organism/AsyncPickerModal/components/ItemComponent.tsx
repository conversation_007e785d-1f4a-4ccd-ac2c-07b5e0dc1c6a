import AvatarCard from '@shared/uikit/AvatarCard';
import Flex from '@shared/uikit/Flex';
import type { IconName } from '@shared/uikit/Icon/types';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import type { PropsWithChildren } from 'react';
import { memo } from 'react';

interface Props {
  title: string;
  subTitle: string;
  image?: string;
  subTitleIcon?: IconName;
  onClick?: VoidFunction;
}

function ItemComponent(props: PropsWithChildren<Props>) {
  const { title, subTitle, image, subTitleIcon, onClick, children } = props;
  return (
    <AvatarCard
      avatarProps={{
        imgSrc: image,
        bordered: false,
        isCompany: true,
      }}
      onClick={onClick}
      data={{
        title,
        subTitle: (
          <Flex className="!flex-row items-center gap-4 mt-4">
            {subTitleIcon && (
              <Icon
                name={subTitleIcon}
                type="far"
                size={16}
                color="secondaryDisabledText"
              />
            )}
            <Typography color="secondaryDisabledText" size={14} height={16}>
              {subTitle}
            </Typography>
          </Flex>
        ),
      }}
      action={<Flex className="ml-auto">{children}</Flex>}
    />
  );
}

export default memo(ItemComponent);
