import React, { useState, type ReactNode } from 'react';
import {
  useGlobalDispatch,
  useGlobalState,
} from '@shared/contexts/Global/global.provider';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import Flex from '@shared/uikit/Flex';
import AutoComplete from '@shared/uikit/AutoComplete';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { IconName } from '@shared/uikit/Icon/types';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import MenuItem from '@shared/uikit/MenuItem';
import IconButton from '@shared/uikit/Button/IconButton';
import useMedia from '@shared/uikit/utils/useMedia';
import type { PipelineInfo } from '@shared/types/pipelineProps';
import classes from './AutomationModal.module.scss';

interface StageOption {
  value: string;
  label: string;
  icon: ReactNode;
}

const AutomationDrawer: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useGlobalDispatch();
  const { isMoreThanTablet } = useMedia();
  const { data } = useGlobalState('automationModal') as {
    data: PipelineInfo | undefined;
  };

  const stageOptions: StageOption[] = [
    {
      value: 'review',
      label: t('Review'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          variant="rectangle"
          iconProps={{ color: 'trench' }}
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'interview',
      label: t('Interview'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'error' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'hiring',
      label: t('Hiring'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'pendingOrange' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'offered',
      label: t('Offered'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'success' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
  ];

  const [selectedStage, setSelectedStage] = useState<StageOption>(
    stageOptions.find(
      (opt) => opt.value.toLocaleLowerCase() === data?.type.toLocaleLowerCase()
    ) || stageOptions[0]
  );

  const handleClose = () => {
    dispatch({ type: 'TOGGLE_AUTOMATION_MODAL', payload: { open: false } });
  };

  const handleAutoMoveClick = () => {
    dispatch({ type: 'TOGGLE_AUTOMATION_MODAL', payload: { open: false } });
    dispatch({ type: 'TOGGLE_AUTO_MOVE_MODAL', payload: { open: true, data } });
  };

  const automationItems = [
    {
      icon: 'envelope' as IconName,
      title: t('auto_reply'),
      subtitle: t('c_t_t_r_t_y_c_f'),
    },
    {
      icon: 'meeting' as IconName,
      title: t('auto_interview'),
      subtitle: t('s_i_a_b_o_t_s_w_c'),
    },
    {
      icon: 'assessment' as IconName,
      title: t('auto_assessment'),
      subtitle: t('s_n_t_a_t_q_o_t_a'),
    },
    {
      icon: 'user-plus-s' as IconName,
      title: t('auto_move'),
      subtitle: t('m_c_t_o_o_b_s_q_s_s_a'),
      onClick: handleAutoMoveClick,
    },
    {
      icon: 'user-times-s' as IconName,
      title: t('auto_reject'),
      subtitle: t('r_c_b_o_f_q_s_s_a'),
    },
    {
      icon: 'note' as IconName,
      title: t('auto_note'),
      subtitle: t('w_n_o_c_a'),
    },
    {
      icon: 'checklist' as IconName,
      title: t('auto_todo'),
      subtitle: t('w_t_o_c_a_f_t_y'),
    },
  ];

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
    >
      <ModalHeaderSimple
        title={t('automation')}
        helper={t('s_c_b_o_t_c_n_a')}
      />
      <ModalBody className={classes.modalBody}>
        <Flex className={classes.content}>
          <Flex className={classes.stageSelectContainer}>
            <AutoComplete
              editable={false}
              visibleRightIcon
              variant="simple"
              value={selectedStage}
              onChangeInput={(value: any) => {
                const option = stageOptions.find(
                  (opt) => opt.value === value.value
                );
                if (option) {
                  setSelectedStage(option);
                }
              }}
              leftIcon={
                <Flex flexDir="row" alignItems="center" className="p-4">
                  {selectedStage.icon}
                </Flex>
              }
              inputWrapClassName={classes.inputWrap}
              options={stageOptions}
              rightIconClassName={classes.rightIcon}
              renderItem={({ item }) => (
                <Flex
                  flexDir="row"
                  alignItems="center"
                  className="w-full h-[40px] gap-10"
                >
                  {item.icon}
                  <Typography size={16}>{item.label}</Typography>
                </Flex>
              )}
              className={classes.stageSelect}
              optionsVariant={isMoreThanTablet ? 'dropdown' : 'bottomsheet'}
              displayName={selectedStage.label}
              onSelect={(item: any) => {
                setSelectedStage(item);
              }}
            />
          </Flex>
          <Flex className={classes.itemsList}>
            {automationItems.map((item, index) => (
              <MenuItem
                key={index}
                className={classes.listItem}
                onClick={item.onClick}
                iconName={item.icon}
                iconType="far"
                iconSize={25}
                iconBoxSize={40}
                title={item.title}
                subTitle={item.subtitle}
                titleVariant="lg"
                rightElement={
                  <Icon
                    color="white"
                    name="chevron-right"
                    type="fas"
                    size={20}
                  />
                }
              />
            ))}
          </Flex>
        </Flex>
      </ModalBody>
    </FixedRightSideModal>
  );
};

export default AutomationDrawer;
