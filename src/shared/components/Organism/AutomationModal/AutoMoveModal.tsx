import React, { useState } from 'react';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import Flex from '@shared/uikit/Flex';
import AutoComplete from '@shared/uikit/AutoComplete';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { IconName } from '@shared/uikit/Icon/types';
import IconButton from '@shared/uikit/Button/IconButton';
import Typography from '@shared/uikit/Typography';
import useMedia from '@shared/uikit/utils/useMedia';
import MenuItem from '@shared/uikit/MenuItem';
import classes from './AutomationModal.module.scss';

interface StageOption {
  value: string;
  label: string;
  icon: ReactNode;
}

const AutoMoveModal: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useGlobalDispatch();
  const { isMoreThanTablet } = useMedia();
  const stageOptions: StageOption[] = [
    {
      value: 'review',
      label: t('Review'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          variant="rectangle"
          iconProps={{ color: 'trench' }}
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'interview',
      label: t('Interview'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'error' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'hiring',
      label: t('Hiring'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'pendingOrange' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
    {
      value: 'offered',
      label: t('Offered'),
      icon: (
        <IconButton
          name="circle-s"
          type="far"
          size="sm18"
          iconProps={{ color: 'success' }}
          variant="rectangle"
          className="w-[32px] p-[5px] bg-darkSecondary_hover rounded-lg justify-center"
        />
      ),
    },
  ];

  const [selectedStage, setSelectedStage] = useState<StageOption>(
    stageOptions[0]
  );

  const handleClose = () => {
    dispatch({ type: 'TOGGLE_AUTO_MOVE_MODAL', payload: { open: false } });
    dispatch({ type: 'TOGGLE_AUTOMATION_MODAL', payload: { open: true } });
  };

  const handleRequirementsClick = () => {
    dispatch({ type: 'TOGGLE_AUTO_MOVE_MODAL', payload: { open: false } });
    dispatch({ type: 'TOGGLE_REQUIREMENTS_MODAL', payload: { open: true } });
  };

  const autoMoveItems = [
    {
      icon: 'assessment' as IconName,
      title: t('auto_assessment'),
      subTitle: t('m_c_t_o_o_b_s_q_s_a_s'),
    },
    {
      icon: 'form' as IconName,
      title: t('requirements'),
      subTitle: t('m_c_t_o_o_b_s_q_s_a'),
      onClick: handleRequirementsClick,
    },
  ];

  return (
    <FixedRightSideModal
      onClose={handleClose}
      onClickOutside={handleClose}
      isOpenAnimation
      wide
    >
      <ModalHeaderSimple title={t('auto_move')} helper={t('m_a_b_o_c_d')} />
      <ModalBody className={classes.modalBody}>
        <Flex className={classes.content}>
          <Flex className={classes.stageSelectContainer}>
            <AutoComplete
              editable={false}
              visibleRightIcon
              variant="simple"
              value={selectedStage}
              onChangeInput={(value: StageOption) => {
                const option = stageOptions.find(
                  (opt) => opt.value === value.value
                );
                if (option) {
                  setSelectedStage(option);
                }
              }}
              leftIcon={
                <Flex flexDir="row" alignItems="center" className="p-4">
                  {selectedStage.icon}
                </Flex>
              }
              inputWrapClassName={classes.inputWrap}
              options={stageOptions}
              rightIconClassName={classes.rightIcon}
              renderItem={({ item }) => (
                <Flex
                  flexDir="row"
                  alignItems="center"
                  className="w-full h-[40px] gap-10"
                >
                  {item.icon}
                  <Typography size={16}>{item.label}</Typography>
                </Flex>
              )}
              className={classes.stageSelect}
              optionsVariant={isMoreThanTablet ? 'dropdown' : 'bottomsheet'}
              displayName={selectedStage.label}
              onSelect={(item: StageOption) => {
                setSelectedStage(item);
              }}
            />
          </Flex>
          <Flex className={classes.itemsList}>
            {autoMoveItems.map((item, index) => (
              <MenuItem
                key={index}
                iconName={item.icon}
                iconType="far"
                iconSize={24}
                title={item.title}
                subTitle={item.subTitle}
                onClick={item.onClick}
                withHover
                actionElement={
                  <IconButton
                    name="chevron-right"
                    type="fas"
                    size="md"
                    colorSchema="transparent"
                    variant="rectangle"
                  />
                }
              />
            ))}
          </Flex>
        </Flex>
      </ModalBody>
    </FixedRightSideModal>
  );
};

export default AutoMoveModal;
