import React from 'react';
import Flex from 'shared/uikit/Flex';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';
import { routeNames } from 'shared/utils/constants/routeNames';
import { schedulesEventTypes } from 'shared/utils/constants/enums';
import useHistory from 'shared/utils/hooks/useHistory';
import Create from 'shared/svg/CreateIcon';
import CreateHighlight from 'shared/svg/CreateHighlight';
import Page from 'shared/svg/PageIcon';
import CreateJob from 'shared/svg/CreateJob';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import { openMultiStepForm } from 'shared/hooks/useMultiStepForm';
import InvitePeopleCreation from 'shared/svg/InvitePeopleCreation';
import { usePrefetchRoutes } from 'shared/hooks/usePrefetchRoutes';
import SvgClub from 'shared/svg/SvgClub';
import SvgNews from 'shared/svg/SvgNews';
import SvgArticle from 'shared/svg/ArticleIcon';
import SvgGroup from 'shared/svg/SvgGroup';
import Meeting from 'shared/svg/Meeting';
import SvgTasks from 'shared/svg/SvgTasks';
import SvgReminders from 'shared/svg/SvgReminders';
import SvgProject from 'shared/svg/SvgProject';
import SvgPipeline from 'shared/svg/SvgPipeline';
import SvgTodos from 'shared/svg/SvgTodos';
import SvgNotes from 'shared/svg/SvgNotes';
import SvgCandidate from 'shared/svg/SvgCandidate';
import SvgAvailability from 'shared/svg/SvgAvailability';
import SvgCampaign from 'shared/svg/SvgCampaign';
import SvgAds from 'shared/svg/SvgAds';
import SvgClient from 'shared/svg/SvgClient';
import SvgSales from 'shared/svg/SvgSales';
import SvgCustomer from 'shared/svg/SvgCustomer';
import SvgService from 'shared/svg/SvgService';
import { appPortalsObject, getPortal } from 'shared/utils/getAppEnv';
import type { CreatableSchedulesEventTypes } from 'shared/types/schedules/schedules';
import CreateEntityPanelItem from '../NineDotPanel/CreateEntityPanel.item';
import getCreateEntityMenuItems from './getCreateEntityMenuItems';
import classes from './CreateEntityPanel.component.module.scss';
import { CandidateFormStepKeys } from '../MultiStepForm/CreateCandidateForm/constants';
import { useOpenAvailabilityModal } from '../AvailabilityModule/partials/useOpenAvailabilityModal';

// Note: When passed, openCustomModal handles the back functionality and though should be called wherever the nineDotsModal context is unmounted.
type Props = {
  onClick: (arg: string) => void;
  Steps: any;
  openCustomModal: () => void;
};

const CreateEntityPanelList = ({
  Steps,
  onClick,
  openCustomModal,
}: Props): React.ReactElement => {
  const { t } = useTranslation();
  const history = useHistory();
  const appDispatch = useGlobalDispatch();
  const { setScheduleEventsPanelData } = useSchedulesUrlState();
  const { openAvailabilityCrEditModal } = useOpenAvailabilityModal();

  usePrefetchRoutes([routeNames.pageCreation, routeNames.jobCreation]);

  const openScheduleForm = async (
    schedulesEventType: CreatableSchedulesEventTypes
  ) => {
    openCustomModal();
    setTimeout(() => {
      setScheduleEventsPanelData({
        schedulesEventType,
        isInCrEdit: true,
      });
    }, 0);
  };

  const navigateToComing = () => {
    onClick(Steps.COMING);
  };

  const menuItems = [
    {
      id: 'post',
      title: t('post'),
      subTitle: t('share_yr_tho_ph_and_vid'),
      image: <Create />,
      onClick: async () => {
        appDispatch({
          type: 'SET_CREATE_POST_MODAL',
          payload: {
            isOpenModal: true,
            currentTab: 'main',
          },
        });
        openCustomModal();
      },
    },
    {
      id: 'article',
      title: t('article'),
      subTitle: t('share_yr_ex_and_pass'),
      image: <SvgArticle />,
      onClick: navigateToComing,
    },
    {
      id: 'pipeline',
      title: t('pipeline'),
      subTitle: t('manage_progress_business_practices'),
      image: <SvgPipeline />,
      onClick: navigateToComing,
    },
    {
      id: 'project',
      title: t('project'),
      subTitle: t('organize_streamline_business_operations'),
      image: <SvgProject />,
      onClick: async () => {
        openMultiStepForm({ formName: 'createProjectForm' });
        openCustomModal();
      },
    },
    {
      id: 'news',
      title: t('news'),
      subTitle: t('inform_audience_latest_happenings'),
      image: <SvgNews />,
      onClick: navigateToComing,
    },
    {
      id: 'service',
      title: t('service'),
      subTitle: t('provide_assis_mee_need_eff'),
      image: <SvgService />,
      onClick: navigateToComing,
    },
    {
      id: 'club',
      title: t('club'),
      subTitle: t('join_communities_build_connections'),
      image: <SvgClub />,
      onClick: navigateToComing,
    },

    {
      id: 'highlight',
      title: t('highlight'),
      subTitle: t('showcase_yr_best_mom_lif'),
      image: <CreateHighlight />,
      onClick: async () => {
        appDispatch({
          type: 'SET_CREATE_POST_MODAL',
          payload: {
            isOpenModal: true,
            currentTab: 'highlight',
          },
        });
        openCustomModal();
      },
    },
    {
      id: 'pages',
      title: t('page'),
      subTitle: t('create_pg_f_y_cmp_ins'),
      image: <Page />,
      onClick: async () => {
        history.push(routeNames.pageCreation);
        openCustomModal();
      },
    },
    {
      id: 'job',
      title: t('job_cap'),
      subTitle: t('recruit_top_tal_f_yr_pr'),
      image: <CreateJob />,
      onClick: async () => {
        if (getPortal() === appPortalsObject.recruiter) {
          openMultiStepForm({ formName: 'createJobForm' });
        } else {
          appDispatch({
            type: 'TOGGLE_CREATE_JOB_MODAL_IN_USER_PROJECT',
            payload: true,
          });
        }
        openCustomModal();
      },
    },

    {
      id: 'group',
      title: t('group'),
      subTitle: t('content_an_engage_with_lik_indiv'),
      image: <SvgGroup />,
      onClick: navigateToComing,
    },
    {
      id: 'candidate',
      title: t('candidate'),
      subTitle: t('evaluate_assess_qualifications'),
      image: <SvgCandidate />,
      onClick: async () => {
        openMultiStepForm({
          formName: 'createCandidateForm',
          stepKey: CandidateFormStepKeys.NEW,
        });
        openCustomModal();
      },
    },
    {
      id: 'meeting',
      title: t('meeting'),
      subTitle: t('facilitate_collaborate_objectives'),
      image: <Meeting />,
      onClick: async () => {
        openScheduleForm(schedulesEventTypes.MEETING);
        openCustomModal();
      },
    },
    {
      id: 'task',
      title: t('task'),
      subTitle: t('organize_tasks_manage_goals'),
      image: <SvgTasks />,
      onClick: () => {
        openScheduleForm(schedulesEventTypes.TASK);
        openCustomModal();
      },
    },
    {
      id: 'reminder',
      title: t('reminder'),
      subTitle: t('set_reminders_stay_on_track'),
      image: <SvgReminders />,
      onClick: () => {
        openScheduleForm(schedulesEventTypes.REMINDER);
        openCustomModal();
      },
    },
    {
      id: 'todo',
      title: t('to_do'),
      subTitle: t('organize_tasks_manage_goals'),
      image: <SvgTodos />,
      onClick: navigateToComing,
    },
    {
      id: 'notes',
      title: t('notes'),
      subTitle: t('capture_ideas_organize_thoughts'),
      image: <SvgNotes />,
      onClick: navigateToComing,
    },

    {
      id: 'availability',
      title: t('availability'),
      subTitle: t('mng_yr_availability_sch_w_e'),
      image: <SvgAvailability />,
      onClick: async () => {
        openAvailabilityCrEditModal();
        openCustomModal();
      },
    },
    {
      id: 'campaign',
      title: t('campaign'),
      subTitle: t('promote_ini_eng_au_wel'),
      image: <SvgCampaign />,
      onClick: navigateToComing,
    },
    {
      id: 'ads',
      title: t('ads'),
      subTitle: t('promote_pro_att_aud_eff'),
      image: <SvgAds />,
      onClick: navigateToComing,
    },
    {
      id: 'client',
      title: t('Client'),
      subTitle: t('support_cus_add_nee_prom'),
      image: <SvgClient />,
      onClick: navigateToComing,
    },
    {
      id: 'sales',
      title: t('sales'),
      subTitle: t('boost_sales_th_ef_stre'),
      image: <SvgSales />,
      onClick: navigateToComing,
    },
    {
      id: 'customer',
      title: t('customer'),
      subTitle: t('support_cus_add_nee_prom'),
      image: <SvgCustomer />,
      onClick: navigateToComing,
    },
    {
      id: 'invite_people',
      title: t('invite'),
      subTitle: t('bring_your_community_unique_ecosystem'),
      image: <InvitePeopleCreation />,
      onClick: async () => {
        openMultiStepForm({ formName: 'invitePeople' });
        openCustomModal();
      },
    },
  ];

  const items = getCreateEntityMenuItems(menuItems);

  return (
    <Flex className={classes.contentWrapper}>
      {items.map(
        ({ id, title, subTitle, image, onClick: clickHandler }: any) => (
          <CreateEntityPanelItem
            key={id}
            onClick={clickHandler}
            image={image}
            title={title}
            subTitle={subTitle}
          />
        )
      )}
    </Flex>
  );
};

export default CreateEntityPanelList;
