import type { PropsWithChildren } from 'react';
import React from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import type { IconName } from 'shared/uikit/Icon/types';
import Icon from 'shared/uikit/Icon';
import OverflowTip from 'shared/uikit/Typography/OverflowTip';
import Typography from 'shared/uikit/Typography';
import type { TypographyProps } from 'shared/uikit/Typography';
import useCssVariables from 'shared/hooks/useCssVariables';
import { TOP_OF_LIST_INFO_CARD } from 'shared/constants/enums';
import classes from './InfoCard.module.scss';

interface Props {
  title?: string | React.ReactNode;
  subTitle?: React.ReactNode;
  avatar?: ReactNode;
  icon?: IconName;
  className?: any;
  direction?: 'column' | 'row';
  color?: 'primaryText' | 'success' | 'warning' | 'brand';
  value?: any;
  border?: boolean;
  disabledHover?: boolean;
  iconSize?: number;
  wrapperClassName?: string;
  iconClassName?: string;
  textWrapperClassName?: string;
  valueProps?: Omit<TypographyProps, 'children'>;
  titleProps?: Omit<TypographyProps, 'children'>;
  onClick?: () => void;
}

const backgroundList = {
  success: 'success_5',
  warning: 'warning_5',
  brand: 'brand_5',
  primaryText: 'backgroundIconSecondary',
};
const InfoCard: React.FC<PropsWithChildren<Props>> = ({
  title,
  subTitle,
  icon,
  avatar,
  className,
  color = 'primaryText',
  value,
  border = false,
  direction = 'row',
  disabledHover,
  iconSize = 20,
  wrapperClassName,
  textWrapperClassName,
  iconClassName,
  valueProps = {},
  titleProps = {},
  children,
  onClick,
}) => {
  const styles = useCssVariables({
    scope: classes.InfoCardWrap,
    variables: {
      direction,
    },
  });
  return (
    <Flex
      className={cnj(
        classes.infoWrapper,
        border && classes.InfoCardRoot,
        className
      )}
      data-name={TOP_OF_LIST_INFO_CARD}
      onClick={onClick}
    >
      {styles}
      <Flex
        className={cnj(
          classes.InfoCardWrap,
          disabledHover && classes.InfoCardWrapNoHover,
          wrapperClassName
        )}
      >
        {icon ? (
          <Flex
            className={cnj(
              classes.iconWrap,
              iconClassName,
              classes[`background-${backgroundList[color]}`]
            )}
          >
            <Icon size={iconSize} name={icon} color={color} type="far" />
          </Flex>
        ) : (
          (avatar ?? null)
        )}
        {(title || subTitle) && (
          <Flex className={cnj(classes.textWrapper, textWrapperClassName)}>
            {typeof title === 'string' ? (
              <Typography
                color={titleProps.color ?? 'colorIconForth2'}
                textAlign={titleProps.textAlign ?? 'left'}
                size={titleProps.size ?? 13}
                font={titleProps.font ?? '500'}
                height={titleProps.height ?? 15}
                {...titleProps}
              >
                {title}
              </Typography>
            ) : (
              title
            )}
            {typeof value === 'string' || subTitle ? (
              <OverflowTip
                size={15}
                color={value ? 'thirdText' : 'colorIconForth2'}
                mt={5}
                className={cnj(classes.subtitle, classes.truncate)}
                height={18}
                {...valueProps}
                isTruncated
                lineNumber={1}
              >
                {value || subTitle}
              </OverflowTip>
            ) : (
              value
            )}
          </Flex>
        )}
        {children}
      </Flex>
    </Flex>
  );
};

export default InfoCard;
