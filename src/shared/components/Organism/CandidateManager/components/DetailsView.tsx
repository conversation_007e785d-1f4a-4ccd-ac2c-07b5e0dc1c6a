import type { FC } from 'react';
import React, { useCallback } from 'react';
import CandidateCard, {
  CandidateCardActions,
  CandidateCardSkeleton,
} from '@shared/components/molecules/CandidateCard';
import SectionLayout from '@shared/components/Organism/Objects/Common/Section.layout';
import { useObjectClicks } from '@shared/hooks/useObjectClicks';
import { RichTextView } from '@shared/uikit/RichText';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Flex from '@shared/uikit/Flex';
import Button from '@shared/uikit/Button';
import EmbededView from '@shared/uikit/EmbededView';
import cleanRepeatedWords from '@shared/utils/toolkit/cleanRepeatedWords';
import { IsManualWrapper } from '@shared/components/molecules/IsManualWrapper';
import { getEmbededDocumentLink } from '@shared/utils/getEmbededDocumentLink';
import classes from '@app/candidates/partials/CandidateDetails.module.scss';
import CardBadge from '@shared/components/molecules/CardBadge';
import Tooltip from '@shared/uikit/Tooltip';
import DateView from '@shared/uikit/DateView';
import Typography from '@shared/uikit/Typography';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import { editCandidateAdditionalInfo } from '@shared/utils/api/candidates';
import HorizontalTagList from '@shared/components/molecules/HorizontalTagList';
import { QueryKeys } from '@shared/utils/constants';
import { useQueryClient } from '@tanstack/react-query';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import { useManagerContext } from '../CandidateManager.context';
import UploadCandidateResume from './FiltersBody/UploadCandidateResume';
import CandidateVsJobComparison from './CandidateVsJobComparison';

interface Props {
  className?: string;
}

export const CandidateManagerDetailsView: FC<Props> = ({ className }) => {
  const { t } = useTranslation();
  const { candidate, selectedSummary } = useManagerContext();
  const { embededSrc } = getEmbededDocumentLink(candidate?.resumeUrl ?? '');
  const { handleTagClick, handleHashtagClick, hoveredHashtag, onHashtagHover } =
    useObjectClicks();
  const appDispatch = useGlobalDispatch();
  const queryClient = useQueryClient();

  const handleOpenManager = useCallback(
    (tab?: CandidateManagerTabkeys) => {
      if (candidate)
        appDispatch({
          type: 'TOGGLE_CANDIDATE_MANAGER',
          payload: {
            isOpen: true,
            tab,
            id: candidate.id,
            enableNavigate: false,
          },
        });
    },
    [appDispatch, candidate]
  );

  const refetch = useCallback(() => {
    queryClient.invalidateQueries({
      queryKey: [QueryKeys.getCandidate, candidate?.id],
      exact: false,
    });
  }, [candidate?.id, queryClient]);

  return (
    <Flex className={className}>
      {candidate ? (
        <CandidateCard
          avatar={candidate?.profile?.croppedImageUrl}
          firstText={candidate?.profile?.fullName}
          secondText={candidate?.profile?.usernameAtSign}
          thirdText={candidate.profile?.occupation?.label}
          fourthText={cleanRepeatedWords(
            candidate?.profile?.location?.title || ''
          )}
          FirstTextWrapper={
            !candidate.profile.username ? IsManualWrapper : undefined
          }
          treeDotMenu={<CandidateCardActions candidate={candidate} />}
        >
          <HorizontalTagList
            tags={candidate.tags}
            title={t('candidate_tags')}
            editable
            onSuccess={refetch}
            apiFunc={(body) =>
              editCandidateAdditionalInfo({ candidateId: candidate.id, body })
            }
          />
          <Flex className={classes.badges}>
            <CardBadge
              value={candidate.notesCount}
              iconsDetails={{ iconName: 'note' }}
              tooltipProps={{
                children: t('notes'),
              }}
              onClick={() => handleOpenManager('notes')}
            />
            <CardBadge
              value={candidate.todosCount}
              iconsDetails={{ iconName: 'checklist' }}
              tooltipProps={{
                children: t('todos'),
              }}
              onClick={() => handleOpenManager('todos')}
            />
            <CardBadge
              value={candidate.meetingsCount}
              iconsDetails={{ iconName: 'meeting' }}
              tooltipProps={{
                children: t('meetings'),
              }}
              onClick={() => handleOpenManager('meetings')}
            />
            {!!candidate.lastModifiedDate && (
              <Flex className="ml-auto">
                <Tooltip
                  trigger={
                    <DateView
                      className={classes.counterDate}
                      value={`${candidate.lastModifiedDate}`}
                    />
                  }
                >
                  <Typography
                    size={14}
                    font="400"
                    height={18}
                    color="tooltipText"
                  >
                    {t('latest_activity')}
                  </Typography>
                </Tooltip>
              </Flex>
            )}
          </Flex>
        </CandidateCard>
      ) : (
        <CandidateCardSkeleton showBadges showTags />
      )}
      {selectedSummary?.type !== 'ORIGINAL_CANDIDATE' && (
        <CandidateVsJobComparison />
      )}
      {candidate?.coverLetter ? (
        <SectionLayout title={t('cover_letter')}>
          <RichTextView
            html={candidate.coverLetter}
            typographyProps={{
              size: 15,
              color: 'thirdText',
              height: 16,
            }}
            showMore
            onMentionClick={handleTagClick}
            onHashtagClick={handleHashtagClick}
            onHashtagHover={onHashtagHover}
            hoveredHashtag={hoveredHashtag}
          />
        </SectionLayout>
      ) : null}
      <SectionLayout
        title={t('resume')}
        classNames={{ childrenWrap: 'min-h-[100px]' }}
        visibleActionButton={!!candidate?.resumeUrl}
        actionButton={
          <Flex flexDir="row" className="gap-4">
            <Button
              href={candidate?.resumeUrl}
              target="_blank"
              label={t('download')}
              leftIcon="download_2"
              leftType="far"
              disabled={!candidate?.resumeUrl}
              schema="gray-semi-transparent"
            />
            <Button
              href={embededSrc}
              target="_blank"
              label={t('view')}
              leftIcon="eye"
              disabled={!embededSrc}
              schema="semi-transparent"
            />
          </Flex>
        }
      >
        {candidate?.resumeUrl ? (
          <EmbededView src={candidate.resumeUrl} className="rounded" />
        ) : candidate ? (
          <UploadCandidateResume candidate={candidate} />
        ) : null}
      </SectionLayout>
      <div className="mb-5" />
    </Flex>
  );
};
