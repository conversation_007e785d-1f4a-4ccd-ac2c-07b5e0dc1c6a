import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import InfoCard from '@shared/components/Organism/Objects/Common/InfoCard';
import Flex from '@shared/uikit/Flex';
import { type IconName } from '@shared/uikit/Icon/types';
import Typography from '@shared/uikit/Typography';
import CandidateVsJobComparisonProgressList from './CandidateVsJobComparisonProgressList';

export type CandidateVsJobComparisonExpectationListProps = {
  expectationsListTitle: string;
  expectationsList: {
    title: string;
    value: string | number;
    icon: IconName;
  }[];
  progressListTitle: string;
  progressList: Skill[];
};

const CandidateVsJobComparisonExpectationList = (
  props: CandidateVsJobComparisonExpectationListProps
) => {
  const {
    expectationsListTitle,
    expectationsList,
    progressListTitle,
    progressList,
  } = props;
  return (
    <CardWrapper classNames={{ container: '!p-0', root: '!w-full' }}>
      <Typography font="500" size={14} mb={4} mt={4} color="muteMidGray">
        {expectationsListTitle}
      </Typography>
      <Flex>
        {expectationsList.map((expectation, index) => (
          <InfoCard
            key={`${expectation.title}-${index}`}
            title={expectation.title}
            subTitle={expectation.value}
            icon={expectation.icon}
            border={false}
            className="w-full"
            titleProps={{
              color: 'muteMidGray',
              font: '500',
              size: 12,
            }}
            valueProps={{
              color: 'smoke',
              font: '400',
              size: 15,
            }}
          />
        ))}
        <CandidateVsJobComparisonProgressList
          progressListTitle={progressListTitle}
          progressList={progressList}
          isLanguageList={false}
        />
      </Flex>
    </CardWrapper>
  );
};

export default CandidateVsJobComparisonExpectationList;
