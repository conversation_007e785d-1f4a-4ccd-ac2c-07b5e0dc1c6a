import type { CandidateFormData } from '@shared/types/candidates';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import cnj from '@shared/uikit/utils/cnj';
import useTranslation from '@shared/utils/hooks/useTranslation';
import useReactInfiniteQuery from '@shared/utils/hooks/useInfiniteQuery';
import {
  getUpcomingMeetings,
  getPastMeetings,
} from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import { useSchedulesCalendar } from '@shared/hooks/useSchedulesCalendar';
import { useCallback, useState } from 'react';
import { MeetingDatetimeType } from '@shared/types/schedules/schedules';
import { dayjs } from '@shared/utils/Time';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import { MeetingCard } from '@shared/components/molecules/MeetingCard';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { FooterWrap } from '../../components/FooterWrap';
import classes from './CandidateMeetingsManager.module.scss';

interface CandidateDocumentsManagerProps {
  candidate: CandidateFormData;
}

type MeetingType = 'upcoming' | 'past';

const cardWrapperClassNames = {
  root: '!p-8 !gap-8 rounded bg-background border border-solid border-techGray_20',
  titleWrapper: '!justify-between',
};

export function CandidateMeetingsManager({
  candidate,
}: CandidateDocumentsManagerProps) {
  const { t } = useTranslation();
  const { openCreateEventWithDate } = useSchedulesCalendar();
  const [activeTab, setActiveTab] = useState<MeetingType>('upcoming');
  const { setScheduleEventsPanelData } = useSchedulesUrlState();

  const infiniteQuery = useReactInfiniteQuery(
    [QueryKeys.candidateMeetings, candidate.id, activeTab],
    {
      func: (props: { pageParam?: number }) =>
        activeTab === 'upcoming'
          ? getUpcomingMeetings({
              params: {
                candidate: candidate.id,
                page: props.pageParam,
                size: 10,
              },
            })
          : getPastMeetings({
              params: {
                candidateId: candidate.id,
                page: props.pageParam,
                size: 10,
              },
            }),
      size: 10,
    },
    {
      enabled: !!candidate.id,
      refetchOnWindowFocus: false,
    }
  );

  const meetings = infiniteQuery.data;

  const handleCreateEvent = useCallback(() => {
    openCreateEventWithDate(
      dayjs().add(1, 'day'),
      {
        schedulesEventType: ScheduleEventTypes.MEETING,
        targetAttendee: {
          ...candidate.profile,
          id: candidate.profile.originalId,
        },
        datetimeType: MeetingDatetimeType.PROVIDE_AVAILABILITY,
      },
      true
    );
  }, [candidate.profile, openCreateEventWithDate]);
  return (
    <>
      <Flex flexDir="row" className={classes.tabsContainer}>
        <Button
          className={cnj(
            classes.tabButton,
            activeTab === 'upcoming' && classes.activeTab
          )}
          label={t('upcoming_meetings')}
          onClick={() => setActiveTab('upcoming')}
        />
        <Button
          className={cnj(
            classes.tabButton,
            activeTab === 'past' && classes.activeTab
          )}
          label={t('past_meetings')}
          onClick={() => setActiveTab('past')}
        />
      </Flex>
      <Flex className={cnj(classes.meetingsContainer, classes.scrollArea)}>
        {infiniteQuery.isLoading ? (
          <Skeleton className={cnj(cardWrapperClassNames.root, 'h-32')} />
        ) : meetings.length > 0 ? (
          meetings.map((meeting) => (
            <MeetingCard
              key={`meeting_${meeting.id}`}
              actionProps={{
                label: t('view_meeting'),
                onClick: () =>
                  setScheduleEventsPanelData({
                    eventId: meeting.id,
                    isFromNotification: true,
                    isInCandidateManager: true,
                    isInCrEdit: false,
                    schedulesEventType: ScheduleEventTypes.MEETING,
                  }),
              }}
              item={{ meeting }}
              cardProps={{
                classNames: { root: classes.jobItem },
              }}
            />
          ))
        ) : (
          <EmptySearchResult
            className={classes.emptyResult}
            title={t('no_meeting_found')}
            sectionMessage={t('no_meeting_found_desc')}
          />
        )}
      </Flex>
      <FooterWrap>
        <Button
          className={classes.createButton}
          label={t('create_meeting')}
          leftIcon="plus"
          leftSize={16}
          onClick={handleCreateEvent}
        />
      </FooterWrap>
    </>
  );
}
