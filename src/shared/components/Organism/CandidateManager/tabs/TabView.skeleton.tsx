import Flex from '@shared/uikit/Flex';
import Skeleton from '@shared/uikit/Skeleton';
import UserInfoSkeleton from '@shared/components/molecules/UserInfo/UserInfoSkeleton';
import CardWrapper from '@shared/components/molecules/CardItem/CardWrapper';
import SkillboardItemSkeleton from '@shared/components/molecules/SkillboardItem/SkillboardItem.skeleton';
import NoteItemSkeleton from '@shared/components/molecules/NoteItem/NoteItemSkeleton';
import TodoItemSkeleton from '@shared/components/molecules/TodoItem/TodoItemSkeleton';
import ReviewItemSkeleton from '@shared/components/molecules/ReviewItem/ReviewItemSkeleton';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import classes from './tab.module.scss';
import { FooterWrap } from '../components/FooterWrap';
import DocumentItemSkeleton from './tab4.documents/DocumentItem.skeleton';

interface TabViewSkeletonProps {
  variant: CandidateManagerTabkeys;
  isReverse?: true;
}

const defaultItemSkeleton = (
  <CardWrapper classNames={{ root: classes.card }}>
    <Skeleton className="!w-50 h-16 rounded" />
    <Skeleton className="!w-full h-40 rounded" />
  </CardWrapper>
);

export default function TabViewSkeleton({
  variant,
  isReverse,
}: TabViewSkeletonProps) {
  const hasUserHeader = ['skillboard'].includes(variant);
  const hasSearchHeader = ['notes', 'todos'].includes(variant);
  const hasTabbedHeader = ['meetings'].includes(variant);
  const hasComposeFooter = ['notes', 'todos', 'reviews'].includes(variant);
  const hasRatingFooter = ['reviews'].includes(variant);
  const hasButtonFooter = ['meetings', 'documents', 'skillboard'].includes(
    variant
  );
  return (
    <>
      {/* start heaer */}
      {hasUserHeader && (
        <Flex className="w-full p-20 !pb-0">
          <UserInfoSkeleton showTitle />
        </Flex>
      )}
      {hasSearchHeader && (
        <Flex className="w-full p-20 !pb-0">
          <Flex flexDir="row" className="gap-20">
            <Skeleton className="rounded-full flex-1 !h-40" />
            <Skeleton className="rounded !h-40 !w-40" />
          </Flex>
        </Flex>
      )}
      {hasTabbedHeader && (
        <Flex className="w-full p-20 !pb-0">
          <Flex flexDir="row" className="gap-10">
            <Skeleton className="rounded-full !h-32 !w-[170px]" />
            <Skeleton className="rounded-full !h-32 !w-[150px]" />
          </Flex>
        </Flex>
      )}
      {/* start body */}
      <Flex
        className={`flex-1 p-20 gap-20 ${isReverse ? '!flex-col-reverse' : '!flex-col'}`}
      >
        {variant === 'notes' && (
          <NoteItemSkeleton
            classNames={{
              root: 'border border-solid !border-techGray_20 bg-background',
              container: '!p-12',
            }}
          />
        )}
        {variant === 'todos' ? (
          <TodoItemSkeleton
            displayCreator
            classNames={{
              root: 'border border-solid !border-techGray_20 bg-background',
              container: '!p-12',
            }}
          />
        ) : null}
        {variant === 'meetings' ? defaultItemSkeleton : null}
        {variant === 'documents' ? (
          <Flex className="gap-8">
            <DocumentItemSkeleton />
            <DocumentItemSkeleton />
            <DocumentItemSkeleton />
          </Flex>
        ) : null}
        {variant === 'reviews' ? (
          <ReviewItemSkeleton
            classNames={{
              root: '!border !border-solid !border-techGray_20',
              container: '!p-12',
            }}
          />
        ) : null}
        {variant === 'skillboard' && (
          <Flex className="gap-12">
            <SkillboardItemSkeleton />
            <SkillboardItemSkeleton />
            <SkillboardItemSkeleton />
          </Flex>
        )}
        {variant === 'threads' ? defaultItemSkeleton : null}
        {variant === 'emails' ? defaultItemSkeleton : null}
        {variant === 'test' ? defaultItemSkeleton : null}
      </Flex>
      {/* start footer */}
      {hasComposeFooter && (
        <FooterWrap className="gap-12">
          {hasRatingFooter && (
            <Flex flexDir="row" className="gap-4">
              <Skeleton className="rounded !h-16 !w-[75px]" />
              <Skeleton className="rounded !h-16 !w-32 ml-4" />
              <Skeleton className="rounded-full !h-22 !w-22 ml-auto" />
              <Skeleton className="rounded-full !h-22 !w-22" />
              <Skeleton className="rounded-full !h-22 !w-22" />
              <Skeleton className="rounded-full !h-22 !w-22" />
              <Skeleton className="rounded-full !h-22 !w-22" />
            </Flex>
          )}
          <Flex flexDir="row" className="gap-10">
            <Skeleton className="rounded-full !w-40 !h-40" />
            <Skeleton className="rounded-full flex-1 !h-40" />
          </Flex>
        </FooterWrap>
      )}
      {hasButtonFooter && (
        <FooterWrap>
          <Skeleton className="rounded !h-[32px] w-full" />
        </FooterWrap>
      )}
    </>
  );
}
