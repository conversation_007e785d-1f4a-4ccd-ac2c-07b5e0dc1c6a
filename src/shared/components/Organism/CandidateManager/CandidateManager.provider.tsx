import { useMemo, useState, type FC, type PropsWithChildren } from 'react';
import * as API from '@shared/utils/api/candidates';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import type { ICandidateSummaryOption } from '@shared/types/candidates';
import type { CandidateManagerTabkeys } from '@shared/types/candidateManager';
import type {
  ActiveFilterKeys,
  CandidateManagerContextValue,
} from './CandidateManager.context';
import CandidateManagerContextProvider from './CandidateManager.context';

interface Props {
  id: string;
  selectedTab?: CandidateManagerTabkeys;
}
const CandidateManagerProvider: FC<PropsWithChildren<Props>> = ({
  children,
  selectedTab: initialSelectedTab = 'notes',
  id: candidateId,
}) => {
  const [activeFilter, setActiveFilter] = useState<ActiveFilterKeys>(undefined);
  const [selectedTab, setSelectedTab] =
    useState<CandidateManagerTabkeys>(initialSelectedTab);
  const [filters, setFilters] = useState<
    CandidateManagerContextValue['filters']
  >({
    notes: {},
    todos: {},
  });
  const [selectedSummary, setSelectedSummary] = useState<
    ICandidateSummaryOption | undefined
  >();

  const { data, isLoading } = useReactQuery({
    action: {
      apiFunc: () =>
        candidateId ? API.getCandidateById(candidateId) : undefined,
      key: [QueryKeys.getCandidate, candidateId],
    },
    config: {
      enabled: !!candidateId,
    },
  });

  const ctxValue = useMemo<CandidateManagerContextValue>(
    () => ({
      isLoading,
      candidate: data,
      filters,
      setFilters,
      activeFilter,
      setActiveFilter,
      selectedTab,
      setSelectedTab,
      isCandidate: data?.isLoboxCandidate,
      selectedSummary,
      setSelectedSummary,
    }),
    [
      data,
      isLoading,
      filters,
      setFilters,
      activeFilter,
      setActiveFilter,
      selectedTab,
      setSelectedTab,
      selectedSummary,
      setSelectedSummary,
    ]
  );

  return (
    <CandidateManagerContextProvider value={ctxValue}>
      {children}
    </CandidateManagerContextProvider>
  );
};

export default CandidateManagerProvider;
