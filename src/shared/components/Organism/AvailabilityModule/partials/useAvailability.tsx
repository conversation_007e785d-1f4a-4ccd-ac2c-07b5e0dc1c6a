import { useCallback, useEffect, useMemo, useState } from 'react';

import { useAuthState } from 'shared/contexts/Auth/auth.provider';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import type {
  BETimesheet,
  Timesheet,
  Timezone,
  UseAvailabilityProps,
} from 'shared/types/preferences/availability.types';
import {
  MeetingChannel,
  type DateType,
} from 'shared/types/schedules/schedules';
import useOpenConfirm from 'shared/uikit/Confirmation/useOpenConfirm';
import useToast from 'shared/uikit/Toast/useToast';
import { getUser } from 'shared/utils/api/user';
import { QueryKeys } from 'shared/utils/constants';
import useReactQuery from 'shared/utils/hooks/useReactQuery';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { dayjs } from 'shared/utils/Time';
import {
  putTimeSheet,
  getPreferences,
  getUserTimesheet,
  deleteTimesheet as deleteTimesheetApi,
} from 'shared/utils/api/schedules';
import type { Dayjs } from 'dayjs';
import { ShareEntities, ShareEntityTab } from '@shared/types/share/entities';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import useGetCurrentTimeZone from '@shared/hooks/api-hook/useGetCurrentTimeZone';
import {
  applyTimezoneShiftOnTimesheet,
  currentTimezoneToTimezone,
  unwrapTimezone,
} from '@shared/utils/normalizers/schedules';
import { useMutation } from '@tanstack/react-query';
import { ConferenceProviderName } from '@shared/components/molecules/EventsIntegration/utils/type';

export const useAvailability = ({
  timesheetId,
  userId,
  rawSelectedDay,
  rawSelectedHour,
}: UseAvailabilityProps = {}) => {
  const { t } = useTranslation();

  const {
    state: { scheduleAvailabilityPanelData },
    setScheduleAvailabilityPanelData,
  } = useSchedulesUrlState();

  const isLoggedIn = useAuthState('isLoggedIn');

  const isBooking = Boolean(userId && timesheetId);

  const {
    data: preferencesData,
    refetch: refetchPreferences,
    isLoading: isLoadingPreferences,
  } = useReactQuery({
    action: {
      key: [QueryKeys.schedulesPreferences],
      apiFunc: getPreferences,
    },
    config: { enabled: isLoggedIn && !userId },
  });

  const {
    data: otherUserData,
    refetch: refetchOtherUser,
    isLoading: isLoadingOtherUserTimesheets,
  } = useReactQuery({
    action: {
      key: [QueryKeys.schedulesPreferences, userId, timesheetId],
      apiFunc: getUserTimesheet,
      spreadParams: true,
      params: { userId },
    },
    config: { enabled: isBooking },
  });

  const { data: availabilityUser, isLoading: isLoadingAvailabilityUser } =
    useReactQuery({
      action: {
        apiFunc: getUser,
        key: [QueryKeys.getUser, userId],
        params: {
          containsCroppedHeaderImageLink: true,
          userId,
        },
      },
      config: {
        enabled: Boolean(userId),
      },
    });

  const otherUserDataTimesheet = useMemo(
    () =>
      otherUserData?.timesheets.find(
        ({ id = undefined, default: isDefault = false }) =>
          id === timesheetId ||
          (timesheetId === 'default' && isDefault) ||
          id === '1'
      ),
    [otherUserData, timesheetId]
  );
  const timesheet = useMemo(
    () =>
      userId
        ? otherUserDataTimesheet
        : preferencesData?.calendar?.timesheets.find(
            (ts) => ts.id === timesheetId
          ),
    [userId, otherUserDataTimesheet, preferencesData, timesheetId]
  );

  const { data: currentTimeZone } = useGetCurrentTimeZone();
  const [displayingTimezone, setDisplayingTimezone] = useState<Timezone>();
  useEffect(() => {
    if (currentTimeZone) {
      setDisplayingTimezone(currentTimezoneToTimezone(currentTimeZone));
    }
  }, [currentTimeZone]);
  const displayingTimesheet = useMemo(
    () =>
      timesheet && displayingTimezone
        ? applyTimezoneShiftOnTimesheet(timesheet, displayingTimezone)
        : timesheet,
    [timesheet, displayingTimezone]
  );
  const selectedHour = useMemo(
    () =>
      rawSelectedHour && displayingTimezone?.code
        ? dayjs(rawSelectedHour).tz(displayingTimezone?.code)
        : undefined,
    [displayingTimezone?.code, rawSelectedHour]
  );
  const selectedDay = useMemo(
    () =>
      rawSelectedDay && displayingTimezone?.code
        ? dayjs(rawSelectedDay).tz(displayingTimezone?.code)
        : !selectedHour && rawSelectedDay
          ? dayjs(rawSelectedDay)
          : selectedHour,
    [displayingTimezone?.code, rawSelectedDay, selectedHour]
  );

  const setSelectedHour = useCallback(
    (time: Dayjs) => {
      setScheduleAvailabilityPanelData({
        ...scheduleAvailabilityPanelData,
        selectedHour: time,
      });
    },
    [scheduleAvailabilityPanelData, setScheduleAvailabilityPanelData]
  );

  const openAvailabilityDetails = useCallback(
    (ts: Timesheet) => {
      setScheduleAvailabilityPanelData({
        isInAvailabilities: true,
        isInDetails: true,
        timeSheetId: ts.id,
      });
    },
    [setScheduleAvailabilityPanelData]
  );

  const openAvailabilityCrEdit = useCallback(
    (ts?: Timesheet) => {
      setScheduleAvailabilityPanelData({
        isInAvailabilities: true,
        isInCrEdit: true,
        timeSheetId: ts?.id,
      });
    },
    [setScheduleAvailabilityPanelData]
  );

  const { mutate: mutateAvailability } = useMutation({
    mutationFn: putTimeSheet,
  });

  const transformAvailabilityToBE = ({
    id,
    title,
    duration,
    timezone,
    attendeePermissions,
    contactType,
    description,
    dailyHours,
    meetingChannel,
    reminder,
    customLink,
    meetingLink,
    externalConferenceProvider,
    externalConferenceProviderType,
    location,
    ...rest
  }: Timesheet): BETimesheet => {
    const newDailyHours: BETimesheet['dailyHours'] = [];
    (Object.keys(dailyHours) as (keyof typeof dailyHours)[]).forEach((key) => {
      const dayArray =
        dailyHours[key]?.map((item) => ({
          ...item,
          day: key,
        })) || [];
      newDailyHours.push(...dayArray);
    });
    const unwrappedTimezone: Timezone = unwrapTimezone(timezone);

    const transformedValues = {
      ...rest,
      id,
      title,
      duration: duration.value,
      dailyHours: newDailyHours,
      attendeePermissions: attendeePermissions?.map((AP) => AP.value),
      contactType: contactType.value,
      description,
      ...(meetingChannel === MeetingChannel.LOBOX_ACCOUNT
        ? {
            externalConferenceProviderType: ConferenceProviderName.LOBOX,
          }
        : meetingChannel === MeetingChannel.PERSONAL_ACCOUNT
          ? {
              externalConferenceProviderType: externalConferenceProvider?.type,
              externalConferenceProviderId: externalConferenceProvider?.id,
            }
          : {
              customLink,
            }),
      reminder: reminder.value,
      location: location?.[0]?.location,
      ...unwrappedTimezone,
    };
    return transformedValues;
  };

  const setDefaultAvailability = async (ts: Timesheet) => {
    const currentDefaultAvailability =
      preferencesData?.calendar?.timesheets?.find((item) => item?.default);
    if (
      currentDefaultAvailability?.id &&
      currentDefaultAvailability.id === ts?.id
    )
      mutateAvailability(transformAvailabilityToBE({ ...ts, default: false }), {
        onSuccess: () => {
          refetchPreferences();
        },
      });
    mutateAvailability(transformAvailabilityToBE({ ...ts, default: true }), {
      onSuccess: (arg) => {
        if (currentDefaultAvailability)
          mutateAvailability(
            transformAvailabilityToBE({
              ...currentDefaultAvailability,
              default: false,
            }),
            { onSuccess: () => refetchPreferences() }
          );
        else refetchPreferences();
      },
    });
  };
  const { openConfirmDialog } = useOpenConfirm();
  const toast = useToast();
  const { mutate: deleteTimesheetMutation } = useMutation({
    mutationFn: deleteTimesheetApi,
  });
  const deleteTimesheet = useCallback(
    (ts: Timesheet, callBack?: Function) => {
      openConfirmDialog({
        title: t('remove_availability'),
        message: `${t('r_y_s_w_r')} ${ts.title}`,
        confirmButtonText: t('remove'),
        cancelButtonText: t('cancel'),
        confirmCallback: () => {
          deleteTimesheetMutation(
            {
              id: ts.id,
            },
            {
              onSuccess: () => {
                toast({
                  type: 'success',
                  icon: 'check-circle',
                  title: t('availability_removed'),
                  message: t('availability_removed_successfully'),
                });
                refetchPreferences();
                callBack?.();
              },
            }
          );
        },
      });
    },
    [deleteTimesheetMutation, openConfirmDialog, refetchPreferences, t, toast]
  );

  const openAvailabilityPanel = useCallback(() => {
    setScheduleAvailabilityPanelData({
      isInAvailabilities: true,
    });
  }, [setScheduleAvailabilityPanelData]);

  const openHoursPanel = useCallback(
    (date: DateType) => {
      setScheduleAvailabilityPanelData({
        ...scheduleAvailabilityPanelData,
        selectedDay: date,
      });
    },
    [scheduleAvailabilityPanelData, setScheduleAvailabilityPanelData]
  );

  const globalDispatch = useGlobalDispatch();
  const shareTimesheet = useCallback(
    (ts: Timesheet) => {
      globalDispatch({
        type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA',
        payload: {
          isOpen: true,
          tabs: [
            ShareEntityTab.COPY_LINK,
            ShareEntityTab.SHARE_VIA_MESSAGE,
            ShareEntityTab.SHARE_VIA_EMAIL,
          ],
          entityData: {
            attachment: {
              type: ShareEntities.AVAILABILITY,
              data: ts,
            },
            showCopyId: false,
          },
        },
      });
    },
    [globalDispatch]
  );

  // const redirectToAvailability = ({
  //   userId: availabilityUserId,
  //   availabilityId,
  // }: {
  //   userId: string;
  //   availabilityId: string;
  // }) => {
  //   let url = routeNames.schedulesAvailability;
  //   const params = new URLSearchParams({
  //     userId: availabilityUserId,
  //     availabilityId,
  //   });
  //   url = url.concat(`?${params?.toString()}`);
  //   history.push(url);
  // };

  return {
    isBooking,
    timesheet,
    timesheets: (preferencesData?.calendar?.timesheets || []).sort(
      defaultFirstSortFunction
    ),
    availabilityUser,
    isLoadingAvailabilityUser,
    isLoadingPreferences,
    isLoadingOtherUserTimesheets,
    openAvailabilityPanel,
    openAvailabilityCrEdit,
    openAvailabilityDetails,
    openHoursPanel,
    setSelectedHour,
    selectedDay,
    selectedHour,
    deleteTimesheet,
    shareTimesheet,
    refetchPreferences,
    refetchOtherUser,
    displayingTimezone,
    setDisplayingTimezone,
    setDefaultAvailability,
    displayingTimesheet,
  };
};

const defaultFirstSortFunction = (A: Timesheet, B: Timesheet) =>
  A?.default ? -1 : 0;
