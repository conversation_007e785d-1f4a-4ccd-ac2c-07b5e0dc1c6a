import { PresenceAnimationWrapper } from 'shared/components/molecules/PresenceAnimationWrapper';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import dynamic from 'next/dynamic';
import { useCallback } from 'react';
import FixedRightSideModalDialog from 'shared/uikit/Modal/FixedRightSideModalDialog/FixedRightSideModalDialog.component';
import type { MeetingDetails } from 'shared/types/schedules/schedules';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import ModalBody from 'shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from 'shared/uikit/Modal/ModalHeaderSimple';
import useTranslation from 'shared/utils/hooks/useTranslation';
import ModalFooter from 'shared/uikit/Modal/ModalFooter';
import Button from 'shared/uikit/Button';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import schedulesApi from 'shared/utils/api/schedules';
import useToast from 'shared/uikit/Toast/useToast';
import { BE_TIME_FORMAT, Time, dayjs } from 'shared/utils/Time';
import { QueryKeys, routeNames } from 'shared/utils/constants';
import useHistory from '@shared/utils/hooks/useHistory';
import { meetingAttendeePermissions } from '@shared/constants/schedules';
import useGetAppObject from '@shared/hooks/useGetAppObject';
import type { Timesheet } from '@shared/types/preferences/preferences';
import Skeleton from '@shared/uikit/Skeleton';
import { useAvailability } from './partials/useAvailability';
import { useGetBookedMeetings } from './partials/useGetBookedMeetings';

const AvailabilityPanel = dynamic(
  () => import('./partials/AvailabilityPanelContent/AvailabilityPanelContent'),
  { ssr: false }
);
const AvailabilityDetailsPanelContent = dynamic(
  () => import('./partials/AvailabilityDetailsPanelContent'),
  { ssr: false }
);
const CrEditAvailabilityPanelContent = dynamic(
  () =>
    import(
      './partials/CrEditAvailabilityPanelContent/CrEditAvailabilityPanelContent'
    ),
  {
    ssr: false,
    loading: () => (
      <ModalHeaderSimple title={<Skeleton className="!h-[21px] !w-[80px]" />} />
    ),
  }
);
const AvailableHoursPanelContent = dynamic(
  () => import('./partials/AvailableHoursPanelContent')
);
const ScheduleMeetingDetails = dynamic(
  () => import('../ScheduleDetailsModal/partials/ScheduleMeetingDetails')
);

const AvailabilityModule = () => {
  const {
    state: { scheduleAvailabilityPanelData },
    setScheduleAvailabilityPanelData,
    resetUrl,
  } = useSchedulesUrlState();

  const { t } = useTranslation();
  const toast = useToast();
  const history = useHistory();

  const {
    isInCrEdit,
    isInDetails,
    timeSheetId,
    isInAvailabilities,
    selectedDay: rawSelectedDay,
    selectedHour: rawSelectedHour,
    userId,
  } = scheduleAvailabilityPanelData || {};
  const queryClient = useQueryClient();
  const {
    refetchPreferences,
    availabilityUser,
    isBooking,
    isLoadingAvailabilityUser,
    isLoadingPreferences,
    isLoadingOtherUserTimesheets,
    openAvailabilityPanel,
    openAvailabilityCrEdit,
    openAvailabilityDetails,
    openHoursPanel,
    deleteTimesheet,
    shareTimesheet,
    selectedDay,
    selectedHour,
    timesheet,
    timesheets,
    displayingTimezone,
    setDisplayingTimezone,
    displayingTimesheet,
    setSelectedHour,
  } = useAvailability({
    timesheetId: timeSheetId,
    userId,
    rawSelectedDay: rawSelectedDay as unknown as string,
    rawSelectedHour: rawSelectedHour as unknown as string,
  });

  const newMeetingOverview: MeetingDetails | undefined = timesheet &&
    availabilityUser &&
    selectedHour && {
      ...timesheet,
      locationDetails: timesheet.addressDetail,
      title: timesheet.meetingTitle,
      creator: availabilityUser,
      start: selectedHour.toISOString(),
      attendees: [],
      currentUserIsCreator: false,
      attachmentFileIds: [],
      remind: timesheet.reminder,
      schedulesEventType: ScheduleEventTypes.MEETING,
      permissions: { ...meetingAttendeePermissions },
    };

  const { authUser } = useGetAppObject();
  const { bookedTimes } = useGetBookedMeetings({
    startDate: Time.getStartingDateOfDay(selectedDay || dayjs()).format(
      BE_TIME_FORMAT
    ),
    endDate: Time.getStartingDateOfDay(selectedDay || dayjs())
      .add(1, 'day')
      .format(BE_TIME_FORMAT),
    userId: isBooking ? availabilityUser?.id : authUser?.id,
  });

  const closeHandler = useCallback(
    () =>
      resetUrl({
        replace: true,
      }),
    [resetUrl]
  );
  const onCrEditSuccess = useCallback(() => {
    refetchPreferences();
    openAvailabilityPanel();
  }, [openAvailabilityPanel, refetchPreferences]);

  const backHandler = useCallback(
    (outside?: boolean) => {
      if (isInAvailabilities) {
        if (isInCrEdit) {
          if (!outside) {
            const { isInCrEdit: s, ...newState } =
              scheduleAvailabilityPanelData!;
            setScheduleAvailabilityPanelData(newState, { replace: true });
          }
        } else if (selectedDay) {
          const { selectedDay: s, ...newState } =
            scheduleAvailabilityPanelData!;
          setScheduleAvailabilityPanelData(newState, { replace: true });
        } else if (isInDetails && timeSheetId) {
          const {
            isInDetails: s,
            timeSheetId: ss,
            ...newState
          } = scheduleAvailabilityPanelData!;
          setScheduleAvailabilityPanelData(newState, { replace: true });
        } else {
          resetUrl({ replace: true });
        }
      } else if (isBooking) {
        if (selectedHour) {
          const { selectedHour: s, ...newState } =
            scheduleAvailabilityPanelData!;
          setScheduleAvailabilityPanelData(newState, { replace: true });
        } else if (selectedDay) {
          const { selectedDay: s, ...newState } =
            scheduleAvailabilityPanelData!;
          setScheduleAvailabilityPanelData(newState, { replace: true });
        } else {
          resetUrl({ replace: true });
        }
      } else {
        resetUrl({ replace: true });
      }
    },
    [
      isInAvailabilities,
      isBooking,
      isInCrEdit,
      selectedDay,
      isInDetails,
      timeSheetId,
      scheduleAvailabilityPanelData,
      setScheduleAvailabilityPanelData,
      resetUrl,
      selectedHour,
    ]
  );
  const onBack = useCallback(() => backHandler(), [backHandler]);
  const onClickOutside = useCallback(() => backHandler(true), [backHandler]);

  const { mutate: bookMeeting, isLoading: isBookingMeeting } = useMutation({
    mutationFn: schedulesApi.bookMeeting,
    onSuccess: () => {
      toast({
        type: 'success',
        icon: 'check-circle',
        title: t('meeting_scheduled'),
        message: t('meeting_suc_scheduled'),
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.getUpcomingAndPastEvents],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.getUpcomingMeetings],
      });
      resetUrl();
      history.replace(routeNames.schedulesCalendarMonth);
    },
  });
  const onScheduleClick = () => {
    if (selectedHour && timesheet?.id) {
      bookMeeting({
        timesheetId: timesheet.id,
        start: dayjs.utc(selectedHour).format('YYYY-MM-DDTHH:mm:ss'),
      });
    }
  };

  const onDeleteTimesheet = useCallback(
    (ts: Timesheet) => {
      deleteTimesheet(ts, refetchPreferences);
    },
    [deleteTimesheet, refetchPreferences]
  );
  const onDeleteTimesheetWithBack = useCallback(
    (ts: Timesheet) => {
      deleteTimesheet(ts, () => {
        refetchPreferences();
        backHandler();
      });
    },
    [backHandler, deleteTimesheet, refetchPreferences]
  );

  const isRightPanelOpen = Boolean(isInAvailabilities || isBooking);

  return (
    <PresenceAnimationWrapper isOpen={isRightPanelOpen}>
      <FixedRightSideModalDialog
        onBack={onBack}
        onClose={closeHandler}
        onClickOutside={onClickOutside}
      >
        {isInAvailabilities ? (
          isInCrEdit ? (
            <CrEditAvailabilityPanelContent
              onSuccess={onCrEditSuccess}
              timesheet={timesheet}
              onClose={onBack}
            />
          ) : selectedDay ? (
            displayingTimesheet && (
              <AvailableHoursPanelContent
                timesheet={displayingTimesheet}
                day={selectedDay}
                bookedMeetings={bookedTimes}
              />
            )
          ) : isInDetails && timeSheetId && displayingTimezone ? (
            <AvailabilityDetailsPanelContent
              availabilityUser={availabilityUser}
              isLoadingCreatorUser={isLoadingAvailabilityUser}
              isLoading={isLoadingPreferences}
              onDeleteTimesheet={onDeleteTimesheetWithBack}
              onOpenAvailabilityCrEdit={openAvailabilityCrEdit}
              onOpenHoursPanel={openHoursPanel}
              timesheet={displayingTimesheet}
              timezone={displayingTimezone}
              onSetTimezone={setDisplayingTimezone}
            />
          ) : (
            <AvailabilityPanel
              isLoading={isLoadingPreferences}
              onDeleteTimesheet={onDeleteTimesheet}
              onOpenAvailabilityCrEdit={openAvailabilityCrEdit}
              onOpenAvailabilityDetails={openAvailabilityDetails}
              onShareTimesheet={shareTimesheet}
              timesheets={timesheets}
            />
          )
        ) : isBooking ? (
          selectedHour ? (
            newMeetingOverview && (
              <>
                <ModalHeaderSimple title={t('meeting_details')} />
                <ModalBody>
                  <ScheduleMeetingDetails meeting={newMeetingOverview} />
                </ModalBody>
                <ModalFooter className="flex !flex-row gap-8">
                  <Button
                    label={t('discard')}
                    fullWidth
                    schema="semi-transparent3"
                    onClick={() => backHandler()}
                    isLoading={isBookingMeeting}
                  />
                  <Button
                    label={t('schedule_meeting')}
                    fullWidth
                    onClick={onScheduleClick}
                    isLoading={isBookingMeeting}
                  />
                </ModalFooter>
              </>
            )
          ) : selectedDay ? (
            displayingTimesheet && (
              <AvailableHoursPanelContent
                timesheet={displayingTimesheet}
                day={selectedDay}
                isSelectMode
                onHourClick={setSelectedHour}
                bookedMeetings={bookedTimes}
              />
            )
          ) : (
            displayingTimezone && (
              <AvailabilityDetailsPanelContent
                availabilityUser={availabilityUser}
                isLoadingCreatorUser={isLoadingAvailabilityUser}
                isBooking
                isLoading={isLoadingOtherUserTimesheets}
                onDeleteTimesheet={deleteTimesheet}
                onOpenAvailabilityCrEdit={openAvailabilityCrEdit}
                onOpenHoursPanel={openHoursPanel}
                timesheet={displayingTimesheet}
                timezone={displayingTimezone}
                onSetTimezone={setDisplayingTimezone}
              />
            )
          )
        ) : null}
      </FixedRightSideModalDialog>
    </PresenceAnimationWrapper>
  );
};

export default AvailabilityModule;
