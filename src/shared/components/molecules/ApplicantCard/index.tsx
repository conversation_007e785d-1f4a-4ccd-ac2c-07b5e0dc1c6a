import SearchCard from 'shared/components/Organism/SearchCard';
import type {
  ApplicantBaseType,
  JobParticipationModel,
} from 'shared/types/jobsProps';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import { useMemo, useRef, useState, type FC } from 'react';
import IconButton from 'shared/uikit/Button/IconButton';
import useOpenMessage from 'shared/hooks/useOpenMessage';
import { roomMemberRoles } from 'shared/components/Organism/Message/constants';
import PopperMenu from 'shared/uikit/PopperMenu';
import PopperItem from 'shared/uikit/PopperItem';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Icon from '@shared/uikit/Icon';
import Link from '@shared/uikit/Link';
import { recruiterSideRoutes } from '@shared/utils/constants/routeNames';
import Tooltip from '@shared/uikit/Tooltip';
import useHistory from '@shared/utils/hooks/useHistory';
import type { PipelineProps } from '@shared/types/pipelineTypes';
import classes from './index.module.scss';
import ApplicantCardBadges from './partials/ApplicantCardBadges';
import HorizontalTagList from '../HorizontalTagList';
import ApplicantCardMoreOptions from './partials/ApplicantCardMoreOptions';

interface BaseCandidateOrApplicantCardProps {
  id: string;
  fullName: string;
  image: string;
  location: string;
  occupation: string;
  username: string;
  onPrimaryButtonClick?: () => void;
}

interface ApplicantCardProps {
  data: JobParticipationModel;
  onChangePipeline: (pipeline: PipelineProps) => void;
  variant?: 'applicant' | 'candidate';
  showJob?: boolean;
  onPrimaryButtonClick?: () => void;
}

const ApplicantCard: FC<ApplicantCardProps> = ({
  data,
  onChangePipeline,
  variant = 'applicant',
  showJob = false,
  onPrimaryButtonClick,
}) => {
  const [isHovering, setIsHovering] = useState(false);
  const history = useHistory();
  const userData = useMemo(() => {
    if (variant === 'applicant') {
      const user = data.applicant;
      return {
        id: user.id,
        fullName: `${user.name} ${user.surname}`,
        image: user.croppedImageUrl,
        location: user.location?.title,
        occupation: user.occupationName,
        username: user.username,
      };
    }
    const user = data.candidate;
    return {
      id: user.id,
      fullName: user.profile?.fullName,
      image: user.profile?.croppedImageUrl,
      location: '',
      occupation: user.profile?.occupationName,
      username: user.profile?.username,
    };
  }, [variant, data]) as BaseCandidateOrApplicantCardProps;
  const onClickAvatar = () =>
    history.push(`/candidates?currentEntityId=${userData.id}`);
  const onMouseEnter = () => {
    setIsHovering(true);
  };
  const onMouseLeave = () => {
    setIsHovering(false);
  };
  return (
    <SearchCard
      imgSrc={userData.image}
      firstText={userData.fullName}
      secondText={userData.username}
      thirdText={userData.occupation}
      fourthText={userData.location}
      isHoverAble={false}
      // eslint-disable-next-line react/no-unstable-nested-components
      bottomComponent={() => (
        <Elements
          user={userData}
          onChangePipeline={onChangePipeline}
          pipelines={data.job.pipelines ?? []}
          jobId={data.job.id}
          jobTitle={data.job.title}
          tags={data.job.tags ?? []}
          onPrimaryButtonClick={onPrimaryButtonClick}
          {...data}
        />
      )}
      classNames={{
        bottomWrapper: classes.bottomWrapper,
        wrapper: '!pr-0',
      }}
      topRightActionComponent={
        showJob || isHovering ? (
          <Flex className="justify-end items-end h-full">
            {isHovering && (
              <ApplicantCardMoreOptions
                candidate={data.candidate ?? data.applicant}
                isCandidate={!!data.candidate}
              />
            )}
            {showJob && (
              <JobInfo jobTitle={data.job.title} jobId={data.job.id} />
            )}
          </Flex>
        ) : undefined
      }
      onClickAvatar={onClickAvatar}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    />
  );
};

export default ApplicantCard;

interface ElementsProps extends ApplicantBaseType {
  user: BaseCandidateOrApplicantCardProps;
  onChangePipeline: (pipeline: PipelineProps) => void;
  onPrimaryButtonClick?: (user: BaseCandidateOrApplicantCardProps) => void;
}

const Elements = ({
  user,
  onChangePipeline,
  tags,
  pipeline,
  pipelines,
  notesCount,
  meetingsCount,
  todosCount,
  dateTime,
  onPrimaryButtonClick,
}: ElementsProps) => {
  const { t } = useTranslation();
  const openMessage = useOpenMessage();
  const sortedPipelines = useMemo(
    () => pipelines?.sort((a, b) => a.order - b.order) ?? [],
    [pipelines]
  );
  const openMessageHandler = (e: any) => {
    openMessage({
      isGroupChat: false,
      id: user.id,
      icon: user.image,
      name: user.fullName,
      owner: user.id,
      username: user.username,
      createdAt: new Date(),
      role: roomMemberRoles.Owner,
      isPage: false,
    });
  };

  return (
    <Flex className={classes.elementsRoot}>
      {!!tags?.length && <HorizontalTagList tags={tags} editable={false} />}
      <ApplicantCardBadges
        notesCount={notesCount}
        todosCount={todosCount}
        meetingsCount={meetingsCount}
        dateTime={dateTime}
      />
      <Flex className={classes.buttons}>
        <PopperMenu
          placement="bottom-end"
          closeOnScroll
          buttonComponent={
            <Button
              label={pipeline.title}
              schema="semi-transparent2"
              rightIcon="chevron-down"
              fullWidth
            />
          }
          popperWidth={(width) => width}
        >
          <>
            {sortedPipelines.map((_pipeline) => (
              <PopperItem
                key={_pipeline.title}
                onClick={() => onChangePipeline(_pipeline)}
                label={_pipeline.title}
                isSelected={_pipeline.title === pipeline.title}
              />
            ))}
          </>
        </PopperMenu>
        <Button
          label={t('manage')}
          fullWidth
          leftIcon="user-cog"
          onClick={() => onPrimaryButtonClick?.(user)}
        />
        <IconButton
          name="envelope"
          type="far"
          variant="rectangle"
          colorSchema="graySecondary"
          size="md20"
          onClick={openMessageHandler}
        />
      </Flex>
    </Flex>
  );
};

const JobInfo = ({ jobTitle, jobId }: { jobTitle: string; jobId: string }) => {
  const { t } = useTranslation();
  const titleBoxRef = useRef<HTMLDivElement>();
  const isEllipsis =
    (titleBoxRef.current?.scrollWidth ?? 0) >
    (titleBoxRef.current?.clientWidth ?? 0);

  return (
    <Flex className="!flex-row items-end">
      <Flex className="!flex-row items-center gap-4">
        <Icon
          name="briefcase-blank-light"
          type="far"
          size={16}
          color="colorIconForth2"
        />
        <Typography
          color="colorIconForth2"
          font="600"
          className="whitespace-nowrap"
        >
          {`${t('latest_link')}:`}
        </Typography>
        <Tooltip
          disabled={!isEllipsis}
          triggerWrapperClassName="h-fit"
          placement="top"
          trigger={
            <Link
              to={`${recruiterSideRoutes.jobLink}?currentEntityId=${jobId}`}
              className={classes.jobInfo}
              onClick={(e) => e.stopPropagation()}
            >
              <Typography
                ref={titleBoxRef}
                color="brand"
                font="600"
                className="!block whitespace-nowrap text-ellipsis max-w-[120px] overflow-hidden"
              >
                {jobTitle}
              </Typography>
            </Link>
          }
        >
          {jobTitle}
        </Tooltip>
      </Flex>
    </Flex>
  );
};
