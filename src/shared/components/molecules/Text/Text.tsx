import { type ReactHTML, useCallback, useMemo } from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex, { type FlexProps } from 'shared/uikit/Flex';
import Typography, { type TypographyProps } from 'shared/uikit/Typography';
import RichTextEditor from 'shared/uikit/RichTextEditor';
import BaseButton from 'shared/uikit/Button/BaseButton';
import removeBreaksAndSpaces from 'shared/utils/toolkit/removeBreaksAndSpaces';
import useTranslation from 'shared/utils/hooks/useTranslation';
import useSchedulesEvent from 'shared/hooks/useSchedulesEvent';
import IconButton from 'shared/uikit/Button/IconButton';
import copyTextToClipboard from 'shared/uikit/utils/copyToClipboard';
import PolymorphicButton from 'shared/uikit/Button/PolymorphicButton';
import Tooltip, { type TooltipProps } from '@shared/uikit/Tooltip/Tooltip';
import Icon from '@shared/uikit/Icon';
import { type IconProps } from '@shared/uikit/Icon/types';
import classes from './Text.module.scss';

export interface TextProps<As extends keyof ReactHTML | undefined>
  extends FlexProps {
  className?: string;
  label: string;
  value: string | null | undefined;
  isRichText?: boolean;
  typographyProps?: Omit<TypographyProps<As>, 'children'>;
  copyText?: string;
  append?: ReactNode;
  hideIfEmpty?: boolean;
  hint?: string | JSX.Element;
  hintTooltipProps?: Omit<TooltipProps, 'children' | 'trigger'>;
  hintTriggerProps?: IconProps;
}

const Text = <As extends keyof ReactHTML | undefined>({
  label,
  value = '',
  isRichText,
  copyText,
  append,
  typographyProps,
  hideIfEmpty,
  hint,
  hintTooltipProps = {},
  hintTriggerProps = {},
  ...wrapperProps
}: TextProps<As>) => {
  const { t } = useTranslation();
  const { handleEdit } = useSchedulesEvent();
  const edit = useCallback(() => handleEdit(true), [handleEdit]);

  const trimedValue = useMemo(
    () => removeBreaksAndSpaces(value ?? ''),
    [value]
  );

  if (hideIfEmpty && !value) return null;
  return (
    <Flex {...wrapperProps}>
      <Typography
        color="colorIconForth2"
        font="500"
        size={13}
        height={15}
        className={classes.textLabel}
      >
        {label}
        {hint && (
          <Tooltip
            trigger={
              <Icon
                size={13}
                name="info-circle"
                type="fal"
                {...hintTriggerProps}
              />
            }
            placement="top"
            tooltipWrapperProps={{
              className: classes.hintTooltip,
            }}
            {...hintTooltipProps}
          >
            {hint}
          </Tooltip>
        )}
      </Typography>
      {trimedValue ? (
        isRichText ? (
          <RichTextEditor
            value={value ?? ''}
            readOnly
            className={classes.editor}
            styles="!p-0 !border-none"
          />
        ) : !copyText ? (
          <Typography
            color="thirdText"
            mt={4}
            height={18}
            size={14}
            {...typographyProps}
          >
            {value}
          </Typography>
        ) : (
          <Flex className={classes.copyText}>
            {append}
            <Typography
              color="brand"
              height={18}
              size={14}
              className={cnj('grow truncate', classes.copyTextValue)}
              {...typographyProps}
            >
              {value}
            </Typography>
            <PolymorphicButton
              NormalComponent={IconButton}
              onClick={() => {
                copyTextToClipboard(copyText);
              }}
              name="copy"
              type="far"
              size="sm20"
              className={classes.copyTextButton}
              transformedComponentProps={{
                name: 'check-circle',
                type: 'far',
                colorSchema: 'ghost-success',
              }}
            />
          </Flex>
        )
      ) : (
        <BaseButton onClick={edit} className={classes.emptyButton}>
          <Typography
            color="gray_60"
            size={14}
            height={18}
            className={classes.emptyText}
          >
            {`${t('add')} ${label}`}
          </Typography>
        </BaseButton>
      )}
    </Flex>
  );
};

export default Text;
