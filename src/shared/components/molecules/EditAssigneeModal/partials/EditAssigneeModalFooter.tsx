import eventKeys from '@shared/constants/event-keys';
import type { MultiStepFormFooterProps } from '@shared/types/formTypes';
import Button from '@shared/uikit/Button';
import Flex from '@shared/uikit/Flex';
import SubmitButton from '@shared/uikit/Form/SubmitButton';
import useTranslation from '@shared/utils/hooks/useTranslation';
import event from '@shared/utils/toolkit/event';
import Observer from '@shared/components/atoms/Observer';

const EditAssigneeModalFooter = (props: MultiStepFormFooterProps) => {
  const { step, validateForm } = props;
  const { t } = useTranslation();

  const handleClickDiscard = () => {
    event.trigger(eventKeys.closeModal);
    return null;
  };

  return (
    <Flex className="!flex-row gap-8">
      <Observer step={step} validateForm={validateForm} />
      <Button
        fullWidth
        label={t('discard')}
        schema="gray-semi-transparent"
        onClick={handleClickDiscard}
      />
      <SubmitButton fullWidth label={t('save')} schema="primary-blue" />
    </Flex>
  );
};

export default EditAssigneeModalFooter;
