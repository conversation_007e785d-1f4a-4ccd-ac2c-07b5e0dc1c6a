import type { MultiStepFormStepProps } from '@shared/types/formTypes';
import useTranslation from '@shared/utils/hooks/useTranslation';
import EditAssigneeModalFooter from './EditAssigneeModalFooter';
import EditAssigneeModalBody from './EditAssigneeModalBody';

const useEditAssignee = ({ onClose }: { onClose?: VoidFunction }) => {
  const { t } = useTranslation();

  const getHeaderProps: MultiStepFormStepProps['getHeaderProps'] = ({
    step,
  }) => ({
    title: t('edit_assignee'),
    hideBack: step === 0 || step === 5,
    backButtonProps: {
      onClick: onClose,
    },
    noCloseButton: step !== 0,
  });
  const renderFooter: MultiStepFormStepProps['renderFooter'] = (
    footerProps
  ) => <EditAssigneeModalFooter {...footerProps} />;
  const renderBody: MultiStepFormStepProps['renderBody'] = () => (
    <EditAssigneeModalBody />
  );

  return { getHeaderProps, renderFooter, renderBody };
};

export default useEditAssignee;
