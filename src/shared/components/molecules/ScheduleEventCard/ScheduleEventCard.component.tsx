import BaseButton from 'shared/uikit/Button/BaseButton';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import OverflowTip from 'shared/uikit/Typography/OverflowTip';
import Typography from 'shared/uikit/Typography';
import type { UserType } from 'shared/types/user';
import useTranslation from 'shared/utils/hooks/useTranslation';
import AvatarsCard from 'shared/components/molecules/AvatarsCard';
import { useSchedulesUrlState } from '@shared/hooks/useSchedulesUrlState';
import { Time } from 'shared/utils/Time';
import { checkIfAllDay } from 'shared/utils/checkIfAllDay';
import type {
  CalendarEvent,
  CreatableSchedulesEventTypes,
  EventDetails,
  TaskStatus,
} from 'shared/types/schedules/schedules';
import { type ColorsKeys } from '@shared/uikit/types';
import AvatarCard, { type AvatarCardProps } from '@shared/uikit/AvatarCard';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import type { ShareEntities } from '@shared/types/share/entities';
import ScheduleEventDateTime from './ScheduleEventCard.dateTime';
import ScheduleEvensCardActions from './ScheduleEventCard.actions';
import CardBadgeTrigger from '../CardBadge/CardBadgeTrigger';
import { EntitiyPopper } from '../EntityPopper/EntitiyPopper';
import ScheduleEventCardEventIndicator from './ScheduleEventCardEventIndicator';
import classes from './ScheduleEventCard.component.module.scss';

type TaskStatusKey = keyof typeof TaskStatus;
const badgeColors: Record<TaskStatusKey, ColorsKeys> = {
  OPEN: 'brand',
  IN_PROGRESS: 'pendingOrange',
  DONE: 'success',
};

interface ScheduleEventCardProps {
  event: CalendarEvent | EventDetails;
  className?: string;
  action?: ((event: CalendarEvent | EventDetails) => ReactNode) | ReactNode;
  onClick?: (event: CalendarEvent | EventDetails) => void;
  clickAction?: 'edit' | 'details';
  disableOnClick?: boolean;
  noActionButtons?: boolean;
  noUserPopper?: boolean;
}

const mapUserEntityToCardData = ({
  croppedImageUrl,
  fullName,
  username,
  usernameAtSign,
  id,
}: Partial<UserType>): AvatarCardProps['data'] => ({
  image: croppedImageUrl,
  title: fullName,
  subTitle: usernameAtSign || `@${username}` || undefined,
  username,
  id,
});

const limit = 3;

const ScheduleEventCard = ({
  event,
  className,
  action,
  onClick,
  clickAction = 'edit',
  disableOnClick,
  noActionButtons,
  noUserPopper,
}: ScheduleEventCardProps) => {
  const eventType =
    'schedulesEventType' in event ? event.schedulesEventType : event.type;

  const { title, start, attendees, assignee } =
    'schedulesEventType' in event
      ? {
          title: event.title,
          start: 'start' in event ? event.start : event.date,
          attendees: 'attendees' in event ? event.attendees : undefined,
          assignee:
            'assignee' in event && event?.assignee && 'user' in event.assignee
              ? event.assignee
              : undefined,
        }
      : event;
  const end = 'end' in event ? event.end : undefined;
  const creator = 'creator' in event ? (event.creator as UserType) : undefined;

  const isAllDay = checkIfAllDay({ ...event, type: eventType });
  const startTime =
    start &&
    (isAllDay
      ? Time.getTimeWithOffset(
          Time.convertBackFormatToFront(start)!,
          -Time.getCurrentTimezoneOffset()
        )
      : Time.convertBackFormatToFront(start));

  const endTime =
    end &&
    (isAllDay
      ? Time.getTimeWithOffset(
          Time.convertBackFormatToFront(end)!,
          -Time.getCurrentTimezoneOffset()
        )
      : Time.convertBackFormatToFront(end));

  const { t } = useTranslation();
  const { setScheduleEventsPanelData: setScheduleCreationModalData } =
    useSchedulesUrlState();

  const names = {
    [ScheduleEventTypes.MEETING]: t('meeting'),
    [ScheduleEventTypes.REMINDER]: t('reminder'),
    [ScheduleEventTypes.TASK]: t('task'),
    [ScheduleEventTypes.HOLIDAY]: t('p_holiday'),
    [ScheduleEventTypes.BIRTHDAY]: t('birthday'),
  };

  const startDateLabels = {
    [ScheduleEventTypes.BIRTHDAY]: t('date'),
    [ScheduleEventTypes.HOLIDAY]: t('date'),
    [ScheduleEventTypes.REMINDER]: t('date_time'),
  };

  const isTask = eventType === ScheduleEventTypes.TASK;
  const isMeeting = eventType === ScheduleEventTypes.MEETING;
  const isReminder = eventType === ScheduleEventTypes.REMINDER;

  const onlyDateNoTime = [
    ScheduleEventTypes.HOLIDAY,
    ScheduleEventTypes.BIRTHDAY,
  ].includes(eventType);
  const noEnd = [
    ScheduleEventTypes.HOLIDAY,
    ScheduleEventTypes.BIRTHDAY,
    ScheduleEventTypes.REMINDER,
  ].includes(eventType);

  const subTitle = names[eventType];
  const eventStatus = event?.status as TaskStatusKey | undefined;

  const startTimeLabel =
    onlyDateNoTime || isAllDay || !startTime
      ? undefined
      : Time.getFormTime(startTime)?.label;

  const endTimeLabel =
    endTime && (noEnd ? undefined : Time.getFormTime(endTime)?.label);

  const avatars = [creator, ...(attendees || [])].filter(Boolean) as UserType[];

  const visibleAvatars = avatars.slice(0, limit);
  const remainCount =
    avatars?.length > limit ? Number(avatars?.length) - limit : undefined;
  const startDateLabel =
    startDateLabels[eventType as keyof typeof startDateLabels] ??
    t('start_date_time');

  const onClickHandler = () => {
    if (disableOnClick) return;
    onClick?.(event);
    setScheduleCreationModalData({
      isInCrEdit: clickAction === 'edit',
      schedulesEventType: eventType as CreatableSchedulesEventTypes,
      eventId: event.id,
    });
  };

  return (
    <BaseButton
      onClick={onClickHandler}
      className={cnj(classes.scheduleEvensCardRoot, className)}
    >
      <Flex className="!flex-row justify-between">
        <Flex className="flex-1 whitespace-nowrap overflow-hidden">
          <OverflowTip
            font="700"
            height={19}
            size={16}
            color="smoke_coal"
            className={classes.title}
          >
            {title}
          </OverflowTip>
          <Typography color="muteMidGray_gray80" height={14} size={12}>
            {subTitle}
          </Typography>
        </Flex>
        {!noActionButtons && (
          <ScheduleEvensCardActions event={event as CalendarEvent} />
        )}
      </Flex>
      {isTask && eventStatus && (
        <Flex className={classes.marginTop}>
          <Typography
            color="secondaryDisabledText"
            className={classes.labelAttendees}
            font="500"
            size={13}
            height={15}
          >
            {t('status')}
          </Typography>
          <CardBadgeTrigger
            value={t(eventStatus)}
            iconProps={{
              color: badgeColors[eventStatus],
              name: 'circle',
              size: 8,
              type: 'fas',
            }}
            noChevron
            className={classes.statusBadge}
          />
        </Flex>
      )}
      <Flex className="!flex-row justify-between items-end">
        <Flex>
          {(startTime || startTimeLabel) && (
            <ScheduleEventDateTime
              className={classes.marginTop}
              label={startDateLabel}
              date={startTime}
              time={startTimeLabel}
            />
          )}

          {isTask && (endTime || endTimeLabel) && (
            <ScheduleEventDateTime
              className={classes.marginTop}
              label={t('end_date_time')}
              date={endTime}
              time={endTimeLabel}
            />
          )}
        </Flex>
      </Flex>
      {isTask ? (
        <Flex className={classes.marginTop}>
          <Typography
            color="secondaryDisabledText"
            className={classes.labelAttendees}
            font="500"
            size={13}
            height={15}
          >
            {t('assignee')}
          </Typography>
          {assignee?.user ? (
            <EntitiyPopper
              isPage={false}
              username={creator?.username ?? ''}
              popperMenuProps={{ disablePortal: noUserPopper }}
              // classNames={{ buttonWrapper: classes.avatar }}
            >
              <AvatarCard
                data={mapUserEntityToCardData(assignee.user)}
                noHover
                withPadding={false}
                avatarProps={{ bordered: true }}
                titleProps={{ size: 16, font: '700', color: 'smoke_coal' }}
                subTitleProps={{ size: 14, color: 'secondaryDisabledText' }}
              />
            </EntitiyPopper>
          ) : (
            <Typography
              color="primaryDisabledText"
              font="400"
              size={14}
              height={16}
              className={classes.noPeopleText}
            >
              {t('no_assignees_added')}
            </Typography>
          )}
        </Flex>
      ) : null}
      {isMeeting && (
        <Flex className={classes.marginTop}>
          <Typography
            color="secondaryDisabledText"
            className={classes.labelAttendees}
            font="500"
            size={13}
            height={15}
          >
            {t('attendees')}
          </Typography>
          {avatars?.length ? (
            avatars?.length === 1 ? (
              <EntitiyPopper
                isPage={false}
                username={visibleAvatars[0]?.username ?? ''}
                popperMenuProps={{ disablePortal: noUserPopper }}
              >
                <AvatarCard
                  noHover
                  withPadding={false}
                  avatarProps={{ bordered: true }}
                  data={mapUserEntityToCardData(visibleAvatars[0])}
                  titleProps={{ size: 16, font: '700', color: 'smoke_coal' }}
                  subTitleProps={{ size: 14, color: 'secondaryDisabledText' }}
                />
              </EntitiyPopper>
            ) : (
              <AvatarsCard
                avatarProps={{
                  size: 'smd',
                  className: classes.avatar,
                }}
                className="px-0"
                text={
                  remainCount ? `+${remainCount} ${t('more_sm')}` : undefined
                }
                avatars={visibleAvatars}
                disableUserPopper={noUserPopper}
              />
            )
          ) : (
            <Typography
              color="primaryDisabledText"
              font="400"
              size={14}
              height={16}
              className={classes.noPeopleText}
            >
              {isTask
                ? t('no_assignees_added')
                : isMeeting
                  ? t('no_attendees_added')
                  : isReminder
                    ? t('no_assignees_added')
                    : ''}
            </Typography>
          )}
        </Flex>
      )}
      {isTask && creator ? (
        <Flex className={classes.marginTop}>
          <Typography
            color="secondaryDisabledText"
            className={classes.labelAttendees}
            font="500"
            size={13}
            height={15}
          >
            {t('creator')}
          </Typography>
          <EntitiyPopper
            isPage={false}
            username={creator?.username ?? ''}
            popperMenuProps={{ disablePortal: noUserPopper }}
            // classNames={{ buttonWrapper: classes.avatar }}
          >
            <AvatarCard
              data={mapUserEntityToCardData(creator)}
              noHover
              withPadding={false}
              avatarProps={{ bordered: true }}
              titleProps={{ size: 16, font: '700', color: 'smoke_coal' }}
              subTitleProps={{ size: 14, color: 'secondaryDisabledText' }}
            />
          </EntitiyPopper>
        </Flex>
      ) : null}

      {action && (
        <Flex className={classes.marginTop}>
          {typeof action === 'function' ? action(event) : action}
        </Flex>
      )}
      <ScheduleEventCardEventIndicator
        type={eventType as unknown as ShareEntities}
      />
    </BaseButton>
  );
};

export default ScheduleEventCard;
