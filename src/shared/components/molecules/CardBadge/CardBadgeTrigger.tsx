import Flex from '@shared/uikit/Flex';
import type { IconProps } from '@shared/uikit/Icon/types';
import Icon from '@shared/uikit/Icon';
import Tooltip from '@shared/uikit/Tooltip';
import type { TooltipProps } from '@shared/uikit/Tooltip/Tooltip';
import Typography from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';

export interface CardBadgeTriggerProps {
  isOpen?: boolean;
  value: { value: string; label: string } | string;
  iconProps?: IconProps;
  onClick?: VoidFunction;
  noChevron?: boolean;
  tooltipProps?: TooltipProps;
  className?: string;
}

const CardBadgeTrigger = (props: CardBadgeTriggerProps) => {
  const {
    isOpen,
    value,
    iconProps,
    onClick,
    noChevron,
    tooltipProps,
    className,
    ref,
  } = props;

  return (
    <Tooltip
      {...tooltipProps}
      ref={ref}
      placement="top"
      disabled={!tooltipProps?.children || tooltipProps?.disabled}
      triggerWrapperClassName={cnj(
        'w-fit',
        tooltipProps?.triggerWrapperClassName
      )}
      trigger={
        <Flex
          onClick={onClick}
          className={cnj(
            'w-fit py-4 px-8 !flex-row items-center gap-[6px] border-[1px] border-techGray_20 border-solid rounded',
            noChevron ? '' : 'cursor-pointer',
            className
          )}
        >
          <Flex className="items-center justify-center w-16 h-16">
            <Icon
              {...iconProps}
              color={iconProps?.color ?? 'primaryText'}
              name={iconProps?.name ?? 'question'}
              type={iconProps?.type ?? 'far'}
              size={iconProps?.size ?? 16}
            />
          </Flex>
          <Typography height={16} lineNumber={1} font="500">
            {value?.label || value}
          </Typography>
          {!noChevron && (
            <Icon
              type="far"
              name={isOpen ? 'chevron-up' : 'chevron-down'}
              size={12}
              className="ml-auto"
            />
          )}
        </Flex>
      }
    >
      {tooltipProps?.children}
    </Tooltip>
  );
};

CardBadgeTrigger.displayName = 'CardBadgeTrigger';

export default CardBadgeTrigger;
