import type { colorsKeys } from '@shared/uikit/helpers/theme';
import type { IconName } from '@shared/uikit/Icon/types';
import PopperMenu from '@shared/uikit/PopperMenu';
import PopperItem from '@shared/uikit/PopperItem';
import { useCallback } from 'react';
import type { TooltipProps } from '@shared/uikit/Tooltip/Tooltip';
import cnj from '@shared/uikit/utils/cnj';
import type { PopperMenuProps } from '@shared/uikit/PopperMenu';
import CardBadgeTrigger from './CardBadgeTrigger';

export interface CardBadgeOptionProps<T extends string> {
  value: T;
  label: string;
}

export type CardBadgeIconsDetailsProps<T extends string> = {
  iconName?: string;
  iconClassName?: string;
  iconSize?: number;
  iconColor?: colorsKeys;
  properties?: {
    [key in T]: {
      color?: colorsKeys;
      name?: IconName;
    };
  };
};

export interface CardBadgeProps<T extends string> {
  onChange?: (value: CardBadgeOptionProps<T>) => void;
  value: CardBadgeOptionProps<T> | string;
  options?: CardBadgeOptionProps<T>[];
  iconsDetails?: CardBadgeIconsDetailsProps<T>;
  name?: string;
  tooltipProps?: TooltipProps;
  onClick?: VoidFunction;
  classNames?: {
    badge?: string;
  };
  popperMenuProps?: PopperMenuProps;
}

const CardBadge = <T extends string>(props: CardBadgeProps<T>) => {
  const {
    onChange,
    value,
    options,
    iconsDetails,
    name,
    tooltipProps,
    onClick,
    classNames,
    popperMenuProps,
  } = props;
  const onSelect = (item: CardBadgeOptionProps<T>) => onChange?.(item);
  const iconName = useCallback(
    (item: CardBadgeOptionProps<T>): IconName =>
      (iconsDetails?.iconName as IconName) ??
      iconsDetails?.properties?.[item.value]?.name ??
      'circle-s',
    [iconsDetails]
  );
  const iconColor = useCallback(
    (item: CardBadgeOptionProps<T>) =>
      iconsDetails?.properties?.[item.value]?.color ?? 'brand',
    [iconsDetails]
  );

  const MemoizedAcionButton = useCallback(
    (isOpen: boolean) =>
      typeof value === 'string' ? null : (
        <CardBadgeTrigger
          value={value}
          isOpen={isOpen}
          iconProps={{
            color: iconColor(value),
            name: iconName(value),
            size: iconsDetails?.iconSize,
          }}
          tooltipProps={tooltipProps}
          className={classNames?.badge}
        />
      ),
    [value, iconName, iconColor, tooltipProps, iconsDetails, classNames]
  );

  if (!options || typeof value === 'string')
    return (
      <CardBadgeTrigger
        value={value}
        iconProps={{
          color: iconsDetails?.iconColor,
          name: iconsDetails?.iconName as IconName,
          size: iconsDetails?.iconSize,
          className: iconsDetails?.iconClassName,
        }}
        noChevron
        tooltipProps={tooltipProps}
        onClick={onClick}
        className={cnj(onClick ? '!cursor-pointer' : '', classNames?.badge)}
      />
    );

  return (
    <PopperMenu
      {...popperMenuProps}
      placement="bottom-start"
      closeOnScroll
      buttonComponent={MemoizedAcionButton}
      menuClassName={cnj('!p-12 min-w-[152px]', popperMenuProps?.menuClassName)}
    >
      {options.map((item) => (
        <PopperItem
          key={`${name}_${item.label}`}
          onClick={() => onSelect(item)}
          iconName={iconName(item)}
          iconColor={iconColor(item)}
          iconType="far"
          label={item.label}
          iconSize={16}
          className="!p-8"
          iconClassName={{
            wrapper: '!mt-0',
            icon: iconsDetails?.iconClassName,
          }}
          isSelected={item.value === value.value}
        />
      ))}
    </PopperMenu>
  );
};

export default CardBadge;
