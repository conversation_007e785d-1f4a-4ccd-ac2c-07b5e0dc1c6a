'use client';

/* eslint-disable jsx-a11y/no-noninteractive-element-interactions, jsx-a11y/click-events-have-key-events */
import { useMemo, type MouseEvent } from 'react';
import dayjs from 'dayjs';

import type { SavedEventType } from '@shared/types/schedules/schedules';
import { useSchedulesCalendar } from '@shared/hooks/useSchedulesCalendar';
import type { FlexProps } from '@shared/uikit/types';
import Typography from '@shared/uikit/Typography';
import { COLORS, COLORS_DARK } from '@shared/utils/consts';
import cnj from '@shared/uikit/utils/cnj';
import PopperMenu from '@shared/uikit/PopperMenu';
import preventClickHandler from '@shared/utils/toolkit/preventClickHandler';
import useMedia from '@shared/uikit/utils/useMedia';
import type { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import ScheduleEventCard from '../ScheduleEventCard';

import classes from './CalendarEvent.module.scss';

export interface CalendarEventProps<EventType> extends FlexProps {
  event: EventType;
  date: string;
  hoveredEvent?: string | null;
  setHoveredEvent?: (id: string | null) => void;
  onCreateEvent?: (clickEvent: MouseEvent<HTMLElement>) => void;
}
export default function CalendarEvent<
  EventType extends SavedEventType | null | undefined,
>({
  event,
  date,
  hoveredEvent,
  setHoveredEvent,
  onCreateEvent,
}: CalendarEventProps<EventType>) {
  const { isMoreThanTablet } = useMedia();
  const [isEmpty, label, startsToday, endsToday] = useMemo(() => {
    if (!event) return [true, null, false, false, 0];
    const currentDate = dayjs(date);
    const tstartsToday = event?.startTime?.isSame(currentDate, 'day');
    const tendsToday = event?.endTime?.isSame(currentDate, 'day');
    const tcanSpan =
      !tstartsToday || tendsToday
        ? 0
        : event?.endTime?.isBefore(currentDate.endOf('week'), 'day')
          ? event?.endTime?.diff(currentDate, 'day')
          : currentDate.endOf('week').diff(currentDate, 'day');
    return [
      false,
      tstartsToday ? event.title : null,
      tstartsToday,
      tendsToday,
      tcanSpan,
    ];
  }, [event, date]);

  const { handleEventClick } = useSchedulesCalendar();

  const onClickHandler =
    (event: EventType | null, date?: string) =>
    (clickEvent: MouseEvent<HTMLElement>) => {
      if (!event) onCreateEvent?.(clickEvent);
      else handleEventClick(clickEvent as MouseEvent<HTMLDivElement>, event);
    };

  const isHovered = useMemo(
    () => (!!event && !!hoveredEvent && event?.id === hoveredEvent) ?? false,
    [event, hoveredEvent]
  );
  if (isEmpty)
    return (
      <p
        onClick={onClickHandler(null, date)}
        className={cnj(classes.calendarEventWrapper, classes.emptyCell)}
      />
    );
  return (
    <PopperMenu
      showWithHover={isMoreThanTablet}
      hoverDelay={300}
      menuClassName={cnj(classes.menuClassName)}
      placement={'top'}
      closeOnScroll
      disablePortal={isMoreThanTablet === false}
      onOpen={() => setHoveredEvent?.(event?.id ?? null)}
      onClose={() => setHoveredEvent?.(null)}
      buttonComponent={() => (
        <div style={{ display: 'grid', width: '100%', height: 'min-content' }}>
          <p
            className={cnj(
              classes.calendarEventWrapper,
              classes[
                `backgroundColor-${COLORS[event?.type as ScheduleEventTypes]}`
              ],
              isHovered &&
                classes[
                  `backgroundColor-${COLORS_DARK[event?.type as ScheduleEventTypes]}`
                ],
              startsToday && classes.startsToday,
              endsToday && classes.endsToday,
              !startsToday && classes.emptyCell
            )}
            // onMouseOver={() => {
            //   setHoveredEvent?.(event?.id ?? null);
            // }}
            // onFocus={() => {
            //   setHoveredEvent?.(event?.id ?? null);
            // }}
            // onMouseOut={() => {
            //   setHoveredEvent?.(null);
            // }}
            // onBlur={() => {
            //   setHoveredEvent?.(null);
            // }}
            onClick={(e) => {
              preventClickHandler(e);
              handleEventClick(e, event);
            }}
            // onTouchEnd={preventClickHandler}
            // onClickCapture={preventClickHandler}
          >
            {label ? (
              <Typography
                color="inherit"
                variant="span"
                height={12}
                size={10}
                className={cnj(
                  classes.eventLabel,
                  !startsToday && classes.emptyCell
                )}
              >
                {label}
              </Typography>
            ) : null}
          </p>
        </div>
      )}
    >
      <ScheduleEventCard
        event={event as any}
        clickAction="details"
        className={classes.poppedItem}
      />
    </PopperMenu>
  );
}

export function CalendarEventFull<
  EventType extends SavedEventType | null | undefined,
>({
  date,
  event,
  onEventClick,
}: Omit<CalendarEventProps<EventType>, 'hoveredEvent' | 'setHoveredEvent'> & {
  onEventClick?: () => void;
}) {
  const timestamp = useMemo(() => {
    if (!event) return '';
    const currentDate = dayjs(date);
    const tstartsToday = event?.startTime.isSame(currentDate, 'day');
    const tendsToday = event?.endTime.isSame(currentDate, 'day');
    if (event.isAllDay) return 'all day';
    if (!tstartsToday && !tendsToday) return 'all day';

    return `${tstartsToday ? event.startTime.format('LT') : dayjs().startOf('day')?.format('LT')} - ${tendsToday ? event.endTime.format('LT') : dayjs().endOf('day').format('LT')}`;
  }, [event]);
  const { handleEventClick } = useSchedulesCalendar();
  const handleClick =
    (event: EventType) => (clickEvent: MouseEvent<HTMLElement>) => {
      onEventClick?.();
      handleEventClick(clickEvent, event);
    };
  if (!event) return null;
  return (
    <p
      className={cnj(
        classes.calendarEventWrapper,
        classes[`backgroundColor-${COLORS[event?.type as ScheduleEventTypes]}`],
        classes.calendarEventFullViewWrapper
      )}
      onClick={handleClick(event)}
    >
      <Typography
        color="inherit"
        variant="span"
        height={12}
        size={10}
        className={cnj(classes.eventLabel)}
      >
        {event.title}
      </Typography>

      <Typography
        color="inherit"
        variant="span"
        height={12}
        size={10}
        className={cnj(classes.eventLabel)}
      >
        {timestamp}
      </Typography>
    </p>
  );
}
