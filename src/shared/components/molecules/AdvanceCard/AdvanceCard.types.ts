import type { ReactNode } from 'react';
import type { AvatarProps } from 'shared/uikit/Avatar';
import type { TypographyProps } from 'shared/uikit/Typography';

export interface AdvanceCardProps {
  data: {
    firstText?: string;
    firstTextProps?: TextProps;
    firstTextHelper?: string;
    firstTextHelperProps?: TextProps;
    firstTextAdditionalProps?: any;
    secondText?: string;
    secondTextProps?: TextProps;
    secondTextAdditionalProps?: any;
    secondTextHelper?: string;
    secondTextHelperProps?: TextProps;
    secondTextSecondHelper?: string;
    secondTextSecondHelperProps?: TextProps;
    thirdText?: string;
    thirdTextProps?: TextProps;
    thirdTextAdditionalProps?: any;
    thirdTextHelper?: string;
    thirdTextHelperProps?: TextProps;
    fourthTextHelper?: string;
    firstTextDotHelper?: Array<string>;
    fourthTextDotHelper?: Array<string>;
    fourthText?: string;
    fourthTextProps?: TextProps;
    fourthTextAdditionalProps?: any;
    badgeText?: string;
    badgeTextProps?: TextProps;
    longText?: string;
    image?: any;
    objectId?: string;
    username?: string;
  };
  avatarProps?: AvatarProps;
  firstTextProps?: TextProps;
  firstTextHelperProps?: TextProps;
  secondTextProps?: TextProps;
  secondTextHelperProps?: TextProps;
  secondTextSecondHelperProps?: TextProps;
  thirdTextHelperProps?: TextProps;
  thirdTextProps?: TextProps;
  fourthTextProps?: TextProps;
  badgeTextProps?: TextProps;
  action?: React.ReactNode;
  bottomAction?: React.ReactNode;
  isSubCard?: boolean;
  visibleDivider?: boolean;
  classNames?: Partial<ClassNameProps>;
  visibleLine?: boolean;
  visibleDot?: boolean;
  disabledContent?: boolean;
  isCompany?: boolean;
  onClick?: (e: any) => void;
  visibleImage?: boolean;
  onClickAvatar?: (arg: any) => void;
  imageDim?: number;
  renderRight?: () => ReactNode;
  ObjectInfoCard?: React.ReactElement;
  showBrief?: boolean;
}

export type TextProps = Omit<TypographyProps, 'children'> & {
  objectId?: string;
};

export type ClassNameProps = {
  root?: string;
  image?: string;
  innerRoot?: string;
  thirdTextWrapperClassName?: string;
  fourthTextWrapperClassName?: string;
  longTextWrapperClassName?: string;
};
