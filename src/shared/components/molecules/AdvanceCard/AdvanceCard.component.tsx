import React, { Fragment, useMemo, ReactNode } from 'react';
import type { JSX } from 'react';
import merge from 'lodash/merge';
import type { AvatarProps } from 'shared/uikit/Avatar';
import Avatar from 'shared/uikit/Avatar';
import cnj from 'shared/uikit/utils/cnj';
import Divider from 'shared/uikit/Divider';
import Flex from 'shared/uikit/Flex';
import RichTextView from 'shared/uikit/RichText/RichTextView';
import Typography from 'shared/uikit/Typography';
import OverflowTip from '@shared/uikit/Typography/OverflowTip';
import ObjectLink from 'shared/uikit/Link/ObjectLink';
import type { TypographyProps } from 'shared/uikit/Typography';
import { makeDynamicStyles } from 'shared/uikit/utils/makeDynamicStyles';
import { usePrefetchRoutes } from 'shared/hooks/usePrefetchRoutes';
import { useObjectClicks } from 'shared/hooks/useObjectClicks';
import useTranslation from 'shared/utils/hooks/useTranslation';
import DividerDot from 'shared/uikit/Divider/Divider.dot';
import BaseButton from 'shared/uikit/Button/BaseButton';
import BulletLine from './BulletLine';
import { useGlobalSearchUtilities } from '../GlobalSearchInput';
import type { AdvanceCardProps } from './AdvanceCard.types';
import classes from './AdvanceCard.component.module.scss';

const AdvanceCard = ({
  data,
  avatarProps,
  action,
  bottomAction,
  isSubCard,
  visibleDivider,
  classNames,
  visibleDot,
  visibleLine,
  disabledContent,
  isCompany,
  onClick,
  visibleImage = true,
  onClickAvatar,
  imageDim = 40,
  renderRight,
  ObjectInfoCard,
  showBrief,
  ...restOfProps
}: AdvanceCardProps): JSX.Element => {
  const {
    firstText,
    firstTextHelper,
    secondText,
    secondTextHelper,
    secondTextSecondHelper,
    thirdText,
    thirdTextHelper,
    fourthTextHelper,
    firstTextDotHelper,
    fourthText,
    badgeText,
    longText,
    image,
    objectId,
    firstTextAdditionalProps,
    secondTextAdditionalProps,
    thirdTextAdditionalProps,
    fourthTextAdditionalProps,
    fourthTextDotHelper,
    username,
    ...restOfData
  } = data;

  const {
    firstTextProps = {},
    firstTextHelperProps = {},
    secondTextProps = {},
    secondTextHelperProps = {},
    secondTextSecondHelperProps = {},
    thirdTextProps = {},
    thirdTextHelperProps = {},
    fourthTextProps = {},
    badgeTextProps = {},
  } = useMemo(() => merge(restOfProps, restOfData), [restOfProps, restOfData]);

  const { t } = useTranslation();
  // const { searchForText } = useGlobalSearchUtilities();
  const searchForText = console.log;

  const stopPropagation = (e: any) => e.stopPropagation();
  const ImageWrapper: any =
    firstTextAdditionalProps?.objectId || fourthTextAdditionalProps?.objectId
      ? ObjectLink
      : firstTextAdditionalProps?.isPageAnonymous ||
          fourthTextAdditionalProps?.isPageAnonymous
        ? BaseButton
        : Fragment;
  const imageWrapperProps = {
    username,
    objectId:
      firstTextAdditionalProps?.objectId || fourthTextAdditionalProps?.objectId,
    enableHover: true,
    onClick: () =>
      searchForText(
        firstTextAdditionalProps?.isPageAnonymous
          ? (firstText as string)
          : fourthTextAdditionalProps?.isPageAnonymous
            ? (fourthText as string)
            : ''
      ),
  };

  const FirstTextWrapper: any = firstTextAdditionalProps?.objectId
    ? ObjectLink
    : firstTextAdditionalProps?.isPageAnonymous
      ? BaseButton
      : Fragment;
  const FourthTextWrapper: any = fourthTextAdditionalProps?.objectId
    ? ObjectLink
    : fourthTextAdditionalProps?.isPageAnonymous
      ? BaseButton
      : Fragment;
  const firstTextWrapperProps =
    firstTextAdditionalProps?.objectId ||
    firstTextAdditionalProps?.isPageAnonymous
      ? {
          username: firstTextAdditionalProps?.username,
          objectId: firstTextAdditionalProps?.objectId,
          enableHover: true,
          onClick: () => searchForText(firstText as string),
          underlineOnHover: true,
          className: classes.flexStart,
        }
      : {};
  const SecondTextWrapper: any = secondTextAdditionalProps?.objectId
    ? ObjectLink
    : Fragment;
  const secondTextWrapperProps = secondTextAdditionalProps?.objectId
    ? { objectId: secondTextAdditionalProps?.objectId }
    : {};
  const ThirdTextWrapper: any = thirdTextAdditionalProps?.objectId
    ? ObjectLink
    : Fragment;
  const thirdTextWrapperProps = thirdTextAdditionalProps?.objectId
    ? { objectId: thirdTextAdditionalProps?.objectId }
    : {};
  const FourthTextAdditionalProps =
    fourthTextAdditionalProps?.objectId ||
    fourthTextAdditionalProps?.isPageAnonymous
      ? {
          className: classes.fourthTextLink,
          objectId: fourthTextAdditionalProps?.objectId,
          enableHover: true,
          onClick: () => searchForText(fourthText as string),
          underlineOnHover: true,
        }
      : {};

  const { handleTagClick, handleHashtagClick, hoveredHashtag, onHashtagHover } =
    useObjectClicks();

  usePrefetchRoutes([`/${username}`]);

  return (
    <Flex
      className={cnj(
        classes.rootTimelineItem,
        isSubCard && classes.rootTimelineItemSub,
        classNames?.root
      )}
    >
      <Flex className={cnj(classNames?.innerRoot, classes.timeLineItem)}>
        {visibleImage && (
          <Flex
            className={cnj(
              classes.contentElement,
              classes.imageWrapper,
              classNames?.image
            )}
            {...makeDynamicStyles({
              width: imageDim,
              height: imageDim,
            })}
          >
            {isSubCard && <BulletLine {...{ visibleLine, visibleDot }} />}
            {!isSubCard ? (
              React.isValidElement(image) ? (
                image
              ) : (
                <ImageWrapper
                  {...(ImageWrapper === Fragment ? {} : imageWrapperProps)}
                >
                  <Avatar
                    onClick={onClickAvatar}
                    size="smd"
                    imgSrc={image}
                    isCompany={isCompany}
                    {...avatarProps}
                  />
                </ImageWrapper>
              )
            ) : null}
          </Flex>
        )}
        <Flex
          onClick={onClick}
          className={cnj(
            classes.infoWrapper,
            onClick && classes.infoWrapperWithOnclick
          )}
        >
          <Flex className={classes.rowWrapper}>
            {ObjectInfoCard || (
              <Flex className="w-full">
                {firstText && (
                  <Flex
                    className={cnj(classes.rowWrapper, '!flex')}
                    style={
                      !isSubCard
                        ? {
                            marginTop: -3,
                          }
                        : undefined
                    }
                  >
                    <FirstTextWrapper
                      {...(FirstTextWrapper === Fragment
                        ? {}
                        : firstTextWrapperProps)}
                    >
                      <OverflowTip
                        font="700"
                        size={20}
                        height={23}
                        color="smoke_coal"
                        {...firstTextProps}
                        className={cnj(
                          classes.contentElement,
                          firstTextProps?.className
                        )}
                      >
                        {firstText}
                      </OverflowTip>
                    </FirstTextWrapper>
                    {!!firstTextDotHelper?.length &&
                      firstTextDotHelper?.map((item, idx) => (
                        <Fragment key={item}>
                          <DividerDot className={classes.dotDivider} />
                          <OverflowTip
                            color="primaryText"
                            font="400"
                            size={14}
                            height={16}
                            preventTruncation={
                              idx !== firstTextDotHelper.length - 1
                            }
                          >
                            {item}
                          </OverflowTip>
                        </Fragment>
                      ))}
                    {badgeText && (
                      <Flex className={classes.badgeTextWrapper}>
                        <Typography
                          font="700"
                          size={15}
                          height={23}
                          color="success"
                          {...badgeTextProps}
                          className={cnj('text-now', badgeTextProps?.className)}
                        >
                          {badgeText}
                        </Typography>
                      </Flex>
                    )}
                  </Flex>
                )}
                {firstTextHelper && <Flex className={classes.vDivider} />}
                {firstTextHelper && (
                  <OverflowTip
                    font="400"
                    size={15}
                    height={23}
                    color="smoke_coal"
                    {...firstTextHelperProps}
                  >
                    {firstTextHelper}
                  </OverflowTip>
                )}
                <Flex className={classes.contentElement}>
                  {secondText && (
                    <Flex
                      className={cnj(
                        classes.row,
                        classes.marginTop,
                        showBrief && 'flex-wrap'
                      )}
                    >
                      <SecondTextWrapper
                        {...(SecondTextWrapper === Fragment
                          ? {}
                          : secondTextWrapperProps)}
                      >
                        <OverflowTip
                          font="400"
                          size={14}
                          height={16}
                          color="secondaryDisabledText"
                          isWordWrap
                          {...secondTextProps}
                          className={secondTextProps?.className}
                        >
                          {secondText}
                        </OverflowTip>
                      </SecondTextWrapper>
                      {secondTextHelper && (
                        <>
                          <Flex className={cnj(classes.vDivider)} />
                          <OverflowTip
                            font="400"
                            size={14}
                            height={16}
                            color="secondaryDisabledText"
                            preventTruncation={!!secondTextSecondHelper}
                            {...secondTextHelperProps}
                          >
                            {secondTextHelper}
                          </OverflowTip>
                        </>
                      )}
                      {secondTextSecondHelper && (
                        <>
                          <Flex className={cnj(classes.vDivider)} />
                          <OverflowTip
                            font="400"
                            size={14}
                            height={16}
                            color="secondaryDisabledText"
                            {...secondTextSecondHelperProps}
                          >
                            {t(secondTextSecondHelper)}
                          </OverflowTip>
                        </>
                      )}
                    </Flex>
                  )}
                  {(thirdText || fourthText) && (
                    <Flex
                      className={cnj(
                        classes.largeResponsiveGutter,
                        classNames?.thirdTextWrapperClassName
                      )}
                    >
                      <Flex className={classes.row}>
                        <ThirdTextWrapper
                          {...(ThirdTextWrapper === Fragment
                            ? {}
                            : thirdTextWrapperProps)}
                        >
                          <OverflowTip
                            font="400"
                            size={12}
                            height={18}
                            color="secondaryDisabledText"
                            {...thirdTextProps}
                          >
                            {thirdText}
                          </OverflowTip>
                        </ThirdTextWrapper>
                        {thirdTextHelper && (
                          <>
                            <Flex className={classes.vDivider} />
                            <Typography
                              font="400"
                              size={12}
                              height={18}
                              color="secondaryDisabledText"
                              {...thirdTextHelperProps}
                            >
                              {thirdTextHelper}
                            </Typography>
                          </>
                        )}
                      </Flex>
                      {fourthText && (
                        <Flex className={classes.rowWrapper}>
                          <OverflowTip
                            font="700"
                            size={16}
                            height={19}
                            color="smoke_coal"
                            mt={thirdText ? 8 : 0}
                            {...fourthTextProps}
                            className={cnj(fourthTextProps.className)}
                          >
                            <FourthTextWrapper
                              {...(FourthTextWrapper === Fragment
                                ? {}
                                : FourthTextAdditionalProps)}
                            >
                              {fourthText}
                            </FourthTextWrapper>
                          </OverflowTip>
                          {!!fourthTextDotHelper?.length &&
                            fourthTextDotHelper?.map((item, idx) => (
                              <Fragment key={item}>
                                <DividerDot className={classes.dotDivider} />
                                <OverflowTip
                                  color="primaryText"
                                  font="400"
                                  size={14}
                                  height={16}
                                  preventTruncation={
                                    idx !== fourthTextDotHelper.length - 1
                                  }
                                >
                                  {item}
                                </OverflowTip>
                              </Fragment>
                            ))}
                        </Flex>
                      )}
                    </Flex>
                  )}
                </Flex>
              </Flex>
            )}
            <Flex className={classes.editIcon}>{action}</Flex>
          </Flex>

          <Flex
            className={cnj(
              classes.contentElement,
              disabledContent && classes.disabled
            )}
          >
            {longText && (
              <RichTextView
                className={cnj(
                  classes.description,
                  classNames?.longTextWrapperClassName
                )}
                html={longText}
                typographyProps={{
                  color: 'thirdText',
                }}
                showMore
                onMentionClick={handleTagClick}
                onHashtagClick={handleHashtagClick}
                onHashtagHover={onHashtagHover}
                hoveredHashtag={hoveredHashtag}
                row={5}
              />
            )}
          </Flex>
        </Flex>
        {renderRight?.()}
      </Flex>
      <Flex as="span" onClick={stopPropagation}>
        {bottomAction}
      </Flex>
      {visibleDivider && <Divider className={classes.largeResponsiveGutter} />}
    </Flex>
  );
};

export default AdvanceCard;
