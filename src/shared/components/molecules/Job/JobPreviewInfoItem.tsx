import InfoCard from 'shared/components/Organism/Objects/Common/InfoCard';
import type { IconName } from 'shared/uikit/Icon/types';
import cnj from 'shared/uikit/utils/cnj';
import type { FC } from 'react';
import classes from './JobStyles.module.scss';

interface JobPreviewInfoItemProps {
  title: string;
  subTitle: string;
  icon: IconName;
  noRadius?: boolean;
}

const JobPreviewInfoItem: FC<JobPreviewInfoItemProps> = (props) => {
  const { noRadius = true, ...rest } = props;

  return (
    <InfoCard
      {...rest}
      disabledHover
      wrapperClassName={cnj(classes.infoCard, noRadius && classes.noRadius)}
      valueProps={{ color: 'primaryText' }}
    />
  );
};

export default JobPreviewInfoItem;
