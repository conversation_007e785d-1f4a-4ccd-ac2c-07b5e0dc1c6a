import type { colorsKeys } from 'shared/uikit/helpers/theme';
import type { JobStatusType } from 'shared/types/jobsProps';
import type { FC, PropsWithChildren } from 'react';
import Flex from 'shared/uikit/Flex';
import type { IconProps } from 'shared/uikit/Icon/types';
import Icon from 'shared/uikit/Icon';
import Typography from 'shared/uikit/Typography';
import relativeTime from 'dayjs/plugin/relativeTime';
import dayjs from 'dayjs';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Tooltip from '@shared/uikit/Tooltip';
import type { TooltipProps } from '@shared/uikit/Tooltip/Tooltip';
import type { PriorityType } from '@shared/types/pipelineTypes';
import classes from './CardBadges.module.scss';

dayjs.extend(relativeTime);

export interface CardBadgesProps {
  time?: string;
  status?: JobStatusType;
  priority?: PriorityType;
  collaboratorsCount?: number;
  candidatesCount?: number;
  applicantsCount?: number;
  notesCount?: number;
  todosCount?: number;
  meetingsCount?: number;
  jobsCount?: number;
}

const CardBadges: FC<CardBadgesProps> = (props) => {
  const {
    applicantsCount,
    candidatesCount,
    collaboratorsCount,
    meetingsCount,
    notesCount,
    priority,
    status,
    time,
    todosCount,
    jobsCount,
  } = props;
  const { t } = useTranslation();
  return (
    <Flex className={classes.badges}>
      {!!status && (
        <BottomInfoItem
          iconProps={{
            name: 'circle-s',
            type: 'far',
            color: 'brand',
          }}
          tooltipProps={{
            children: t('status'),
          }}
        >
          {t(status.toLowerCase())}
        </BottomInfoItem>
      )}
      {!!priority && (
        <BottomInfoItem
          iconProps={{
            name: 'double_line',
            type: 'far',
            color: iconColor[priority as PriorityType],
          }}
          tooltipProps={{
            children: t('priority'),
          }}
        >
          {t((priority ?? '').toLowerCase())}
        </BottomInfoItem>
      )}
      {!!jobsCount && (
        <BottomInfoItem
          iconProps={{
            name: 'briefcase-blank-light',
            type: 'far',
            color: 'primaryText',
            size: 16,
          }}
          tooltipProps={{
            children: t('job'),
          }}
        >
          {jobsCount}
        </BottomInfoItem>
      )}
      {!!collaboratorsCount && (
        <BottomInfoItem
          iconProps={{
            name: 'user-collaborator',
            type: 'far',
            color: 'primaryText',
            size: 16,
          }}
          tooltipProps={{
            children: t('collaborator'),
          }}
        >
          {collaboratorsCount}
        </BottomInfoItem>
      )}
      {!!candidatesCount && (
        <BottomInfoItem
          iconProps={{
            name: 'user-candidates',
            type: 'far',
            color: 'primaryText',
            size: 16,
          }}
          tooltipProps={{
            children: t('candidate'),
          }}
        >
          {candidatesCount}
        </BottomInfoItem>
      )}
      {!!applicantsCount && (
        <BottomInfoItem
          iconProps={{
            name: 'user-applicants',
            type: 'far',
            color: 'primaryText',
            size: 16,
          }}
          tooltipProps={{
            children: t('applicant'),
          }}
        >
          {applicantsCount}
        </BottomInfoItem>
      )}
      {!!notesCount && (
        <BottomInfoItem
          iconProps={{
            name: 'note',
            type: 'far',
            color: 'primaryText',
            size: 16,
          }}
          tooltipProps={{
            children: t('note'),
          }}
        >
          {notesCount}
        </BottomInfoItem>
      )}
      {!!todosCount && (
        <BottomInfoItem
          iconProps={{
            name: 'checklist',
            type: 'far',
            color: 'primaryText',
            size: 16,
          }}
          tooltipProps={{
            children: t('checklist'),
          }}
        >
          {todosCount}
        </BottomInfoItem>
      )}
      {!!meetingsCount && (
        <BottomInfoItem
          iconProps={{
            name: 'meeting',
            type: 'far',
            color: 'primaryText',
            size: 16,
          }}
          tooltipProps={{
            children: t('meeting'),
          }}
        >
          {meetingsCount}
        </BottomInfoItem>
      )}
      {!!time && (
        <Typography className={classes.counterDate}>
          {dayjs(time).fromNow()}
        </Typography>
      )}
    </Flex>
  );
};

export default CardBadges;

interface BottomInfoItemProps extends PropsWithChildren {
  iconProps: IconProps;
  tooltipProps?: TooltipProps;
}

const BottomInfoItem: FC<BottomInfoItemProps> = ({
  iconProps,
  tooltipProps,
  children,
}) => (
  <Tooltip
    {...tooltipProps}
    placement="top"
    disabled={!tooltipProps?.children || tooltipProps?.disabled}
    trigger={
      <Flex className={classes.bottomInfoRoot}>
        <Flex className={classes.bottomInfoIcon}>
          <Icon {...iconProps} size={iconProps?.size ?? 12} />
        </Flex>
        <Typography height={16}>{children}</Typography>
      </Flex>
    }
  >
    {tooltipProps?.children}
  </Tooltip>
);

const iconColor: { [key in PriorityType]: colorsKeys } = {
  CRITICAL: 'error',
  HIGH: 'darkError',
  MEDIUM: 'pendingOrange',
  LOW: 'lightGreen',
};
