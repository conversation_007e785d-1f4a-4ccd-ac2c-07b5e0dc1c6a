import React, {
  type ComponentProps,
  type FC,
  type PropsWithChildren,
} from 'react';
import cnj from 'shared/uikit/utils/cnj';
import Flex from '@shared/uikit/Flex';
import classes from './CandidateCard.module.scss';
import CandidateInfoCard from './CandidateInfoCard';
import CardWrapper from '../CardItem/CardWrapper';

export interface CandidateCardClassNamesProps {
  root?: string;
  header?: string;
  avatar?: string;
  infoCard?: string;
}

export interface CandidateCardProps
  extends ComponentProps<typeof CandidateInfoCard> {
  onClick?: VoidFunction;
  isFocused?: boolean;
  classNames?: CandidateCardClassNamesProps;
  footer?: React.ReactNode;
  enableLinks?: boolean;
  treeDotMenu?: React.ReactElement;
  topRightActionComponent?: React.ReactElement;
}

const CandidateCard: FC<PropsWithChildren<CandidateCardProps>> = (props) => {
  const {
    classNames,
    onClick,
    isFocused,
    footer,
    children,
    FirstTextWrapper,
    enableLinks,
    treeDotMenu,
    topRightActionComponent,
    ...objectInfoProps
  } = props;

  const showActions = !!treeDotMenu;

  return (
    <CardWrapper
      bottomComponents={footer}
      isFocused={isFocused}
      onClick={onClick}
      classNames={{
        root: cnj(classes.root, classNames?.root, showActions && 'group'),
        container: classes.container,
      }}
    >
      <Flex className={cnj(classes.header, classNames?.header)}>
        <CandidateInfoCard
          {...objectInfoProps}
          FirstTextWrapper={FirstTextWrapper}
          actions={
            <Flex>
              {showActions && (
                <span
                  className={cnj(
                    'sm:hidden sm:group-hover:inline-flex inline-flex'
                  )}
                >
                  {treeDotMenu}
                </span>
              )}
            </Flex>
          }
          isSecondTextSmall
        />
        {topRightActionComponent}
      </Flex>
      {children}
    </CardWrapper>
  );
};

export default CandidateCard;
