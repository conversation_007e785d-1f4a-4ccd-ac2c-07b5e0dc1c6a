import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import type { IconProps } from '@shared/uikit/Icon/types';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { FC } from 'react';

export interface ConditionChangerBodyProps {
  isOpen: boolean;
  onClick: React.MouseEventHandler<HTMLDivElement>;
  value?: { value: string; label: string };
  iconProps?: IconProps;
}

const ConditionChangerBody: FC<ConditionChangerBodyProps> = (props) => {
  const { isOpen, onClick, value, iconProps } = props;
  const { t } = useTranslation();
  return (
    <Flex
      onClick={onClick}
      className="py-4 px-6 !flex-row items-center gap-4 border-[1px] border-techGray_20 border-solid rounded"
    >
      <Flex style={{ justifyContent: 'center', alignItems: 'center' }}>
        <Icon {...iconProps} name="circle-s" type="far" size={12} />
      </Flex>
      <Typography height={16}>{value ? t(value.label ?? '') : ''}</Typography>
      <IconButton
        type="far"
        name={isOpen ? 'chevron-up' : 'chevron-down'}
        size="tiny"
        className="ml-auto"
      />
    </Flex>
  );
};

export default ConditionChangerBody;
