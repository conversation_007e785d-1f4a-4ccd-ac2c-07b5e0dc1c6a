import Flex from '@shared/uikit/Flex';
import type { IconProps } from '@shared/uikit/Icon/types';
import Icon from '@shared/uikit/Icon';
import Typography from '@shared/uikit/Typography';
import type { FC } from 'react';

export interface ConditionChangerItemProps {
  isSelected: boolean;
  item: { value: string; label: string };
  iconProps?: IconProps;
}

const ConditionChangerItem: FC<ConditionChangerItemProps> = (props) => {
  const { item, isSelected, iconProps } = props;

  return (
    <Flex
      className={`!flex-row items-center gap-8 p-8 rounded ${isSelected ? 'bg-brand' : ''}`}
    >
      <Flex className="justify-center items-center border-[1px] border-white border-solid rounded-full">
        <Icon
          {...iconProps}
          name="circle-s"
          type="far"
          // color={iconColor[item.value]}
          size={16}
        />
      </Flex>
      <Typography>{item.label}</Typography>
    </Flex>
  );
};

export default ConditionChangerItem;
