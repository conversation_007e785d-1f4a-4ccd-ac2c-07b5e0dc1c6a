import Flex from '@shared/uikit/Flex';
import { useRef, type FC } from 'react';
import type { JobStatusType, SingleJobAPIProps } from 'shared/types/jobsProps';
import useTranslation from '@shared/utils/hooks/useTranslation';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import Tooltip from '@shared/uikit/Tooltip';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import jobsApi from 'shared/utils/api/jobs';
import useToast from '@shared/uikit/Toast/useToast';
import { QueryKeys } from '@shared/utils/constants';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import DateView from '@shared/uikit/DateView/DateView.component';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import useRecruiterJobMoreActions from '@shared/hooks/useRecruiterJobMoreActions';
import type { PriorityType } from '@shared/types/pipelineTypes';
import type { CardBadgeOptionProps } from '../../CardBadge/CardBadge';
import CardBadge from '../../CardBadge/CardBadge';
import useBusinessJobCard from './useBusinessJobCard';
import type { BaseBusinessJobCardProps } from '../BaseBusinessJobCard';

dayjs.extend(relativeTime);

export interface BadgeActionProps {
  onApplicantsClick: VoidFunction;
  onCandidatesClick: VoidFunction;
  onAssigneesClick: VoidFunction;
}

interface BusinessJobCardBadgesProps {
  time?: string;
  status: JobStatusType;
  priority: PriorityType;
  collaboratorsCount?: number;
  candidatesCount?: number;
  applicantsCount?: number;
  inList?: boolean;
  jobId: string;
  showBrief?: boolean;
  handleRedirect?: VoidFunction;
  badgeActions?: BadgeActionProps;
  allProps: BaseBusinessJobCardProps;
  handleUpdateCache?: (condition: string, value: string) => void;
}

const BusinessJobCardBadges: FC<BusinessJobCardBadgesProps> = (props) => {
  const {
    time,
    status,
    priority,
    applicantsCount,
    candidatesCount,
    collaboratorsCount,
    inList,
    jobId,
    showBrief = false,
    handleRedirect,
    badgeActions,
    allProps,
    handleUpdateCache,
  } = props;
  const { t } = useTranslation();
  const toast = useToast();
  const queryClient = useQueryClient();
  const originalConditionRef = useRef('');
  const { onAction } = useRecruiterJobMoreActions({
    exclude: ['submit_to_vendor'],
  });

  const {
    priorityIconDetails,
    priorityOptions,
    statusIconDetails,
    statusOptions,
  } = useBusinessJobCard();
  const { handleChangeParams } = useCustomParams();

  const { mutate: changeStatusMutate } = useMutation({
    mutationFn: jobsApi.setStatus,
    onSuccess: () => {
      toast({
        type: 'success',
        icon: 'check-circle',
        message: translateReplacer(t('condition_changed_successfully'), [
          t('status'),
        ]),
      });
      handleChangeParams({
        add: { refresh: 'true' },
      });
    },
    onError: () => {
      handleChangeCondition('status', '', 'remove');
      toast({
        type: 'error',
        icon: 'times-circle',
        message: 'unknown_error',
      });
    },
  });
  const { mutate: onChangePriority } = useMutation({
    mutationFn: jobsApi.setPriority,
    onSuccess: () => {
      toast({
        type: 'success',
        icon: 'check-circle',
        message: translateReplacer(t('condition_changed_successfully'), [
          t('priority'),
        ]),
      });
      handleChangeParams({
        add: { refresh: 'true' },
      });
    },
    onError: () => {
      handleChangeCondition('priority', '', 'remove');
      toast({
        type: 'error',
        icon: 'times-circle',
        message: 'unknown_error',
      });
    },
  });

  const handleChangeCondition = (
    condition: string,
    value: string,
    type: 'add' | 'remove'
  ) => {
    const jobDetails = queryClient.getQueryData([
      QueryKeys.jobDetails,
      String(jobId),
    ]) as { data: SingleJobAPIProps };
    if (type === 'add') {
      if (jobDetails) {
        originalConditionRef.current = (jobDetails.data as any)[condition];
        queryClient.setQueryData([QueryKeys.jobDetails, String(jobId)], {
          data: { ...jobDetails.data, [condition]: value },
        });
      }
      handleUpdateCache?.(condition, value);
    } else {
      if (jobDetails) {
        queryClient.setQueryData([QueryKeys.jobDetails, String(jobId)], {
          data: {
            ...jobDetails.data,
            [condition]: originalConditionRef.current,
          },
        });
        originalConditionRef.current = '';
      }
      handleUpdateCache?.(condition, originalConditionRef.current);
    }
  };

  const onStatusChanged = (_status: CardBadgeOptionProps<JobStatusType>) => {
    handleChangeCondition('status', _status.value, 'add');
    changeStatusMutate({ id: jobId, status: _status.value as any });
  };

  const onStatusSelect = (_status: CardBadgeOptionProps<JobStatusType>) => {
    if (_status.value === 'CLOSED') {
      const job = {
        ...allProps,
        pageCroppedImageUrl: allProps?.image,
        title: allProps?.title,
        pageTitle: allProps?.title,
        categoryName: allProps?.category,
        location: { title: allProps?.location } as any,
        ownerFullName: allProps?.creator?.name,
        ownerUsername: allProps?.creator?.username,
        createdDate: allProps?.createdAt,
        lastModifiedDate: String(allProps?.lastUpdate),
      };
      return openMultiStepForm({
        formName: 'deleteEntityModal',
        data: job,
        variant: 'closeJob',
      });
    }
    return onStatusChanged(_status);
  };
  const onOpenEditModal = () => onAction('edit', { id: jobId } as any);

  return (
    <Flex className="!flex-row flex-wrap gap-20">
      <Flex
        className={`gap-${inList ? '4' : '8'} !flex-row`}
        onClick={handleRedirect}
      >
        <CardBadge
          onChange={onStatusSelect}
          value={
            inList || status === 'UNPUBLISHED'
              ? t(status.toLowerCase())
              : { value: status, label: t(status.toLowerCase()) }
          }
          options={statusOptions}
          iconsDetails={
            inList || status === 'UNPUBLISHED'
              ? {
                  iconSize: 12,
                  iconColor: statusIconDetails.properties?.[status].color,
                  iconName: 'circle-s',
                }
              : statusIconDetails
          }
          name="job_status"
          tooltipProps={{
            children: t('status'),
          }}
          onClick={inList ? undefined : onOpenEditModal}
        />
        {priority && (
          <CardBadge
            onChange={(_priority) => {
              handleChangeCondition('priority', _priority.value, 'add');
              onChangePriority({ id: jobId, priority: _priority.value });
            }}
            value={
              inList
                ? t(priority.toLowerCase())
                : { value: priority, label: t(priority.toLowerCase()) }
            }
            options={priorityOptions}
            iconsDetails={
              inList
                ? {
                    iconColor: priorityIconDetails.properties?.[priority].color,
                    iconName: priorityIconDetails.properties?.[priority].name,
                  }
                : priorityIconDetails
            }
            name="job_priority"
            tooltipProps={{
              children: t('priority'),
            }}
          />
        )}
        {!showBrief && (
          <>
            <CardBadge
              value={String(applicantsCount ?? 0)}
              iconsDetails={{ iconName: 'user-candidates' }}
              tooltipProps={{
                children: t('applicants'),
              }}
              onClick={badgeActions?.onApplicantsClick}
            />
            <CardBadge
              value={String(candidatesCount ?? 0)}
              iconsDetails={{ iconName: 'user-collaborator' }}
              tooltipProps={{
                children: t('candidates'),
              }}
              onClick={badgeActions?.onCandidatesClick}
            />
            <CardBadge
              value={String(collaboratorsCount ?? 0)}
              iconsDetails={{ iconName: 'assignees' }}
              tooltipProps={{
                children: t('assignees'),
              }}
              onClick={badgeActions?.onAssigneesClick}
            />
          </>
        )}
      </Flex>
      {!!time && (
        <Tooltip
          trigger={
            <DateView value={time} size={12} color="secondaryDisabledText" />
          }
          triggerWrapperClassName="ml-auto"
          disabled={inList && !showBrief}
          placement="top"
        >
          {t('latest_activity')}
        </Tooltip>
      )}
    </Flex>
  );
};

export default BusinessJobCardBadges;
