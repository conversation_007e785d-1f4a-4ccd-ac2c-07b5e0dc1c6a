import type { JobStatusType } from 'shared/types/jobsProps';
import type {
  CardBadgeIconsDetailsProps,
  CardBadgeOptionProps,
} from 'shared/components/molecules/CardBadge';
import useTranslation from '@shared/utils/hooks/useTranslation';
import {
  priorityIconDetails,
  priorityOptions,
} from '@shared/constants/priorityDetails';
import type { PriorityType } from '@shared/types/pipelineTypes';

const useBusinessJobCard = () => {
  const { t } = useTranslation();

  const statusOptions = jobStatusOptions.map((stOpt) => ({
    ...stOpt,
    label: t(stOpt.label.toLowerCase()),
  }));
  const priorityOptionsData = jobPriorityOptions.map((stOpt) => ({
    ...stOpt,
    label: t(stOpt.label.toLowerCase()),
  }));

  return {
    statusOptions,
    priorityOptions: priorityOptionsData,
    priorityIconDetails: jobPriorityIconDetails,
    statusIconDetails: jobStatusIconDetails,
  };
};

export default useBusinessJobCard;

const jobStatusOptions: Array<CardBadgeOptionProps<JobStatusType>> = [
  {
    value: 'OPEN',
    label: 'OPEN',
  },
  {
    value: 'ARCHIVE',
    label: 'archived',
  },
  {
    value: 'CLOSED',
    label: 'CLOSED',
  },
];

const jobStatusIconDetails: CardBadgeIconsDetailsProps<JobStatusType> = {
  iconClassName: 'border-[1px] border-white border-solid rounded-full',
  iconSize: 12,
  properties: {
    OPEN: {
      color: 'success',
    },
    CLOSED: {
      color: 'error',
    },
    ARCHIVE: {
      color: 'secondaryDisabledText',
    },
    UNPUBLISHED: {
      color: 'error',
    },
  },
};

const jobPriorityOptions = priorityOptions;

const jobPriorityIconDetails: CardBadgeIconsDetailsProps<PriorityType> = {
  properties: priorityIconDetails,
};
