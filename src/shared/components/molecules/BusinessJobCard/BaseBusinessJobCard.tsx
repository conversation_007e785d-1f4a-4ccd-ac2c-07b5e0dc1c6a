import { type PropsWithChildren, type FC, useMemo } from 'react';
import Flex from 'shared/uikit/Flex';
import formatDate from '@shared/utils/toolkit/formatDate';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Avatar from 'shared/uikit/Avatar';
import cnj from 'shared/uikit/utils/cnj';
import Typography from 'shared/uikit/Typography';
import type { JobStatusType } from 'shared/types/jobsProps';
import useHistory from '@shared/utils/hooks/useHistory';
import type { PriorityType } from '@shared/types/pipelineTypes';
import classes from './BusinessJobCard.module.scss';
import ObjectInfoCard from '../ObjectInfoCard';
import type { CardWrapperProps } from '../CardItem/CardWrapper';
import CardWrapper from '../CardItem/CardWrapper';
import type { BadgeActionProps } from './BaseBusinessJobCard/BusinessJobCardBadges';
import BusinessJobCardBadges from './BaseBusinessJobCard/BusinessJobCardBadges';
import { EntitiyPopper } from '../EntityPopper/EntitiyPopper';

export interface CommonBaseBusinessJobCardProps {
  cardProps?: CardWrapperProps;
  moreOptions?: React.ReactNode;
  hasRedirection?: boolean;
  actions?: React.ReactNode;
  badgeActions?: BadgeActionProps;
  tags?: React.ReactNode;
  creatorRedirect?: boolean;
}

export interface BaseBusinessJobCardClassNamesProps {
  header?: string;
  avatar?: string;
  infoCard?: string;
  actions?: string;
  moreOptions?: string;
}

export type BaseBusinessJobCardProps = CommonBaseBusinessJobCardProps &
  PropsWithChildren & {
    classNames?: BaseBusinessJobCardClassNamesProps;
    image: string;
    title: string;
    username: string;
    location: string;
    category: string;
    creator?: {
      name: string;
      username: string;
      role?: string;
    };
    projects?: string;
    createdAt?: string;
    lastUpdate?: string;
    status: JobStatusType;
    priority: PriorityType;
    applicantsCount?: number;
    candidatesCount?: number;
    collaboratorsCount?: number;
    showCounterDate?: boolean;
    inList?: boolean;
    id: string;
    showBrief?: boolean;
    projectIds?: string[];
  };

const BaseBusinessJobCard: FC<BaseBusinessJobCardProps> = (props) => {
  const {
    classNames,
    image,
    title,
    username,
    category,
    location,
    creator,
    projects,
    createdAt,
    lastUpdate,
    status,
    priority,
    applicantsCount,
    candidatesCount,
    collaboratorsCount,
    tags,
    showCounterDate = true,
    actions,
    children,
    cardProps,
    moreOptions,
    inList,
    id,
    showBrief,
    hasRedirection,
    projectIds = [],
    badgeActions,
    creatorRedirect = true,
  } = props;
  const { t } = useTranslation();
  const history = useHistory();
  const goToJobDetails = () => {
    if (!hasRedirection) return;
    history.push(`/search/my-jobs?currentEntityId=${id}`);
  };
  const goToUserProfile = (_username: string) => {
    if (!creatorRedirect) return;
    history.push(`/${_username}`);
  };
  const goToJobsWithProjects = () => {
    if (!hasRedirection) return;
    history.push(`/search/my-jobs?projectIds=${projectIds?.join(',')}`);
  };
  return (
    <CardWrapper {...cardProps} bottomComponents={children}>
      <Flex className={classes.container}>
        <Flex
          className={cnj(classes.header, classNames?.header)}
          onClick={goToJobDetails}
        >
          <Avatar
            imgSrc={image}
            className={cnj(classes.avatar, classNames?.avatar)}
            isCompany
            size="flg"
          />
          <ObjectInfoCard
            firstText={title}
            secondText={username}
            thirdText={category}
            fourthText={location}
            isFirstTextSmall
            isPage
            withAvatar={false}
            className={cnj(classes.infoCard, classNames?.infoCard)}
          />
          {!!moreOptions && (
            <Flex className={cnj(classes.moreOptions, classNames?.moreOptions)}>
              {moreOptions}
            </Flex>
          )}
        </Flex>
        {!!creator && !!createdAt && !!projects && (
          <Flex className={classes.infoBox}>
            <InfoItem
              title={t('creator')}
              value={creator.name}
              extraInfo={creator.role}
              onClick={() => goToUserProfile(creator.username)}
              className="cursor-pointer"
              username={!inList ? creator.username : undefined}
            />
            <InfoItem
              title={t('projects')}
              value={projects}
              onClick={goToJobsWithProjects}
            />
            <InfoItem
              title={t('created')}
              value={formatDate(createdAt, 'LL')}
            />
          </Flex>
        )}
        {tags}
        <BusinessJobCardBadges
          status={status}
          priority={priority}
          collaboratorsCount={collaboratorsCount}
          candidatesCount={candidatesCount}
          applicantsCount={applicantsCount}
          time={showCounterDate ? lastUpdate : undefined}
          jobId={id}
          inList={inList}
          showBrief={showBrief}
          handleRedirect={hasRedirection ? goToJobDetails : undefined}
          badgeActions={badgeActions}
          allProps={props}
        />
        {actions && <Flex className={classNames?.actions}>{actions}</Flex>}
      </Flex>
    </CardWrapper>
  );
};

export default BaseBusinessJobCard;

interface InfoItemProps {
  title: string;
  value: string;
  extraInfo?: string;
  onClick?: VoidFunction;
  className?: string;
  username?: string;
}

const InfoItem: FC<InfoItemProps> = ({
  title,
  value,
  extraInfo,
  onClick,
  className,
  username,
}) => {
  const Info = useMemo(
    () => (
      <Flex className="!flex-row items-center gap-8">
        <span>{value}</span>
        {extraInfo && (
          <>
            <Flex className={classes.divider} />
            <Typography color="secondaryDisabledText" font="400" size={14}>
              {extraInfo}
            </Typography>
          </>
        )}
      </Flex>
    ),
    [extraInfo, value]
  );
  const InfoWithDetails = useMemo(() => {
    if (username)
      return (
        <EntitiyPopper
          isPage={false}
          username={username}
          classNames={{ buttonWrapper: classes.avatar }}
          variant="business"
          // TODO: needs to be implemented later
          onClick={() =>
            // (username) => onClickAvatar(`/${username}`)
            alert('Needst to be implemented later!')
          }
          showRole
        >
          {Info}
        </EntitiyPopper>
      );
    return Info;
  }, [username, Info]);
  return (
    <Flex className={cnj(classes.infoItemRoot, className)} onClick={onClick}>
      <Typography className={classes.info} color="smoke_coal">
        {`${title}:`}
        {InfoWithDetails}
      </Typography>
    </Flex>
  );
};
