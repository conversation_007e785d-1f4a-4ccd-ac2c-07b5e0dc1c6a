import type { IconFontType, IconName } from '@shared/uikit/Icon/types';
import { useRef } from 'react';
import Button from 'shared/uikit/Button';
import Flex from 'shared/uikit/Flex';
import cnj from 'shared/uikit/utils/cnj';
import useHorizontalScroll from 'shared/uikit/utils/useHorizontalScroll';
import useTheme from 'shared/uikit/utils/useTheme';
import classes from './PilledTabBar.module.scss';

export interface PilledTabItem<T extends string> {
  id: T;
  label: string;
  icon?: IconName;
  iconType?: IconFontType;
}
export interface PilledTabBarProps<T extends string> {
  className?: string;
  list: PilledTabItem<T>[];
  state: T;
  onChange?: (next: T) => void;
}

function PilledTabBar<T extends string>({
  className,
  list,
  state,
  onChange,
}: PilledTabBarProps<T>) {
  const wrapperRef = useRef<HTMLElement>();
  const { isDark } = useTheme();
  const activeSituation = 'primary-blue';
  const inactiveSituation = isDark ? 'secondary-dark' : 'semi-transparent';
  const handleClickItem = (id: T) => () => onChange?.(id);
  useHorizontalScroll(wrapperRef);

  return (
    <Flex ref={wrapperRef} className={cnj(className, classes.root)}>
      {list.map(({ id, label, icon, iconType }) => (
        <Button
          key={id}
          schema={state === id ? activeSituation : inactiveSituation}
          leftType={iconType}
          leftIcon={icon}
          className={classes.button}
          label={label}
          onClick={handleClickItem(id)}
        />
      ))}
    </Flex>
  );
}

export default PilledTabBar;
