import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { EventCardPopper } from 'shared/components/molecules/EventCardPopper/EventCardPopper';
import { COLORS, COLORS_DARK } from 'shared/utils/consts';
import { useGetBrokenDays } from 'shared/hooks/useGetBrokenDays';
import { useGetEventRangeStatus } from 'shared/hooks/useGetEventRangeStatus';
import { getEventTitleOnBoard } from 'shared/utils/getEventTitleOnBoard';
import { makeDynamicStyles } from 'shared/uikit/utils/makeDynamicStyles';
import useMedia from '@shared/uikit/utils/useMedia';
import type { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { useSchedulesCalendar } from '../../../hooks/useSchedulesCalendar';
import type {
  Data,
  UnArray,
  DateType,
} from '../../../types/schedules/schedules';
import classes from './EventCard.week.module.scss';

type Props = {
  eventData: UnArray<Data['savedEvents']>;
  specs?: {
    height: number;
    top: number;
    left: number;
    width: number;
  };
  isAllDay?: boolean;
  isMultipleDay?: boolean;
  verticalStartingIndex?: number;
  dayInWeekIndex?: number;
  weekTime?: {
    startTime: DateType;
    endTime: DateType;
  };
};

const MAX_DAY_IN_WEEK_INDEX = 6;

export function EventCardWeek({
  eventData,
  specs,
  isAllDay,
  isMultipleDay,
  verticalStartingIndex,
  dayInWeekIndex,
  weekTime,
}: Props): JSX.Element {
  const { t } = useTranslation();
  const { isTabletAndLess } = useMedia();
  const eventLength = useGetBrokenDays(eventData)?.length;
  const {
    comingFromLastUnit: comingFromLastWeek,
    goesToNextUnit: goesToNextWeek,
    isEventEntirelyCoveringUnit: isEventEntirelyCoveringWeek,
  } = useGetEventRangeStatus({
    isMultipleDay,
    eventData,
    startTime: weekTime?.startTime,
    endTime: weekTime?.endTime,
  });
  const isEmpty = !eventData?.id;

  const { handleEventClick } = useSchedulesCalendar();
  const handleClick = (e: any) => {
    handleEventClick(e, eventData);
  };

  function getLength(): number {
    return isEventEntirelyCoveringWeek
      ? MAX_DAY_IN_WEEK_INDEX + 1
      : goesToNextWeek
        ? MAX_DAY_IN_WEEK_INDEX - dayInWeekIndex + 1
        : comingFromLastWeek
          ? dayInWeekIndex + 1
          : eventLength;
  }

  const isNormal = !isAllDay && !isMultipleDay;

  return (
    <Flex
      className={cnj(
        classes.eventCard,
        classes[
          `backgroundColor-${COLORS[eventData?.type as ScheduleEventTypes]}`
        ],
        classes[
          `backgroundColorHover-${COLORS_DARK[eventData?.type as ScheduleEventTypes]}`
        ],
        isEventEntirelyCoveringWeek && classes.bothRound,
        comingFromLastWeek && classes.startRound,
        goesToNextWeek && classes.endRound,
        isMultipleDay
          ? classes.isMultipleDay
          : isAllDay
            ? classes.isAllDay
            : classes.isNormal,
        isAllDay && isEmpty && classes.isAllDayEmpty
      )}
      {...(isNormal && makeDynamicStyles({ ...specs }))}
      {...(isMultipleDay &&
        makeDynamicStyles({
          width: `calc( ${eventLength * 100}% + ${eventLength * 1}px - 5px )`,
          top: (verticalStartingIndex || 0) * (MIN_HEIGHT + 2),
          ...(comingFromLastWeek && { left: 'unset', right: 2 }),
          ...(isEventEntirelyCoveringWeek && { left: 2, right: 'unset' }),
        }))}
      onClick={handleClick}
    >
      <EventCardPopper
        event={eventData}
        popperMenuProps={{ disablePortal: isTabletAndLess }}
      >
        <Typography
          size={10}
          font="500"
          color="white"
          isTruncated
          height={12}
          className={classes.title}
        >
          {getEventTitleOnBoard(eventData)}
        </Typography>
      </EventCardPopper>
    </Flex>
  );
}
export const MIN_HEIGHT = 16;
