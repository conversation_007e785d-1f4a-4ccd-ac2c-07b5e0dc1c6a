import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';

import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { EventCardPopper } from 'shared/components/molecules/EventCardPopper/EventCardPopper';
import { COLORS, COLORS_DARK } from 'shared/utils/consts';
import { checkIfAllDay } from 'shared/utils/checkIfAllDay';
import { useGetEventRangeStatus } from '@/shared/hooks/useGetEventRangeStatus';
import { getEventTitleOnBoard } from 'shared/utils/getEventTitleOnBoard';
import { makeDynamicStyles } from 'shared/uikit/utils/makeDynamicStyles';
import type { Data, UnArray, DateType } from 'shared/types/schedules/schedules';
import useMedia from '@shared/uikit/utils/useMedia';
import type { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { useSchedulesCalendar } from '../../../hooks/useSchedulesCalendar';
import { Time } from '../../../utils/Time';
import classes from './EventCard.day.module.scss';

type Props = {
  eventData: UnArray<Data['savedEvents']>;
  specs?: {
    height: number;
    top: number;
    width: number;
    left: number;
  };
  isAllDay?: boolean;
  isMultipleDay?: boolean;
  dayTime?: {
    startTime: DateType;
    endTime: DateType;
  };
};

export function EventCardDay({
  eventData,
  specs,
  isAllDay,
  isMultipleDay,
  dayTime,
}: Props): JSX.Element {
  const { t } = useTranslation();
  const { isTabletAndLess } = useMedia();

  const type = eventData?.type;

  const {
    comingFromLastUnit: comingFromLastDay,
    goesToNextUnit: goesToNextDay,
    isEventEntirelyCoveringUnit: isEventEntirelyCoveringDay,
  } = useGetEventRangeStatus({
    isMultipleDay,
    eventData,
    startTime: dayTime?.startTime,
    endTime: dayTime?.endTime,
  });

  const { handleEventClick } = useSchedulesCalendar();
  const handleClick = (e: any) => {
    handleEventClick(e, eventData);
  };

  const isNormal = !isAllDay && !isMultipleDay;

  const timeLabel = checkIfAllDay(eventData)
    ? t('all_day')
    : Time.getTimeLabel(
        eventData?.startTime,
        !Time.isBetween(
          eventData?.endTime,
          eventData?.startTime,
          Time.incrementDateBy(eventData?.startTime, 'hour', 0.25)
        ) && eventData?.endTime,
        isMultipleDay
      );
  return (
    <Flex
      className={cnj(
        classes.eventCard,
        classes[`backgroundColor-${COLORS[type as ScheduleEventTypes]}`],
        classes[
          `backgroundColorHover-${COLORS_DARK[type as ScheduleEventTypes]}`
        ],
        isNormal && classes.isNormal,
        isMultipleDay && classes.isMultipleDay,
        isAllDay && classes.isAllDay,
        isEventEntirelyCoveringDay && classes.bothRound,
        comingFromLastDay && classes.startRound,
        goesToNextDay && classes.endRound
      )}
      {...makeDynamicStyles({
        ...specs,
      })}
      onClick={handleClick}
    >
      <EventCardPopper
        event={eventData}
        popperMenuProps={{ disablePortal: isTabletAndLess }}
      >
        <Flex className={classes.wrapper}>
          <Typography
            size={10}
            font="500"
            color="white"
            isTruncated
            height={12}
            className={classes.title}
          >
            {getEventTitleOnBoard(eventData)}
          </Typography>
          <Typography
            size={10}
            font="400"
            color="white"
            isTruncated
            height={12}
            className={classes.time}
          >
            {timeLabel}
          </Typography>
        </Flex>
      </EventCardPopper>
    </Flex>
  );
}
