import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import React from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import { COLORS, COLORS_DARK } from 'shared/utils/consts';
import { useGetEventRangeStatus } from 'shared/hooks/useGetEventRangeStatus';
import { checkIfAllDay } from 'shared/utils/checkIfAllDay';
import { getEventTitleOnBoard } from 'shared/utils/getEventTitleOnBoard';
import type { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { Time } from '../../../utils/Time';
import { useSchedulesCalendar } from '../../../hooks/useSchedulesCalendar';
import type {
  Data,
  UnArray,
  DateType,
} from '../../../types/schedules/schedules';
import classes from './EventCard.year.module.scss';

type Props = {
  eventData: UnArray<Data['savedEvents']>;
  isMultipleDay?: boolean;
  startTime: DateType;
  endTime: DateType;
};

export function EventCardYear({
  eventData,
  isMultipleDay,
  startTime,
  endTime,
}: Props): JSX.Element {
  const { t } = useTranslation();

  const {
    comingFromLastUnit: comingFromLastWeek,
    goesToNextUnit: goesToNextWeek,
    isEventEntirelyCoveringUnit: isEventEntirelyCoveringWeek,
  } = useGetEventRangeStatus({
    isMultipleDay,
    eventData,
    startTime,
    endTime,
  });

  const { handleEventClick } = useSchedulesCalendar();
  const handleClick = (e: any) => {
    handleEventClick(e, eventData);
  };

  const timeLabel = checkIfAllDay(eventData)
    ? t('all_day')
    : Time.getTimeLabel(
        eventData?.startTime,
        !Time.isBetween(
          eventData?.endTime,
          eventData?.startTime,
          Time.incrementDateBy(eventData?.startTime, 'hour', 0.25)
        ) && eventData?.endTime,
        isMultipleDay
      );

  return (
    <Flex
      className={cnj(
        classes.eventCard,
        classes[
          `backgroundColor-${COLORS[eventData?.type as ScheduleEventTypes]}`
        ],
        classes[
          `backgroundColorHover-${COLORS_DARK[eventData?.type as ScheduleEventTypes]}`
        ],
        isEventEntirelyCoveringWeek && classes.bothRound,
        comingFromLastWeek && classes.startRound,
        goesToNextWeek && classes.endRound
      )}
      onClick={handleClick}
    >
      <Typography
        size={10}
        font="500"
        color="white"
        isTruncated
        height={12}
        className={classes.title}
      >
        {getEventTitleOnBoard(eventData)}
      </Typography>
      <Typography
        size={10}
        font="400"
        color="white"
        isTruncated
        height={14}
        className={classes.time}
      >
        {timeLabel}
      </Typography>
    </Flex>
  );
}
export const MIN_HEIGHT = 16;
