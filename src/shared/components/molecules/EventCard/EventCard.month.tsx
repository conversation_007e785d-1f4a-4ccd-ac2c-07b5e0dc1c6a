import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import cnj from 'shared/uikit/utils/cnj';
import type { Dispatch, SetStateAction } from 'react';
import React, { useCallback } from 'react';
import { EventCardPopper } from 'shared/components/molecules/EventCardPopper/EventCardPopper';
import { COLORS, COLORS_DARK } from 'shared/utils/consts';
import { useGetEventRangeStatus } from 'shared/hooks/useGetEventRangeStatus';
import { useGetBrokenDays } from 'shared/hooks/useGetBrokenDays';
import { getEventTitleOnBoard } from 'shared/utils/getEventTitleOnBoard';
import type { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { Time } from '../../../utils/Time';
import { useSchedulesCalendar } from '../../../hooks/useSchedulesCalendar';
import type {
  Data,
  UnArray,
  DateType,
} from '../../../types/schedules/schedules';
import classes from './EventCard.month.module.scss';

type Props = {
  eventData: UnArray<Data['savedEvents']>;
  startDate: DateType;
  endDate: DateType;
  isMultipleDay?: boolean;
  isEmpty?: boolean;
  hoveredEventUniqueId: string;
  setHoveredEventUniqueId: Dispatch<SetStateAction<string>>;
};

export function EventCardMonth({
  eventData,
  startDate,
  endDate,
  isMultipleDay,
  isEmpty,
  hoveredEventUniqueId,
  setHoveredEventUniqueId,
}: Props): JSX.Element {
  const { handleEventClick } = useSchedulesCalendar();
  const handleClick = (e: any) => {
    handleEventClick(e, eventData);
  };
  const isFirstDayOfWeek = startDate?.day() === 0;
  const isLastDayOfWeek = startDate?.day() === 6;
  const {
    comingFromLastUnit: comingFromLastDay,
    goesToNextUnit: goesToNextDay,
    isEventEntirelyCoveringUnit: isEventEntirelyCoveringDay,
  } = useGetEventRangeStatus({
    isMultipleDay,
    eventData,
    startTime: startDate,
    endTime: endDate,
  });

  const isBeingHovered = hoveredEventUniqueId === eventData?.uniqueEventId;

  const brokenDownEvents =
    useGetBrokenDays(
      eventData,
      isMultipleDay || !!Object.keys(eventData)?.length
    ) || [];
  const isFirstInMultiples =
    brokenDownEvents?.findIndex(
      (eve) => eve?.startTime && Time.areSameDay(eve?.startTime, startDate)
    ) === 0;

  const handleEnter = useCallback(() => {
    if (isBeingHovered) return;
    setHoveredEventUniqueId(eventData?.uniqueEventId);
  }, [isBeingHovered, setHoveredEventUniqueId, eventData]);

  const handleExit = useCallback(() => {
    if (!isBeingHovered) return;
    setHoveredEventUniqueId('');
  }, [isBeingHovered, setHoveredEventUniqueId]);
  return (
    <Flex
      className={cnj(
        classes.eventCard,
        classes[
          `backgroundColor-${COLORS[eventData?.type as ScheduleEventTypes]}`
        ],
        isEmpty && classes.eventCardEmpty,
        isBeingHovered &&
          classes[
            `backgroundColorHover-${COLORS_DARK[eventData?.type as ScheduleEventTypes]}`
          ],
        // isBeingHovered && classes.eventCardHover,
        isBeingHovered && isEmpty && classes.eventCardHoverEmpty,
        isMultipleDay && classes.eventCardMultipleDay,
        isMultipleDay &&
          (isFirstInMultiples || isFirstDayOfWeek) &&
          classes.eventCardzIndexHigh,
        isEventEntirelyCoveringDay &&
          !isFirstDayOfWeek &&
          !isLastDayOfWeek &&
          classes.eventCardCoveringDayNotFirstNotLast,
        isEventEntirelyCoveringDay &&
          isFirstDayOfWeek &&
          classes.eventCardCoveringDayIsFirst,
        isEventEntirelyCoveringDay &&
          isLastDayOfWeek &&
          classes.eventCardCoveringDayIsLast,
        goesToNextDay && classes.eventCardGoesNext,
        goesToNextDay && isLastDayOfWeek && classes.eventCardGoesNextIsLast,
        comingFromLastDay && classes.eventCardComingFromLast,
        comingFromLastDay &&
          isFirstDayOfWeek &&
          classes.eventCardComingFromLastIsFirst
      )}
      onClick={handleClick}
      onMouseEnter={handleEnter}
      onMouseLeave={handleExit}
    >
      <EventCardPopper event={eventData}>
        <Flex className={classes.insidePopperWrapper}>
          {(!isMultipleDay || isFirstInMultiples || isFirstDayOfWeek) && (
            <Typography
              size={10}
              font="500"
              color="white"
              isTruncated={!isMultipleDay}
              className={cnj(
                classes.title,
                isMultipleDay && classes.titleMultipleDay
              )}
            >
              {getEventTitleOnBoard(eventData)}
            </Typography>
          )}
        </Flex>
      </EventCardPopper>
    </Flex>
  );
}
