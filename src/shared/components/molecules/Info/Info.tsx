import React from 'react';
import Flex from 'shared/uikit/Flex';
import type { TextProps } from 'shared/uikit/Typography';
import Typography from 'shared/uikit/Typography';
import type { IconName } from 'shared/uikit/Icon/types';
import Icon from 'shared/uikit/Icon';
import cnj from 'shared/uikit/utils/cnj';
import type { colorsKeys } from 'shared/uikit/helpers/theme';
import classes from './Info.module.scss';

export interface InfoProps {
  className?: string;
  text: string;
  color?: colorsKeys;
  icon?: IconName;
  textColor?: colorsKeys;
  otherComponents?: React.ReactNode;
  classNames?: {
    text?: string;
  };
  textProps?: TextProps;
}

const Info = ({
  text,
  icon = 'info-circle',
  className,
  textColor,
  color = 'gray',
  otherComponents,
  classNames,
  textProps,
}: InfoProps) => (
  <Flex flexDir="row" className={cnj(classes.wrapper, className)}>
    <Flex className="!flex-row flex-1">
      <Icon name={icon} color={color} type="far" size={20} />
      <Typography
        size={14}
        height={18}
        font="400"
        color={textColor ?? color}
        ml={8}
        className={classNames?.text}
        {...textProps}
      >
        {text}
      </Typography>
    </Flex>
    {otherComponents}
  </Flex>
);

export default Info;
