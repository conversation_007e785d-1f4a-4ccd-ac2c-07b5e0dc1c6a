import React from 'react';
import Avatar from 'shared/uikit/Avatar';
import cnj from 'shared/uikit/utils/cnj';
import Flex from 'shared/uikit/Flex';
import IconButton from 'shared/uikit/Button/IconButton';
import QueryKeys from 'shared/utils/constants/queryKeys';
import event from 'shared/utils/toolkit/event';
import { mainRoutes, routeNames } from 'shared/utils/constants/routeNames';
import { useGlobalDispatch } from 'shared/contexts/Global/global.provider';
import useLocation from 'shared/utils/hooks/useLocation';
import eventKeys from 'shared/constants/event-keys';
import { isBusinessApp } from 'shared/utils/getAppEnv';
import useGetAppObject from 'shared/hooks/useGetAppObject';
import { useQueryClient } from '@tanstack/react-query';
import { useSchedulesCalendar } from '@shared/hooks/useSchedulesCalendar';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import BottomBarItem from './partials/BottomBarItem';
import classes from './index.module.scss';

const BottomBar = () => {
  const location = useLocation();
  const { pathname } = useLocation();
  const appDispatch = useGlobalDispatch();
  const isScheduleRoute = location?.pathname?.startsWith(mainRoutes.schedules);
  const queryClient = useQueryClient();
  const { getAppObjectPropValue } = useGetAppObject();

  const toggleGlobalEntityModal = () => {
    appDispatch({ type: 'TOGGLE_CREATE_ENTITY_PANEL' });
  };
  const scrollToTopFeedList = () => {
    event.trigger(eventKeys.scrollToTopFeedList);
  };
  const isActiveRoute = (route: string | string[]): boolean => {
    if (route === mainRoutes.home) {
      return pathname === mainRoutes.home;
    }
    return (typeof route === 'string' ? [route] : route)?.some(
      (theRoute: string) => pathname?.startsWith(theRoute)
    );
  };

  const { openCreateEventWithDate } = useSchedulesCalendar();
  const onCreateScheduleClick = () => {
    const schedulesEventType = pathname.includes(routeNames.schedulesTasks)
      ? ScheduleEventTypes.TASK
      : pathname.includes(routeNames.schedulesReminders)
        ? ScheduleEventTypes.REMINDER
        : ScheduleEventTypes.MEETING;
    openCreateEventWithDate(undefined, { schedulesEventType });
  };

  const AVATAR = {
    key: 'profileMenu',
    name: 'profileMenu',
    image: (
      <Avatar
        imgSrc={getAppObjectPropValue({
          userKey: 'croppedImageUrl',
          pageKey: 'croppedImageUrl',
        })}
        isCompany={isBusinessApp}
        size={isActiveRoute(routeNames.profileMenu) ? 'xxs' : 'xxs'}
        bordered
        avatarInnerClassName={cnj(
          isActiveRoute(routeNames.profileMenu) && classes.activeAvatar
        )}
      />
    ),
    link: routeNames.profileMenu,
    onClick: scrollToTopFeedList,
  };

  const schedulesList = [
    {
      key: 'schedulesCalendar',
      rootRoute: routeNames.schedulesCalendar,
      name: 'schedulesCalendar',
      icon: 'calender-line',
      link: routeNames.schedulesCalendarMonth,
      onClick: scrollToTopFeedList,
      replace: !isActiveRoute(routeNames.profileMenu),
    },
    {
      key: 'schedulesMeetings',
      rootRoute: [
        routeNames.schedulesMeetings,
        routeNames.schedulesEvents,
        routeNames.schedulesReminders,
        routeNames.schedulesTasks,
      ],
      name: 'schedulesMeetings',
      icon: 'calender-star',
      link: routeNames.schedulesMeetings,
      onClick: scrollToTopFeedList,
      replace: !isActiveRoute(routeNames.schedulesCalendar),
    },
    {
      key: 'createSchedule',
      rootRoute: routeNames.createSchedule,
      onClick: onCreateScheduleClick,
      name: 'createSchedule',
      icon: 'circle-plus',
      replace: false,
    },
    {
      key: 'availabilities',
      rootRoute: routeNames.schedulesAvailability.main,
      link: routeNames.schedulesAvailability.main,
      name: 'availabilities',
      icon: 'calendar-timer',
      onClick: scrollToTopFeedList,
      replace: !isActiveRoute(routeNames.schedulesCalendar),
    },
    AVATAR,
  ];

  const landList = [
    {
      key: 'home',
      rootRoute: mainRoutes.home,
      name: 'Home',
      icon: 'house',
      link: routeNames.home,
      onClick: () => {
        scrollToTopFeedList();
        if (pathname === routeNames.home) {
          queryClient.resetQueries([QueryKeys.homeFeedList]);
        }
      },
    },
    {
      key: 'people',
      rootRoute: mainRoutes.people,
      name: 'People',
      icon: 'circle-user',
      link: routeNames.peopleDiscover,
      onClick: () => {
        scrollToTopFeedList();
        appDispatch({
          type: 'PARENT_PATHNAME',
          payload: location.pathname,
        });
      },
    },
    {
      key: 'create',
      rootRoute: mainRoutes.create,
      name: 'Create',
      icon: 'circle-plus',
      onClick: () => {
        toggleGlobalEntityModal();
      },
    },
    {
      key: 'jobs',
      rootRoute: mainRoutes.jobs,
      name: 'Jobs',
      icon: 'briefcase-blank',
      link: isBusinessApp
        ? routeNames.searchRecruiterJobs
        : routeNames.discoverJobs,
      onClick: () => {
        scrollToTopFeedList();
        appDispatch({
          type: 'PARENT_PATHNAME',
          payload: location.pathname,
        });
      },
    },
    AVATAR,
  ];
  const list = isScheduleRoute ? schedulesList : landList;

  return (
    <Flex className={classes.bottomPanelRoot}>
      {list.map(
        ({
          key,
          icon,
          link,
          image,
          rootRoute,
          visibleIndicator,
          onClick,
          className,
          replace,
        }: any) => {
          const isActive = isActiveRoute(rootRoute || link);

          return (
            <BottomBarItem
              key={key}
              onClick={onClick}
              active={isActive}
              visibleIndicator={visibleIndicator}
              className={className}
              to={link}
              replace={replace}
              icon={
                image || (
                  <IconButton
                    noHover
                    name={isActive ? icon : `${icon}-light`}
                    size="md24"
                    type={isActive ? 'fas' : 'far'}
                    colorSchema={
                      isActive ? 'tertiary-transparent' : 'secondary2'
                    }
                  />
                )
              }
            />
          );
        }
      )}
    </Flex>
  );
};

export default BottomBar;
