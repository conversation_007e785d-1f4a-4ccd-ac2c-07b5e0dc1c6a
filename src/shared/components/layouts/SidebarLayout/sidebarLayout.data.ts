import { routeNames } from 'shared/utils/constants/routeNames';
import type { AppPortal } from 'shared/utils/getAppEnv';
import { appPortalsObject } from 'shared/utils/getAppEnv';

const userSettings = [
  {
    title: 'profile_details',
    subTitle: 'name_username_l',
    icon: 'id-card',
    routeName: routeNames.settingsProfileDetail,
    searchKeys: 'location job birthday gender',
  },
  {
    title: 'account',
    subTitle: 'account_type_delete',
    icon: 'user-circle',
    routeName: routeNames.settingsAccount,
    searchKeys: 'account,Private account, Take a break, Delete your account',
  },
  {
    title: 'security_login',
    subTitle: 'password_l_i_d',
    icon: 'shield-check',
    routeName: routeNames.settingsSecurity,
    searchKeys:
      'change password, devices, Logged in devices Logged in devices windows, mac, android, iphone ' +
      'Log out all',
  },
  {
    title: 'contact_information',
    subTitle: 'email_tel_link',
    icon: 'address-book',
    routeName: routeNames.settingsContactInfo,
    searchKeys: 'phone number,Email addresses Add another email address links',
  },
  {
    title: 'career_pref',
    subTitle: 'work_st_citi',
    icon: 'briefcase',
    routeName: routeNames.settingsCareerPref,
    searchKeys:
      'Open for job opportunities Career preferences Work status in the current living country Country of citizenship Who can download your resume?',
  },
  {
    title: 'preferences',
    subTitle: 'sub_preferences',
    icon: 'user-cog',
    routeName: routeNames.settingsPreferences,
    searchKeys: 'Preferences',
  },
  {
    title: 'privacy',
    subTitle: 'visi_block_user',
    icon: 'eye-slash',
    routeName: routeNames.settingsPrivacy,
    searchKeys:
      'Who can download your resume? Review all the posts that you are tagged and metioned in blocked users Edit visible profile information for unregistered visitors Chat status',
  },
  {
    title: 'lang_local',
    subTitle: 'sys_lang_time',
    icon: 'language',
    routeName: routeNames.settingsLocalization,
    searchKeys: 'Language, Date format, 24h time format',
  },
  {
    title: 'feed_pref',
    subTitle: 'content_mention_tag',
    icon: 'newspaper',
    routeName: routeNames.settingsFeedPref,
    searchKeys:
      'Feed preferences, Show content that I follow, in which people and pages are tagged.,Show content that I follow, in which people and pages are commented. Show content that I follow, in which people and pages are commented.,Allow others to share your posts.,Allow others to tag or mention you on their posts.,Hidden contents',
  },
  /* {
    title: 'synchronization',
    subTitle: 'linked_acc_cal',
    icon: 'sync',
    routeName: routeNames.settingsSync,
    searchKeys:
      'Synchronization, Linked accounts, facebook, Twitter,Instagram,Yahoo,Link another account',
  }, */
  {
    title: 'notifications',
    subTitle: 'select_t_k_of_notif',
    icon: 'bell-on',
    routeName: routeNames.settingsNotification.main,
    searchKeys:
      'Notification settings, New reactions, comments, replies, shares, tags and mentions,Upcoming meetings and birthdays,Reminders,From followings,New posts, check-ins and highlights,People, New followers, peoples from your workplace or your school... Suggestions,Suggestions to follow, popular hashtags and new jobs Schedule New meetings, meeting updates and cancellations Recommendation New recommendations and requests  Pages you manage  New followers, reactions, comments, shares and updates Pages you follow Published new highlights and jobs',
  },
  {
    title: 'help_support',
    subTitle: 'faq_privacy_policy',
    icon: 'question-circle',
  },
  {
    title: 'support',
    subTitle: 'contact_us',
    icon: 'comment-alt-edit',
  },
  {
    title: 'dar_mode',
    subTitle: 'turn_on_l',
    subTitleCallback: (isDark: boolean): string =>
      isDark ? 'turn_on_l' : 'turn_off_l',
    icon: 'lightbulb',
  },
];

const pageSettings = (portal: AppPortal) =>
  [
    {
      title: 'page_details',
      subTitle: 'page_name_username_l',
      icon: 'id-card',
      routeName: routeNames.settingsPageDetail,
      searchKeys:
        'name ,username category industry description Establisment date  Company size',
    },
    {
      title: 'account',
      subTitle: 'page_account_type_delete',
      icon: 'user-circle',
      routeName: routeNames.settingsPageAccount,
      searchKeys: 'Published page Adult content  Delete your page',
    },
    {
      title: 'accessibility',
      subTitle: 'mgn_owner_page_role',
      icon: 'user-cog',
      routeName: routeNames.settingsPageRoles,
    },
    {
      title: 'contact_information',
      subTitle: 'page_email_tel_link',
      icon: 'address-book',
      routeName: routeNames.settingsPageContactInfo,
      searchKeys: 'Email address Phone number  Links  Locations Add location',
    },
    {
      title: 'privacy',
      subTitle: 'page_visi_block_user',
      icon: 'eye-slash',
      routeName: routeNames.settingsPagePrivacy,
      searchKeys:
        'Review all the posts that you are tagged and metioned in Review all blocked users',
    },
    [appPortalsObject.user, appPortalsObject.editor].includes(portal) && {
      title: 'feed_pref',
      subTitle: 'page_content_mention_tag',
      icon: 'newspaper',
      routeName: routeNames.settingsFeedPref,
      searchKeys:
        'Feed preferences Show content  mention you on their posts. Hidden contents',
    },
    /* {
    title: 'subscriptions_pay',
    subTitle: 'page_membership_pay_meth',
    icon: 'sync',
    routeName: routeNames.settingsSubscription,
    searchKeys: 'Subscriptions & payments Subscribed products Payment methods ',
  }, */
    {
      key: 'notifications',
      title: 'notifications',
      subTitle: 'page_notification_settings',
      icon: 'bell-on',
      routeName: routeNames.settingsNotification.main,
      searchKeys:
        'New reactions, comments, replies, shares, tags mentions,Upcoming meetings  birthdays,Reminders,From followings,New posts, check-ins and highlights,People, followers, workplace school Suggestions hashtags Schedule meetings cancellations Recommendation requests',
    },
    {
      key: 'templates',
      title: 'templates',
      subTitle: '-',
      icon: 'file',
      routeName: routeNames.settingsTemplates,
      searchKeys:
        'template, templates, text, texts, message, messages, email, emails, rejection, rejections',
    },
    {
      key: 'help_support',
      title: 'help_support',
      subTitle: 'page_help_support',
      icon: 'question-circle',
    },
    {
      key: 'feedback',
      title: 'support',
      subTitle: 'contact_us',
      icon: 'comment-alt-edit',
    },
  ].filter(Boolean);

const userSettingsPreferences = [
  {
    title: 'home_preferences',
    subtitle: 'preferences_subtitle_description',
  },
  {
    title: 'people_preferences',
    subtitle: 'preferences_subtitle_description',
  },
  {
    title: 'pages_preferences',
    subtitle: 'preferences_subtitle_description',
  },
  {
    title: 'schedule_preferences',
    subtitle: 'schedule_preferences_subtitle_description',
    routeName: routeNames.settingsSchedulePref,
  },
  {
    title: 'message_preferences',
    subtitle: 'preferences_subtitle_description',
  },
  {
    title: 'jobs_preferences',
    subtitle: 'preferences_subtitle_description',
  },
];

const userSettingsSchedulePreferences = [
  { title: 'general', subtitle: 'default_time_formats' },
  {
    title: 'calendar',
    subtitle: 'view_integrations',
    routeName: routeNames.settingsScheduleCalendar,
  },
  {
    title: 'meeting_tools',
    subtitle: 'integration_subtitle',
    routeName: routeNames.settingsScheduleMeeting,
  },
  {
    title: 'availability',
    subtitle: 'set_availability',
    routeName: routeNames.settingsScheduleAvailability,
  },
  { title: 'tasks', subtitle: '' },
  { title: 'events', subtitle: 'birthdays_workshops' },
];
const pageSettingsTemplates = [
  {
    title: 'text_cap',
    subtitle: 'text_template_subtitle',
    routeName: routeNames.settingsTextTemplates.main,
  },
  // {
  //   title: 'questions',
  //   subtitle: 'questions_template_subtitle',
  //   routeName: routeNames.settingsQuestionTemplates,
  //   disabled: true,
  // },
  // {
  //   title: 'tests',
  //   subtitle: 'tests_template_subtitle',
  //   routeName: routeNames.settingsTestTemplates,
  //   disabled: true,
  // },
];
const pageSettingsTextTemplates = {
  email: {
    title: 'emails',
    subtitle: 'text_template_email_subtitle',
    hasDefault: false,
    routeName: routeNames.settingsTextTemplates.makeRoute('email'),
  },
  message: {
    title: 'messages',
    subtitle: 'text_template_messages_subtitle',
    hasDefault: false,
    routeName: routeNames.settingsTextTemplates.makeRoute('message'),
  },
  rejection: {
    title: 'rejection',
    subtitle: 'text_template_rejection_subtitle',
    hasDefault: true,
    routeName: routeNames.settingsTextTemplates.makeRoute('rejection'),
  },
  meeting: {
    title: 'meetings',
    subtitle: 'text_template_meetings_subtitle',
    hasDefault: true,
    routeName: routeNames.settingsTextTemplates.makeRoute('meeting'),
  },
};

export {
  pageSettings,
  userSettings,
  userSettingsPreferences,
  userSettingsSchedulePreferences,
  pageSettingsTemplates,
  pageSettingsTextTemplates,
};
