import type { SchedulesStoreState } from 'shared/types/schedules/store';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';
import { getDateEvents } from './schedulesStore.helpers';

export const selectEvents = (state: SchedulesStoreState) => state.savedEvents;

export const selectDayEvents =
  (date: string, applyFilters?: boolean) => (state: SchedulesStoreState) => {
    const filteredEvents =
      (applyFilters && state?.displayingEventTypes?.size
        ? state?.savedEvents?.filter(({ type }) =>
            state?.displayingEventTypes?.has(type)
          )
        : state.savedEvents) ?? [];

    return getDateEvents(date, filteredEvents);
  };

export const selectFilteredEvents = (state: SchedulesStoreState) =>
  state.savedEvents.filter(({ type }) =>
    state?.displayingEventTypes?.has(type)
  ) || [];

export const selectDisplayEventTypes = (state: SchedulesStoreState) =>
  state.displayingEventTypes;

export const selectViewDate = (state: SchedulesStoreState) => state.viewDate;

export const selectDisplayDate = (state: SchedulesStoreState) =>
  state.displayDate ?? state.viewDate.start;

export const selectTemporaryEvent = (state: SchedulesStoreState) =>
  state?.savedEvents?.find(
    (item) => item?.type === ScheduleEventTypes.TEMPORARY
  );
