import { Store } from '@tanstack/react-store';
import {
  type Data,
  type SavedEventType,
} from 'shared/types/schedules/schedules';
import { dayjs, Time } from '@shared/utils/Time';
import { type Dayjs } from 'dayjs';
import { ScheduleEventTypes } from '@shared/utils/constants/enums/scheduleEventTypes';

type SchedulesStoreState = {
  displayingEventTypes: Set<keyof typeof ScheduleEventTypes>;
  savedEvents: SavedEventType[];
  viewDate: Data['viewDate'];
  displayDate: Dayjs | null;
  isLoading: boolean;
};

export const schedulesIntialState: SchedulesStoreState = {
  displayingEventTypes: new Set(
    Object.keys(ScheduleEventTypes) as (keyof typeof ScheduleEventTypes)[]
  ), // was named filteredEvents
  savedEvents: [],
  viewDate: Time.getViewDateDetails(dayjs()),
  displayDate: null,
  isLoading: false, // was named isLoaded
};

export const schedulesStore = new Store(schedulesIntialState);
